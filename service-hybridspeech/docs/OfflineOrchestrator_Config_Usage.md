# OfflineOrchestrator 配置管理使用指南

## 概述

简化的配置管理系统，专为启动时配置设计，无需运行时动态更新。

## 配置参数

- `modelPath`: 模型文件路径
- `useCustomFeed`: 使用自定义数据输入（默认: true）
- `useTxtSmooth`: 启用文本平滑（默认: true）
- `useWpInRec`: 启用词边界识别（默认: true）
- `useSensitiveWdsNorm`: 启用敏感词规范化（默认: true）
- `useStreamChar`: 启用流式字符输出（默认: false）
- `useTprocess`: 启用文本后处理（默认: true）
- `usePhrase`: 启用短语识别（默认: true）

## 基本使用

### 1. 创建配置

```kotlin
// 基本配置（使用默认参数）
val config = OfflineEngineConfig.basic("/path/to/model")

// 高质量配置（启用所有优化功能）
val config = OfflineEngineConfig.highQuality("/path/to/model")

// 快速配置（禁用部分功能以提升速度）
val config = OfflineEngineConfig.fast("/path/to/model")

// 自定义配置
val config = OfflineEngineConfig(
    modelPath = "/path/to/model",
    useTxtSmooth = true,
    usePhrase = false,
    useTprocess = true
)
```

### 2. 设置配置并启动

```kotlin
val orchestrator = OfflineOrchestrator(context)

// 设置配置（只在启动时调用一次）
orchestrator.setConfig(config)

// 启动处理
orchestrator.start(pcmDataFlow)
```

## 预设配置详情

### basic() - 基本配置
使用所有默认参数，适合大多数场景。

### highQuality() - 高质量配置
```kotlin
useCustomFeed = true
useTxtSmooth = true
useWpInRec = true
useSensitiveWdsNorm = true
useStreamChar = false
useTprocess = true
usePhrase = true
```

### fast() - 快速配置
```kotlin
useCustomFeed = true
useTxtSmooth = false      // 禁用以提升速度
useWpInRec = false        // 禁用以提升速度
useSensitiveWdsNorm = false // 禁用以提升速度
useStreamChar = false
useTprocess = false       // 禁用以提升速度
usePhrase = false         // 禁用以提升速度
```

## 使用示例

### 标准使用流程
```kotlin
class MyActivity {
    private lateinit var orchestrator: OfflineOrchestrator

    fun initializeOfflineAsr() {
        orchestrator = OfflineOrchestrator(this)

        // 根据需求选择配置
        val config = when (qualityMode) {
            QualityMode.HIGH -> OfflineEngineConfig.highQuality(modelPath)
            QualityMode.FAST -> OfflineEngineConfig.fast(modelPath)
            else -> OfflineEngineConfig.basic(modelPath)
        }

        // 设置配置
        if (orchestrator.setConfig(config)) {
            // 启动处理
            orchestrator.start(pcmDataFlow)
        } else {
            Log.e(TAG, "Failed to set config")
        }
    }

    fun cleanup() {
        orchestrator.release()
    }
}
```

### 自定义配置示例
```kotlin
// 针对特定场景的自定义配置
val customConfig = OfflineEngineConfig(
    modelPath = "/sdcard/my_model",
    useTxtSmooth = true,     // 启用文本平滑
    usePhrase = true,        // 启用短语识别
    useTprocess = false,     // 禁用后处理以提升速度
    useWpInRec = false       // 禁用词边界识别
)

orchestrator.setConfig(customConfig)
```

## 注意事项

1. **一次性配置**: `setConfig()` 只在启动时调用一次，不支持运行时更新
2. **状态检查**: 如果引擎已初始化，再次调用 `setConfig()` 会直接返回 true
3. **错误处理**: 配置失败时会返回 false，应检查返回值
4. **资源管理**: 使用完毕后记得调用 `release()` 释放资源

## 日志输出

```
I/OfflineOrchestrator: Setting config: modelPath=/sdcard/offline_model
I/OfflineOrchestrator: Engine async initialization started with config: OfflineEngineConfig(modelPath=/sdcard/offline_model, ...)
I/OfflineEngine: Processing config updated: OfflineEngineConfig(...)
```
