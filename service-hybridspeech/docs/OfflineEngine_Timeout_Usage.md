# OfflineEngine 初始化超时机制使用指南

## 概述

OfflineEngine 现在支持初始化超时机制，当离线引擎初始化超过10秒时，会自动超时并通知上层应用。

## 主要特性

### 1. 自动超时检测
- 初始化超时时间：10秒
- 超时后自动转换到错误状态
- 清理缓存的PCM数据和初始化资源

### 2. 错误状态标识
- 新增 `isInitializationTimeout` 属性用于识别超时错误
- 错误码：-1001（初始化超时）
- 错误描述：包含具体的超时时间信息

### 3. 状态变化通知
- 通过 `setStatusChangeListener` 监听超时事件
- 上层应用可以根据超时状态进行相应处理

## 使用示例

### 基本使用

```kotlin
class MyOfflineEngineManager {
    private val offlineEngine = OfflineEngine()
    
    fun setupEngine() {
        // 设置状态变化监听器
        offlineEngine.setStatusChangeListener { status ->
            when {
                status.isInitializationTimeout -> {
                    Log.e(TAG, "离线引擎初始化超时")
                    handleInitializationTimeout(status)
                }
                status.isError -> {
                    Log.e(TAG, "离线引擎错误: ${status.engineState}")
                    handleEngineError(status)
                }
                status.isInitialized -> {
                    Log.i(TAG, "离线引擎初始化成功")
                    onEngineReady()
                }
            }
        }
        
        // 设置模型路径并启动异步初始化
        offlineEngine.setModelPath("/path/to/offline/model")
        val initResult = offlineEngine.initializeAsync()
        
        if (!initResult) {
            Log.e(TAG, "启动初始化失败")
            return
        }
        
        Log.i(TAG, "离线引擎初始化已启动，等待完成...")
    }
    
    private fun handleInitializationTimeout(status: OfflineEngineStatus) {
        // 处理初始化超时
        Log.w(TAG, "初始化超时，缓存数据数量: ${status.cachedDataCount}")
        
        // 可以选择重试或切换到在线模式
        retryInitialization()
    }
    
    private fun retryInitialization() {
        // 停止当前引擎
        offlineEngine.stop()
        
        // 等待一段时间后重试
        Handler(Looper.getMainLooper()).postDelayed({
            Log.i(TAG, "重试离线引擎初始化...")
            offlineEngine.initializeAsync()
        }, 2000)
    }
    
    private fun handleEngineError(status: OfflineEngineStatus) {
        // 处理其他错误
        if (status.engineState is EngineState.Error) {
            val error = status.engineState.error
            Log.e(TAG, "引擎错误: ${error.errorDesc}")
        }
    }
    
    private fun onEngineReady() {
        // 引擎准备就绪，可以开始处理音频
        Log.i(TAG, "离线引擎已准备就绪")
    }
}
```

### 在 OfflineOrchestrator 中的使用

```kotlin
class MyOfflineOrchestrator {
    private val orchestrator = OfflineOrchestrator(context)
    
    fun startOfflineProcessing() {
        // 设置状态监听
        orchestrator.setStatusChangeListener { status ->
            when {
                status.engineStatus.isInitializationTimeout -> {
                    Log.e(TAG, "离线引擎初始化超时，切换到在线模式")
                    switchToOnlineMode()
                }
                status.isError -> {
                    Log.e(TAG, "离线处理错误")
                    handleOfflineError(status)
                }
                status.isRunning && status.engineStatus.isInitialized -> {
                    Log.i(TAG, "离线处理已启动")
                    onOfflineProcessingStarted()
                }
            }
        }
        
        // 配置并启动
        orchestrator.setConfig("/path/to/model")
        orchestrator.start(pcmDataFlow)
    }
    
    private fun switchToOnlineMode() {
        // 切换到在线ASR模式
        Log.i(TAG, "由于离线引擎超时，切换到在线模式")
        // 实现在线模式切换逻辑
    }
}
```

## 状态检查

### 检查超时状态

```kotlin
fun checkEngineStatus() {
    val status = offlineEngine.getStatus()
    
    when {
        status.isInitializationTimeout -> {
            Log.w(TAG, "引擎初始化超时")
            // 处理超时情况
        }
        status.isInitializing -> {
            Log.i(TAG, "引擎正在初始化，缓存数据: ${status.cachedDataCount}")
            // 显示初始化进度
        }
        status.isInitialized -> {
            Log.i(TAG, "引擎已初始化，模型路径: ${status.modelPath}")
            // 引擎可用
        }
        status.isError -> {
            Log.e(TAG, "引擎错误状态")
            // 处理错误
        }
    }
}
```

## 配置参数

### 超时时间配置
当前超时时间固定为10秒，定义在 `OfflineEngine.INITIALIZATION_TIMEOUT_MS` 常量中。

### 错误码
- `-1001`: 初始化超时错误
- 错误描述格式: "Offline engine initialization timeout after 10000ms"

## 最佳实践

1. **及时设置监听器**: 在调用 `initializeAsync()` 之前设置状态变化监听器
2. **处理超时情况**: 实现超时重试或降级策略
3. **资源清理**: 超时后引擎会自动清理资源，无需手动处理
4. **状态检查**: 定期检查引擎状态，特别是在关键操作前
5. **日志记录**: 记录超时事件用于问题诊断

## 注意事项

- 超时机制仅适用于异步初始化 (`initializeAsync()`)
- 超时后引擎会自动清理缓存的PCM数据
- 超时状态下需要重新初始化才能使用引擎
- 建议在超时后实现降级策略（如切换到在线模式）
