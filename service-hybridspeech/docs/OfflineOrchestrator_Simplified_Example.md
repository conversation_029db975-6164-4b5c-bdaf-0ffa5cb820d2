# OfflineOrchestrator 简化设计示例

## 设计原理

**路径保存和判断逻辑**：
- `OfflineEngine` 自己保存当前的 `modelPath`
- `OfflineEngine.setConfig()` 方法内部比较新旧路径
- 如果路径不同或引擎处于 `Idle` 状态，返回 `true` 表示需要初始化
- 如果路径相同且引擎已配置，返回 `false` 表示无需初始化

## 核心流程

```
OfflineOrchestrator.setConfig(config)
    ↓
OfflineEngine.setConfig(config)
    ↓
内部比较 modelPath 是否变更
    ├─ 变更 → 返回 true (需要初始化)
    └─ 相同 → 返回 false (无需初始化)
    ↓
根据返回值决定是否调用 initializeAsync()
```

## 代码示例

### 1. 首次配置
```kotlin
val orchestrator = OfflineOrchestrator(context)
val config = OfflineEngineConfig.basic("/path/to/model1")

// 首次配置，引擎处于 Idle 状态，会初始化
orchestrator.setConfig(config)  // 内部：needsInit = true，执行初始化
```

### 2. 相同配置重复调用
```kotlin
// 再次使用相同配置
orchestrator.setConfig(config)  // 内部：needsInit = false，直接返回 true
```

### 3. 不同路径配置
```kotlin
val newConfig = OfflineEngineConfig.basic("/path/to/model2")

// 路径变更，会重新初始化
orchestrator.setConfig(newConfig)  // 内部：needsInit = true，先释放再初始化
```

## 关键代码

### OfflineEngine.setConfig()
```kotlin
fun setConfig(config: OfflineEngineConfig): Boolean {
    // 检查是否需要重新初始化（主要看模型路径）
    val needsInit = modelPath != config.modelPath || _currentState.value is EngineState.Idle
    
    if (needsInit) {
        Log.i(TAG, "Model path changed from '$modelPath' to '${config.modelPath}' or engine is idle, will reinitialize")
        
        // 如果引擎正在运行，先释放
        if (_currentState.value !is EngineState.Idle) {
            Log.i(TAG, "Releasing current engine for reinitialization")
            release()
        }
    }
    
    // 更新所有配置
    this.modelPath = config.modelPath
    this.useCustomFeed = config.useCustomFeed
    // ... 其他配置参数
    
    return needsInit  // 返回是否需要初始化
}
```

### OfflineOrchestrator.setConfig()
```kotlin
fun setConfig(config: OfflineEngineConfig): Boolean {
    if (initializationInProgress) {
        return false
    }

    // 让引擎自己判断是否需要重新初始化
    val needsInit = offlineEngine.setConfig(config)
    
    if (needsInit) {
        val initStarted = offlineEngine.initializeAsync()
        if (initStarted) {
            initializationInProgress = true
        }
        return initStarted
    } else {
        return true  // 配置相同，无需初始化
    }
}
```

## 优势

1. **职责清晰**：`OfflineEngine` 负责保存路径和判断逻辑
2. **代码简洁**：`OfflineOrchestrator` 只需要调用引擎方法
3. **逻辑集中**：所有配置相关的判断都在引擎内部
4. **易于维护**：路径比较逻辑只在一个地方

## 使用场景

### 应用启动
```kotlin
class MainActivity {
    private lateinit var orchestrator: OfflineOrchestrator
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        orchestrator = OfflineOrchestrator(this)
        
        // 启动时配置一次
        val config = OfflineEngineConfig.basic("/sdcard/offline_model")
        if (orchestrator.setConfig(config)) {
            orchestrator.start(pcmDataFlow)
        }
    }
}
```

### 模型切换（如果需要）
```kotlin
fun switchModel(newModelPath: String) {
    val newConfig = OfflineEngineConfig.basic(newModelPath)
    
    // 引擎会自动判断是否需要重新初始化
    if (orchestrator.setConfig(newConfig)) {
        Log.i(TAG, "Model switched successfully")
    } else {
        Log.e(TAG, "Failed to switch model")
    }
}
```

## 日志输出示例

```
// 首次配置
I/OfflineEngine: Model path changed from '' to '/path/to/model1' or engine is idle, will reinitialize
I/OfflineEngine: Config updated: OfflineEngineConfig(modelPath=/path/to/model1, ...)
I/OfflineOrchestrator: Engine initialization started

// 相同配置重复调用
I/OfflineEngine: Config updated: OfflineEngineConfig(modelPath=/path/to/model1, ...)
I/OfflineOrchestrator: Engine already configured with same settings

// 路径变更
I/OfflineEngine: Model path changed from '/path/to/model1' to '/path/to/model2' or engine is idle, will reinitialize
I/OfflineEngine: Releasing current engine for reinitialization
I/OfflineEngine: Config updated: OfflineEngineConfig(modelPath=/path/to/model2, ...)
I/OfflineOrchestrator: Engine initialization started
```

这种设计将复杂的判断逻辑封装在 `OfflineEngine` 内部，使得 `OfflineOrchestrator` 的代码更加简洁，同时保持了功能的完整性。
