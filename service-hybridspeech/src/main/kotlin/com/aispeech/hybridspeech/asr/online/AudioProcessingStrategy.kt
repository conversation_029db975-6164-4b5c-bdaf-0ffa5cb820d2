package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.AsrResumeConfig
import com.aispeech.hybridspeech.Mp3EncodingConfig
import com.aispeech.hybridspeech.OpusEncodingConfig
import com.aispeech.hybridspeech.storage.RecordingStorageManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelAndJoin
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlin.coroutines.cancellation.CancellationException

/**
 * 定义处理模式的状态机
 */
private sealed class ProcessingMode {
  /** 初始化模式 */
  data object Init : ProcessingMode()
  /** 实时处理模式 */
  data object Realtime : ProcessingMode()
  /** 暂停模式 */
  data object Paused : ProcessingMode()
  /** 续传处理模式，包含续传所需的数据流 */
  data class Resuming(val dataFlow: Flow<ByteArray>) : ProcessingMode()
}

/**
 * 音频处理配置基类
 * 统一不同音频格式的配置接口
 */
sealed class AudioProcessingConfig {
  /**
   * Opus音频处理配置
   */
  data class OpusConfig(
    val opusEncodingConfig: OpusEncodingConfig
  ) : AudioProcessingConfig()

  /**
   * MP3音频处理配置
   */
  data class Mp3Config(
    val mp3EncodingConfig: Mp3EncodingConfig
  ) : AudioProcessingConfig()
}

/**
 * 音频类型枚举
 */
enum class AudioType(val value: String) {
  OGG_OPUS("ogg_opus"),
  MP3("mp3");

  companion object {
    fun fromString(value: String): AudioType {
      return when (value.lowercase()) {
        "ogg_opus" -> OGG_OPUS
        "mp3" -> MP3
        else -> OGG_OPUS // 默认使用 OGG_OPUS
      }
    }
  }
}

/**
 * 音频处理策略接口
 * 定义统一的音频处理操作，屏蔽底层实现差异
 */
interface AudioProcessingStrategy {

  /**
   * 启动音频处理
   * @param dataFlow 音频数据流（PCM或MP3）
   * @param config 音频处理配置
   * @param asrSocketClient ASR Socket客户端
   * @param scope 协程作用域
   * @return 是否启动成功
   */
  fun startProcessing(
    dataFlow: SharedFlow<ByteArray>,
    config: AudioProcessingConfig,
    asrSocketClient: IAsrSocketClient,
    readyStateFlow: SharedFlow<OnlineAsrReadyState>,
    scope: CoroutineScope
  ): Boolean

  /**
   * 停止音频处理
   */
  suspend fun stopProcessing()

  /**
   * 重置处理器状态
   */
  fun reset()

  /**
   * 启动续传处理
   * @param resumeConfig 续传配置
   * @param storageManager 存储管理器
   * @param asrSocketClient ASR Socket客户端
   * @param scope 协程作用域
   * @param onResumeCompleted 续传完成回调
   */
  fun startResumeTransmission(
    resumeConfig: AsrResumeConfig,
    storageManager: RecordingStorageManager,
    asrSocketClient: IAsrSocketClient,
    scope: CoroutineScope,
    onResumeCompleted: () -> Unit
  )

  /**
   * 暂停流的处理
   */
  fun pauseProcessing()

  /**
   * 继续流处理
   */
  fun resumeProcessing()

  /**
   * 停止续传处理
   */
  suspend fun stopResumeTransmission()

  /**
   * 检查是否支持续传
   * @param resumeConfig 续传配置
   * @return 是否支持续传
   */
  fun supportsResume(resumeConfig: AsrResumeConfig): Boolean

  /**
   * 获取处理器状态信息
   */
  fun getStatus(): AudioProcessingStatus

  /**
   * 关闭处理器
   */
  fun shutdown(reason: String)
}

/**
 * 音频处理状态
 */
data class AudioProcessingStatus(
  val isProcessing: Boolean,
  val isResuming: Boolean,
  val isPausedRealTime: Boolean,
  val processingJobActive: Boolean,
  val resumeJobActive: Boolean,
  val additionalInfo: Map<String, Any> = emptyMap()
)

/**
 * 音频处理策略工厂
 * 负责根据音频类型创建相应的策略实例
 */
object AudioProcessingStrategyFactory {

  /**
   * 创建音频处理策略
   * @param audioType 音频类型
   * @return 对应的策略实例
   */
  fun createStrategy(audioType: AudioType): AudioProcessingStrategy {
    return when (audioType) {
      AudioType.OGG_OPUS -> OpusProcessingStrategy()
      AudioType.MP3 -> Mp3ProcessingStrategy()
    }
  }

  /**
   * 创建默认配置
   * @param audioType 音频类型
   * @return 对应的默认配置
   */
  fun createDefaultConfig(audioType: AudioType): AudioProcessingConfig {
    return when (audioType) {
      AudioType.OGG_OPUS -> AudioProcessingConfig.OpusConfig(
        OpusEncodingConfig.createDefault()
      )
      AudioType.MP3 -> AudioProcessingConfig.Mp3Config(
        Mp3EncodingConfig.createDefault()
      )
    }
  }
}

/**
 * Opus音频处理策略
 * 处理PCM -> Opus编码 -> OGG数据流
 */
class OpusProcessingStrategy : AudioProcessingStrategy {
  companion object {
    private const val TAG = "OpusProcessingStrategy"
    private const val FORWARD_RETRY_DELAY_MS = 200L
  }

  private val opusEncoder = OpusEncoder()
  private val oggCache = OggAsrStreamCache()

  private val processingMode = MutableStateFlow<ProcessingMode>(ProcessingMode.Init)
  private var processingJobs: Job? = null

  @Volatile
  private var isProcessing = false

  @Volatile
  private var currentReadyState = OnlineAsrReadyState.NOT_CONNECTED

  /**
   * 根据模式返回处理中的流
   */
  private fun mapProcessingMode(
    mode: ProcessingMode,
    dataFlow: SharedFlow<ByteArray>
  ): Flow<ByteArray> {
    return when (mode) {
      is ProcessingMode.Init -> {
        AILog.i(TAG, "Switching to INIT processing mode. No data will be processed.")
        emptyFlow()
      }
      ProcessingMode.Realtime -> {
        AILog.i(TAG, "Switching to REALTIME processing mode.")
        dataFlow
      }
      is ProcessingMode.Paused -> {
        AILog.i(TAG, "Switching to PAUSED processing mode. No data will be processed.")
        emptyFlow()
      }
      is ProcessingMode.Resuming -> {
        AILog.i(TAG, "Switching to RESUMING processing mode.")
        mode.dataFlow
      }
    }
  }


  override fun startProcessing(
    dataFlow: SharedFlow<ByteArray>,
    config: AudioProcessingConfig,
    asrSocketClient: IAsrSocketClient,
    readyStateFlow: SharedFlow<OnlineAsrReadyState>,
    scope: CoroutineScope
  ): Boolean {
    return try {
      if (isProcessing) {
        AILog.w(TAG, "Already processing")
        return true
      }

      // 获取Opus配置
      val opusConfig = when (config) {
        is AudioProcessingConfig.OpusConfig -> config.opusEncodingConfig
        else -> {
          AILog.e(TAG, "Invalid config type for Opus strategy: ${config::class.simpleName}")
          return false
        }
      }

      // 启动Opus编码器（使用配置）
      if (!opusEncoder.startEncodingWithConfig(opusConfig)) {
        AILog.e(TAG, "Failed to start Opus encoder with config")
        return false
      }

      isProcessing = true

      processingJobs?.cancel()
      processingJobs = scope.launch {
        // PCM -> Opus Encoder
        launch {
          //这里要注意：不能先处理实时流再处理续传流，所以结束或者重置以后默认需要改成: ProcessingMode.Init
          processingMode.flatMapLatest { mode ->
            mapProcessingMode(mode, dataFlow)
          }.collect { pcmData ->
            // 统一流
            opusEncoder.encodePcmData(pcmData)
          }
        }

        // Opus -> cache
        launch {
          opusEncoder.oggDataFlow.collect { oggData ->
            oggCache.writeOggData(oggData)
          }
        }

        // socket state
        launch {
          readyStateFlow.collect { state -> currentReadyState = state }
        }

        // OGG Cache -> Socket
        launch {
          try {
            while (isActive) {
              if (asrSocketClient.getCurrentState() == ConnectionStatus.CONNECTED
                && currentReadyState == OnlineAsrReadyState.READY) {
                val batch = oggCache.readOggDataBatch()
                for (data in batch) {
                  if (!asrSocketClient.sendAudioData(data)) {
                    AILog.w(TAG, "Failed to send data, connection might be lost. Pausing forwarding.")
                    break
                  }
                }
              } else {
                delay(FORWARD_RETRY_DELAY_MS)
              }
            }
          } catch (e: Exception) {
            AILog.i(TAG, "Forwarding job stopped: ${e.message}")
          }
        }
      }

      AILog.i(TAG, "Opus processing started successfully with config")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Error starting Opus processing", e)
      false
    }
  }

  override fun pauseProcessing() {
    AILog.i(TAG, "Pause processing to Realtime via state change.")
    processingMode.value = ProcessingMode.Paused
    // 暂停：编码器重置(ws会重建)
    opusEncoder.stopEncoding()
  }

  override fun resumeProcessing() {
    AILog.i(TAG, "Resuming processing to Realtime via state change.")
    processingMode.value = ProcessingMode.Realtime
    // 继续：编码器启动
    opusEncoder.start()
  }


  override suspend fun stopProcessing() {
    isProcessing = false
    processingJobs?.cancelAndJoin()
    processingJobs = null

    opusEncoder.stopEncoding()
    oggCache.clear()

    processingMode.value = ProcessingMode.Init
    AILog.i(TAG, "Opus processing stopped")
  }


  override fun reset() {
    opusEncoder.reset()
    oggCache.clear()
    processingMode.value = ProcessingMode.Init
    AILog.i(TAG, "Opus processing reset")
  }

  override fun startResumeTransmission(
    resumeConfig: AsrResumeConfig,
    storageManager: RecordingStorageManager,
    asrSocketClient: IAsrSocketClient,
    scope: CoroutineScope,
    onResumeCompleted: () -> Unit
  ) {
    AILog.i(TAG, "Starting Opus resume from ${resumeConfig.resumeFromOffset}ms")

    try {
      // 续传：编码器启动
      opusEncoder.start()
      // 这里不能清理ogg缓存, 因为会清理掉第一个start消息帧
      // oggCache.clear()

      // 获取当前PCM文件路径
      val currentPcmFilePath = storageManager.getCurrentPcmFilePath()
      if (currentPcmFilePath == null) {
        AILog.i(TAG, "No PCM file for resume, aborting.")
        processingMode.value = ProcessingMode.Realtime
        onResumeCompleted.invoke()
        return
      }

      // 创建从指定偏移量开始的PCM数据流
      val resumePcmFlow = storageManager.createPcmDataStreamFromOffset(
        currentPcmFilePath,
        resumeConfig.resumeFromOffset,
      )

      if (resumePcmFlow == null) {
        AILog.e(TAG, "Failed to create resume PCM flow")
        processingMode.value = ProcessingMode.Realtime
        onResumeCompleted.invoke()
        return
      }

      // 切换状态，flatMapLatest将自动处理流的切换
      processingMode.value = ProcessingMode.Resuming(
        resumePcmFlow.onCompletion { cause ->
          if (cause != null && cause !is CancellationException) {
            AILog.e(TAG, "Resume flow completed with an error.", cause)
          } else {
            AILog.i(TAG, "Opus resume completed, switching back to realtime.")
          }
          // 续传流结束后（无论正常结束还是异常），自动切回实时模式
          processingMode.value = ProcessingMode.Realtime
          // 调用上层回调
          onResumeCompleted.invoke()
        }
      )

    } catch (e: Exception) {
      AILog.e(TAG, "Failed to start Opus resume transmission", e)
      processingMode.value = ProcessingMode.Realtime
      onResumeCompleted.invoke()
    }
  }

  override suspend fun stopResumeTransmission() {
    AILog.i(TAG, "Opus resume transmission stopped")
    if (processingMode.value is ProcessingMode.Resuming) {
      processingMode.value = ProcessingMode.Init
      AILog.i(TAG, "Stopping resume mode, switching back to Init.")
    }
  }

  override fun supportsResume(resumeConfig: AsrResumeConfig): Boolean {
    // Opus模式支持基于时间偏移的续传
    return resumeConfig.resumeFromOffset >= 0
  }

  override fun getStatus(): AudioProcessingStatus {
    val currentMode = processingMode.value
    return AudioProcessingStatus(
      isProcessing = isProcessing,
      isResuming = currentMode is ProcessingMode.Resuming,
      isPausedRealTime = false,
      processingJobActive = processingJobs?.isActive == true,
      resumeJobActive = currentMode is ProcessingMode.Resuming && processingJobs?.isActive == true,
    )
  }

  override fun shutdown(reason: String) {
    opusEncoder.shutdown("$reason - OpusProcessingStrategy")
    AILog.i(TAG, "Opus processing strategy shut down: $reason")
  }
}

/**
 * MP3音频处理策略
 * 直接处理MP3数据流，支持chunk跟踪和续传
 */
class Mp3ProcessingStrategy : AudioProcessingStrategy {
  companion object {
    private const val TAG = "Mp3ProcessingStrategy"
  }

  private val mp3ChunkTracker = Mp3ChunkTracker()

  private val processingMode = MutableStateFlow<ProcessingMode>(ProcessingMode.Init)

  // 所有处理任务的父Job
  private var processingJobs: Job? = null

  @Volatile
  private var isProcessing = false

  override fun startProcessing(
    dataFlow: SharedFlow<ByteArray>,
    config: AudioProcessingConfig,
    asrSocketClient: IAsrSocketClient,
    readyStateFlow: SharedFlow<OnlineAsrReadyState>,
    scope: CoroutineScope
  ): Boolean {
    return try {
      if (isProcessing) {
        AILog.w(TAG, "Already processing")
        return true
      }

      // 获取MP3配置（用于日志记录）
      val mp3Config = when (config) {
        is AudioProcessingConfig.Mp3Config -> config.mp3EncodingConfig
        else -> {
          AILog.e(TAG, "Invalid config type for MP3 strategy: ${config::class.simpleName}")
          return false
        }
      }

      AILog.i(TAG, "Starting MP3 processing with config: bitRate=${mp3Config.bitRate}, quality=${mp3Config.quality}")

      isProcessing = true

      // 重置 MP3 chunk 跟踪器
      mp3ChunkTracker.reset()

      // 处理 mp3 数据流
      processingJobs = scope.launch {
        // 主数据流管道
        processingMode.flatMapLatest { mode ->
          when (mode) {
            is ProcessingMode.Realtime -> {
              AILog.i(TAG, "Switching to REALTIME processing mode.")
              dataFlow
            }
            is ProcessingMode.Resuming -> {
              AILog.i(TAG, "Switching to RESUMING processing mode.")
              mode.dataFlow
            }
            is ProcessingMode.Paused -> {
              AILog.i(TAG, "Switching to PAUSED processing mode.")
              emptyFlow()
            }
            is ProcessingMode.Init -> {
              AILog.i(TAG, "Switching to INIT processing mode.")
              emptyFlow()
            }
          }
        }
          .combine(readyStateFlow.map { it == OnlineAsrReadyState.READY }.distinctUntilChanged()) { mp3Data, isReady ->
            mp3Data to isReady
          }
          .filter { (_, isReady) -> isReady }
          .collect { (mp3Data, _) ->
            try {
              if (processingMode.value is ProcessingMode.Realtime) {
                mp3ChunkTracker.recordChunk(mp3Data)
              }
              asrSocketClient.sendAudioData(mp3Data)
            } catch (e: Exception) {
              AILog.e(TAG, "Error processing MP3 data", e)
            }
          }
      }

      AILog.i(TAG, "MP3 processing started successfully")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Error starting MP3 processing", e)
      false
    }
  }


  override suspend fun stopProcessing() {
    isProcessing = false
    processingJobs?.cancelAndJoin()
    processingJobs = null

    mp3ChunkTracker.reset()
    processingMode.value = ProcessingMode.Init
    AILog.i(TAG, "MP3 processing stopped")
  }


  override fun reset() {
    mp3ChunkTracker.reset()
    processingMode.value = ProcessingMode.Init // 状态复位
    AILog.i(TAG, "MP3 processing reset")
  }

  override fun startResumeTransmission(
    resumeConfig: AsrResumeConfig,
    storageManager: RecordingStorageManager,
    asrSocketClient: IAsrSocketClient,
    scope: CoroutineScope,
    onResumeCompleted: () -> Unit
  ) {
    val chunkIndex = resumeConfig.resumeFromMp3ChunkIndex ?: run {
      AILog.w(TAG, "MP3 resume aborted: chunk index is null.")
      processingMode.value = ProcessingMode.Realtime
      onResumeCompleted.invoke()
      return
    }

    AILog.i(TAG, "Starting MP3 resume from chunk index: $chunkIndex")

    val currentMp3FilePath = storageManager.getCurrentMp3FilePath()
    if (currentMp3FilePath == null) {
      AILog.w(TAG, "No MP3 file for resume, aborting.")
      processingMode.value = ProcessingMode.Realtime
      onResumeCompleted.invoke()
      return
    }

    // 注意：chunkSizes 应该从 tracker 获取，因为实时录制时已经记录了
    val chunkSizes = mp3ChunkTracker.getChunkSizesFromIndex(0)
    if (chunkSizes.isEmpty()) {
      AILog.w(TAG, "No chunk size information for resume, aborting.")
      processingMode.value = ProcessingMode.Realtime
      onResumeCompleted.invoke()
      return
    }

    // 未上传服务的 mp3 flow
    val resumeMp3Flow = storageManager.createMp3DataStreamFromChunkIndex(
      currentMp3FilePath,
      chunkIndex,
      chunkSizes,
      1024
    )

    if (resumeMp3Flow == null) {
      AILog.e(TAG, "Failed to create resume MP3 flow, aborting.")
      processingMode.value = ProcessingMode.Realtime
      onResumeCompleted.invoke()
      return
    }

    // 切换状态，flatMapLatest将自动处理流的切换
    processingMode.value = ProcessingMode.Resuming(
      resumeMp3Flow.onCompletion { cause ->
        if (cause != null && cause !is CancellationException) {
          AILog.e(TAG, "Resume flow completed with an error.", cause)
        } else {
          AILog.i(TAG, "MP3 resume completed, switching back to realtime.")
        }
        // 续传流结束后，自动切回实时模式
        processingMode.value = ProcessingMode.Realtime
        onResumeCompleted.invoke()
      }
    )
  }

  override suspend fun stopResumeTransmission() {
    AILog.i(TAG, "MP3 resume transmission stop requested.")
    if (processingMode.value is ProcessingMode.Resuming) {
      AILog.i(TAG, "Stopping resume mode, switching back to Init.")
      processingMode.value = ProcessingMode.Init
    }
  }

  override fun supportsResume(resumeConfig: AsrResumeConfig): Boolean {
    return resumeConfig.resumeFromMp3ChunkIndex != null && resumeConfig.resumeFromMp3ChunkIndex!! >= 0
  }

  override fun getStatus(): AudioProcessingStatus {
    val currentMode = processingMode.value
    return AudioProcessingStatus(
      isProcessing = isProcessing,
      isResuming = currentMode is ProcessingMode.Resuming,
      isPausedRealTime = currentMode is ProcessingMode.Paused,
      processingJobActive = processingJobs?.isActive == true,
      resumeJobActive = currentMode is ProcessingMode.Resuming && processingJobs?.isActive == true,
    )
  }

  override fun pauseProcessing() {
    AILog.i(TAG, "Pausing processing via state change to PAUSED.")
    processingMode.value = ProcessingMode.Paused
  }

  override fun resumeProcessing() {
    AILog.i(TAG, "Resuming processing to Realtime via state change.")
    processingMode.value = ProcessingMode.Realtime
  }

  /**
   * 获取 MP3 chunk 跟踪状态
   */
  fun getMp3ChunkTrackerStatus(): Mp3ChunkTrackerStatus {
    return mp3ChunkTracker.getStatus()
  }

  override fun shutdown(reason: String) {
    // MP3处理策略没有需要关闭的资源
    AILog.i(TAG, "MP3 processing strategy shut down: $reason")
  }
}
