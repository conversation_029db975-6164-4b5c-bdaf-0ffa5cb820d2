package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.OpusEncodingConfig
import com.aispeech.hybridspeech.audio.opus.OggOpusEncoder
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * Opus编码器
 * 将PCM数据编码为OGG Opus格式，用于在线ASR传输
 * 使用 lib-duilite SDK 的真实 Opus 编码能力
 */
class OpusEncoder: CoroutineScope {
  companion object {
    private const val TAG = "OpusEncoder"
    private const val SAMPLE_RATE = 16000
    private const val CHANNELS = 1
  }

  enum class State {
    IDLE,
    INIT,
    START,
    STOP,
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val _oggDataFlow = MutableSharedFlow<ByteArray>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val oggDataFlow: SharedFlow<ByteArray> = _oggDataFlow.asSharedFlow()

//  private var isEncoding = false
//  private var encodingJob: Job? = null
//  private val pcmQueue = ConcurrentLinkedQueue<ByteArray>()
//  private val pcmBuffer = ByteArrayOutputStream()

  // 使用 SDK 的真实 Opus 编码器
  private var encoder: OggOpusEncoder? = null
  private var currentConfig: OpusEncodingConfig? = null

  private var mState: State? = State.IDLE

  fun isInit() = encoder != null
  fun isStart() = encoder != null && mState == State.START

  /**
   * 开始编码（支持配置）
   */
  fun startEncodingWithConfig(config: OpusEncodingConfig): Boolean {
    try {
      if (currentConfig != config) {
        currentConfig = config
        AILog.i(TAG, "startEncodingWithConfig: $config")
        //destroy encoder if change config
        encoder?.let {
          destroy()
        }
      }

      val success = initWithEncoder(SAMPLE_RATE, CHANNELS, config.bitRate, config.complexity) { data, size ->
        if (data != null && size > 0) {
          // 将编码后的数据发送到 Flow
          _oggDataFlow.tryEmit(data.copyOf(size))
        }
      }

      if(success) {
        // 启动编码器
        start()
        AILog.i(TAG, "Opus encoding started with config: bitRate=${config.bitRate}, frameSize=${config.frameSize}")
      } else {
        AILog.w(TAG, "Failed to init Opus encoding with config, current state = $mState, $encoder")
      }
      return success
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to start Opus encoding with config", e)
    }
    return false
  }


  /**
   * 开始编码（使用默认配置）
   */
  fun startEncoding(): Boolean {
    return startEncodingWithConfig(OpusEncodingConfig.createDefault())
  }

  /**
   * 停止编码
   */
  fun stopEncoding() {
    if(mState == State.STOP || mState == State.IDLE) {
      AILog.w(TAG, "invalid stop. current state = $mState, $encoder")
      return
    }
    AILog.i(TAG, "stop. current state = $mState -> STOP, $encoder")
    mState = State.STOP
    encoder?.stop()
  }

  /**
   * 输入PCM数据进行编码
   */
  fun encodePcmData(pcmData: ByteArray) {
    if(mState != State.START) {
      AILog.w(TAG, "invalid feed. current state = $mState, len: ${pcmData.size}, $encoder")
    }
    encoder?.feed(pcmData)
  }

  /**
   * 关闭编码器协程作用域
   * 取消所有子协程，但不影响其他模块
   */
  fun shutdown(reason: String = "OpusEncoder shutdown") {
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Opus encoder scope shut down: $reason")
    destroy()
  }


  /**
   * 重置编码器（用于网络重连时）
   */
  fun reset() {
    AILog.i(TAG, "Opus encoder reset")
    stopEncoding()
  }

  /**
   * 开启编码器
   */
  fun start() {
    if(mState == State.START || mState == State.IDLE) {
      AILog.w(TAG, "invalid start. current state = $mState, $encoder")
      return
    }
    AILog.i(TAG, "start. current state = $mState -> START, $encoder")
    mState = State.START
    encoder?.start()
  }

  /**
   * 销毁编码器
   */
  fun destroy() {
    AILog.i(TAG, "destroy. current state = $mState -> IDLE, $encoder")
    encoder?.destroy()
    encoder = null
    mState = State.IDLE
  }

  private fun initWithEncoder(
    sampleRate: Int,
    channel: Int,
    bitrate: Int,
    complexity: Int,
    onEncoderBuffer: OggOpusEncoder.IOpusEncoderCallback?
  ): Boolean {
    if (encoder == null) {
      encoder = OggOpusEncoder()
    }
    if(mState == State.IDLE) {
      AILog.i(TAG, "init. current state = $mState -> INIT, $encoder")
      mState = State.INIT
      encoder?.init(sampleRate, channel, bitrate, complexity, onEncoderBuffer)
      return true
    } else {
      AILog.w(TAG, "invalid init. current state = $mState, $encoder")
    }
    return false
  }



}