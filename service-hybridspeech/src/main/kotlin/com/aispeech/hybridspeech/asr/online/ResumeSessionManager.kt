package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.AsrResumeConfig
import com.aispeech.hybridspeech.IHybridSpeechConfigProvider
import com.aispeech.hybridspeech.INetworkConfigCallback
import com.aispeech.hybridspeech.NetworkConfig
import com.aispeech.hybridspeech.NetworkConfigRequest
import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.storage.RecordingStorageManager
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

/**
 * 统一会话管理器
 * 负责管理所有会话相关的状态和网络配置获取
 * 作为唯一的 session 和网络配置管理入口
 */
class ResumeSessionManager {

  companion object {
    private const val TAG = "ResumeSessionManager"
  }

  // 续传配置相关
  private var asrResumeConfig: AsrResumeConfig? = null

  // 会话状态相关
  private var currentSessionId: String? = null
  private var audioOffsetMs: Long = 0L // 服务端已收到的音频时长（毫秒）
  private var recordingStartTime: Long = 0L // 录音开始时间

  // 录音会话信息
  private var configProvider: IHybridSpeechConfigProvider? = null
  private var currentRecordId: Long? = null
  private var currentUserId: String? = null
  private var currentAudioType: String = "ogg_opus"
  private var enableSmooth: Boolean = true

  // 存储管理器（用于续传时读取PCM文件）
  private var storageManager: RecordingStorageManager? = null

  // 续传状态管理
  @Volatile
  private var resumeState: ResumeState = ResumeState.NORMAL
  private var currentPcmFilePath: String? = null

  // 用于暂停/恢复实时PCM流处理
  @Volatile
  private var pauseRealTimeProcessing = false

  /**
   * 设置网络配置提供者和录音会话信息
   */
  fun setConfigProvider(
    provider: IHybridSpeechConfigProvider?,
    recordId: Long? = null,
    userId: String? = null,
    audioType: String? = null,
    enableSmooth: Boolean = true,
  ) {
    this.configProvider = provider
    this.currentRecordId = recordId
    this.currentUserId = userId
    this.enableSmooth = enableSmooth

    audioType?.let { this.currentAudioType = it }

    AILog.i(TAG, "Config provider ${if (provider != null) "set" else "cleared"}")
    if (provider != null) {
      AILog.i(TAG, "Recording session info: recordId=$recordId, userId=$userId, audioType=$audioType")
    }
  }

  /**
   * 设置ASR续传配置
   */
  fun setAsrResumeConfig(config: AsrResumeConfig?) {
    this.asrResumeConfig = config
    if (config != null) {
      AILog.i(TAG, "ASR resume config set: sessionId=${config.sessionId}, offset=${config.resumeFromOffset}ms")

      // 更新当前会话信息
      currentSessionId = config.sessionId
      audioOffsetMs = config.resumeFromOffset

    } else {
      AILog.i(TAG, "ASR resume config cleared")
      currentSessionId = null
      audioOffsetMs = 0L
    }
  }

  /**
   * 设置存储管理器（用于续传时读取PCM文件）
   */
  fun setStorageManager(manager: RecordingStorageManager?) {
    this.storageManager = manager
    AILog.i(TAG, "Storage manager ${if (manager != null) "set" else "cleared"}")
  }

  /**
   * 设置当前PCM文件路径
   */
  fun setCurrentPcmFilePath(path: String?) {
    this.currentPcmFilePath = path
    AILog.i(TAG, "Current PCM file path: $path")
  }

  /**
   * 设置录音开始时间
   */
  fun setRecordingStartTime(startTime: Long) {
    this.recordingStartTime = startTime
    AILog.i(TAG, "Recording start time: $startTime")
  }

  /**
   * 更新音频偏移量
   */
  fun updateAudioOffset(offsetMs: Long) {
    this.audioOffsetMs = offsetMs
    AILog.i(TAG, "Audio offset updated: ${offsetMs}ms")
  }

  /**
   * 更新会话ID
   */
  fun updateSessionId(sessionId: String?) {
    this.currentSessionId = sessionId
    AILog.i(TAG, "Session ID updated: $sessionId")
  }

  /**
   * 设置续传状态
   */
  fun setResumeState(state: ResumeState) {
    this.resumeState = state
    AILog.i(TAG, "Resume state: $state")
  }

  /**
   * 设置是否暂停实时处理
   */
  fun setPauseRealTimeProcessing(pause: Boolean) {
    this.pauseRealTimeProcessing = pause
    AILog.i(TAG, "Pause real-time processing: $pause")
  }

  /**
   * 获取初始网络配置
   * @param config 录音配置
   * @return 网络配置，失败时返回null
   */
  suspend fun fetchNetworkConfig(config: RecordingConfig): NetworkConfig? {
    val provider = configProvider ?: return null
    val asrConfig = config.onlineAsrConfig ?: return null

    val request = NetworkConfigRequest.createAsrWebSocketRequest(
      recordId = currentRecordId ?: asrConfig.recordId,
      userId = currentUserId ?: asrConfig.userId,
      language = config.language,
      audioType = currentAudioType,
      translate = config.translate,
      enableRealtimeAgenda = asrConfig.enableRealtimeAgenda,
      enableSmooth = enableSmooth
    )

    return getNetworkConfigSuspend(provider, request)
  }

  /**
   * 获取续传配置
   * @param reason 获取配置的原因（用于日志）
   * @return 续传配置，失败时返回null
   */
  suspend fun fetchResumeConfig(reason: String): AsrResumeConfig? {
    val provider = configProvider ?: return null

    AILog.i(TAG, "Fetching resume config for $reason: sessionId=$currentSessionId, audioOffsetMs=${audioOffsetMs}ms")

    val resumeRequest = NetworkConfigRequest.createSmartResumeRequest(
      recordId = currentRecordId ?: 0L,
      userId = currentUserId ?: "",
      sessionId = currentSessionId ?: "",
      resumeFromOffset = audioOffsetMs,
      audioType = currentAudioType,
      translate = null,
      enableSmooth = enableSmooth
    )

    val networkConfig = getNetworkConfigSuspend(provider, resumeRequest)
    val resumeConfig = networkConfig?.asrResumeConfig

    if (resumeConfig != null) {
      AILog.i(TAG, "Got resume config for $reason: sessionId=${resumeConfig.sessionId}")
      resumeConfig
    } else {
      AILog.i(TAG, "No resume config returned for $reason")
      null
    }
  }

  /**
   * 将回调式的网络配置获取转换为协程挂起函数
   * @param configProvider 配置提供者
   * @param request 网络配置请求
   * @return 网络配置，失败时返回null
   */
  private suspend fun getNetworkConfigSuspend(
    configProvider: IHybridSpeechConfigProvider,
    request: NetworkConfigRequest
  ): NetworkConfig? {
    return suspendCancellableCoroutine { continuation ->
      val callback = object : INetworkConfigCallback.Stub() {
        override fun onConfigReady(config: NetworkConfig) {
          AILog.i(TAG, "Received network config from provider")
          continuation.resume(config)
        }

        override fun onConfigError(errorCode: Int, errorMessage: String) {
          AILog.e(TAG, "Failed to get network config: $errorCode - $errorMessage")
          continuation.resume(null)
        }
      }

      try {
        configProvider.requestNetworkConfig(request, callback)
      } catch (e: Exception) {
        AILog.e(TAG, "Error requesting network config", e)
        continuation.resume(null)
      }
    }
  }

  /**
   * 清理所有状态
   */
  fun clear() {
    AILog.i(TAG, "Clearing all resume session state")

    asrResumeConfig = null
    currentSessionId = null
    audioOffsetMs = 0L
    recordingStartTime = 0L

    configProvider = null
    currentRecordId = null
    currentUserId = null
    currentAudioType = "ogg_opus"

    storageManager = null
    resumeState = ResumeState.NORMAL
    currentPcmFilePath = null
    pauseRealTimeProcessing = false
  }

  /**
   * 验证网络配置是否有效
   * @param networkConfig 网络配置
   * @return 是否有效
   */
  fun validateNetworkConfig(networkConfig: NetworkConfig?): Boolean {
    if (networkConfig == null) {
      AILog.e(TAG, "Network config is null")
      return false
    }

    val websocketConfig = networkConfig.websocketConfig
    if (websocketConfig == null) {
      AILog.e(TAG, "WebSocket config is null")
      return false
    }

    if (websocketConfig.signedUrl.isBlank()) {
      AILog.e(TAG, "WebSocket signed URL is blank")
      return false
    }

    AILog.i(TAG, "Network config validation passed")
    return true
  }

  /**
   * 从网络配置中提取WebSocket URL
   * @param networkConfig 网络配置
   * @return WebSocket URL，失败时返回null
   */
  fun extractWebSocketUrl(networkConfig: NetworkConfig): String? {
    return networkConfig.websocketConfig?.signedUrl
  }

  /**
   * 检查网络配置是否包含续传配置
   * @param networkConfig 网络配置
   * @return 是否包含续传配置
   */
  fun hasResumeConfig(networkConfig: NetworkConfig): Boolean {
    return networkConfig.asrResumeConfig != null
  }

  /**
   * 记录网络配置信息（用于调试）
   * @param networkConfig 网络配置
   */
  fun logNetworkConfigInfo(networkConfig: NetworkConfig) {
    val websocketConfig = networkConfig.websocketConfig
    if (websocketConfig != null) {
      AILog.i(TAG, "WebSocket URL configured: ${websocketConfig.signedUrl.take(100)}...")
    }

    val asrResumeConfig = networkConfig.asrResumeConfig
    if (asrResumeConfig != null) {
      AILog.i(TAG, "ASR resume config available: resumeFromOffset=${asrResumeConfig.resumeFromOffset}")
    } else {
      AILog.w(TAG, "No ASR resume config found in network config")
    }
  }

  // Getters
  fun getAsrResumeConfig(): AsrResumeConfig? = asrResumeConfig
  fun getCurrentSessionId(): String? = currentSessionId
  fun getAudioOffsetMs(): Long = audioOffsetMs
  fun getRecordingStartTime(): Long = recordingStartTime
  fun getConfigProvider(): IHybridSpeechConfigProvider? = configProvider
  fun getCurrentRecordId(): Long? = currentRecordId
  fun getCurrentUserId(): String? = currentUserId
  fun getCurrentAudioType(): String = currentAudioType
  fun getStorageManager(): RecordingStorageManager? = storageManager
  fun getResumeState(): ResumeState = resumeState
  fun getCurrentPcmFilePath(): String? = currentPcmFilePath
  fun isPauseRealTimeProcessing(): Boolean = pauseRealTimeProcessing
}
