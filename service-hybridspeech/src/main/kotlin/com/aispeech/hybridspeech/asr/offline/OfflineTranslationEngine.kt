package com.aispeech.hybridspeech.asr.offline

import android.os.Environment
import com.aispeech.AIError
import com.aispeech.AIResult
import com.aispeech.aibase.AILog
import com.aispeech.export.config.AILocalMtConfig
import com.aispeech.export.engines2.AILocalMtEngine
import com.aispeech.export.intent.AILocalMtIntent
import com.aispeech.export.listeners.AIMtListener
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.lite.MagnusRuntimeTask
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import org.json.JSONArray
import org.json.JSONObject
import java.io.File

/**
 * 离线翻译引擎
 * 本地离线翻译引擎，处理文本翻译并生成翻译结果
 */
class OfflineTranslationEngine: CoroutineScope {
  companion object {
    private const val TAG = "OfflineTranslationEngine"

  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val _translationResultFlow = MutableSharedFlow<String>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val translationResultFlow: SharedFlow<String> = _translationResultFlow.asSharedFlow()

  private var aiLocalMtEngine: AILocalMtEngine? = null
  private var isEngineInitialized = false
  private var initializationInProgress = false

  // 翻译配置
  private var taskName: String = MagnusRuntimeTask.MODEL_TRANS_DIALECT
  private var resourcePath: String = ""

  // 状态变化监听器
  private var statusChangeListener: ((OfflineTranslationEngineStatus) -> Unit)? = null

  /**
   * 设置翻译任务类型
   */
  fun setTaskName(taskName: String) {
    this.taskName = taskName
    AILog.i(TAG, "Task name set: $taskName")
  }

  /**
   * 设置资源路径
   */
  fun setResourcePath(path: String) {
    this.resourcePath = path
    AILog.i(TAG, "Resource path set: $path")
  }

  /**
   * 设置状态变化监听器
   */
  fun setStatusChangeListener(listener: (OfflineTranslationEngineStatus) -> Unit) {
    this.statusChangeListener = listener
  }

  /**
   * 通知状态变化
   */
  private fun notifyStatusChange() {
    statusChangeListener?.invoke(getStatus())
  }

  /**
   * 初始化引擎
   */
  fun initialize(): Boolean {
    try {
      if (initializationInProgress) {
        AILog.w(TAG, "Initialization already in progress")
        return false
      }

      if (isEngineInitialized) {
        AILog.w(TAG, "Engine already initialized")
        return true
      }

      initializationInProgress = true
      isEngineInitialized = false

      // 通知开始初始化
      notifyStatusChange()

      // 使用默认路径如果没有设置
      if (resourcePath.isEmpty()) {
        resourcePath = getDefaultResourcePath()
        AILog.i(TAG, "Using default resource path: $resourcePath")
      }

      // 检查资源路径是否存在
      if (!checkResourcePath()) {
        AILog.e(TAG, "Resource path does not exist or is invalid: $resourcePath")
        initializationInProgress = false
        notifyStatusChange()
        return false
      }

      // 创建AILocalMtEngine实例
      aiLocalMtEngine = AILocalMtEngine.createInstance()
      if (aiLocalMtEngine == null) {
        AILog.e(TAG, "Failed to create AILocalMtEngine instance")
        initializationInProgress = false
        notifyStatusChange()
        return false
      }

      // 配置翻译引擎
      val mtConfig = AILocalMtConfig.Builder()
        .setTaskName(taskName)
        .build()

      // 初始化引擎 - 这里会触发onInit回调
      aiLocalMtEngine!!.init(mtConfig, createMtListener())

      AILog.i(TAG, "Offline translation engine initialization started, waiting for onInit callback...")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error initializing offline translation engine", e)
      initializationInProgress = false
      return false
    }
  }

  /**
   * 获取默认资源路径
   */
  private fun getDefaultResourcePath(): String {
    return File(Environment.getExternalStorageDirectory(), "hybrid_speech_debug/offline_translation_resources").path
  }

  /**
   * 检查资源路径是否有效
   */
  private fun checkResourcePath(): Boolean {
    return try {
      val resourceDir = File(resourcePath)
      val exists = resourceDir.exists() && resourceDir.isDirectory
      if (exists) {
        AILog.i(TAG, "Resource path verified: $resourcePath")
      } else {
        AILog.w(TAG, "Resource path does not exist, will attempt to use default resources")
        resourceDir.mkdirs()
      }
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Error checking resource path: $resourcePath", e)
      false
    }
  }

  /**
   * 开始翻译文本
   */
  private fun translateText(
    inputText: String,
    fromLanguage: String = TranslationLanguage.ENGLISH,
    toLanguage: String = TranslationLanguage.CHINESE,
    domain: String = TranslationDomain.AICAR,
  ): Boolean {
    try {
      if (aiLocalMtEngine == null || !isEngineInitialized) {
        AILog.e(TAG, "Engine not properly initialized")
        return false
      }

      if (initializationInProgress) {
        AILog.e(TAG, "Engine initialization still in progress")
        return false
      }

      val mtIntent = AILocalMtIntent().apply {
        setFromLanStr(inputText)
        setDomain(domain)
        setFromLanguages(fromLanguage)
        setToLanguages(toLanguage)
      }

      aiLocalMtEngine!!.start(mtIntent)
      AILog.i(TAG, "Translation started for text: $inputText")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error starting translation", e)
      return false
    }
  }

  /**
   * 使用配置对象开始翻译文本
   */
  fun translateText(inputText: String, config: TranslationConfig): Boolean {
    return translateText(
      inputText = inputText,
      fromLanguage = config.fromLanguage,
      toLanguage = config.toLanguage,
      domain = config.domain,
    )
  }

  /**
   * 停止处理
   */
  fun stop() {
    aiLocalMtEngine?.stop()
    AILog.i(TAG, "Offline translation engine stopped")
  }

  /**
   * 关闭引擎协程作用域
   */
  fun shutdown(reason: String = "OfflineTranslationEngine shutdown") {
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Offline translation engine scope shut down: $reason")
  }

  /**
   * 释放资源
   */
  fun release() {
    stop()
    aiLocalMtEngine?.destroy()
    aiLocalMtEngine = null
    isEngineInitialized = false
    initializationInProgress = false

    shutdown("Engine released")
    AILog.i(TAG, "Offline translation engine released")
  }

  /**
   * 获取引擎状态
   */
  fun getStatus(): OfflineTranslationEngineStatus {
    return OfflineTranslationEngineStatus(
      isInitialized = isEngineInitialized,
      isInitializing = initializationInProgress,
      taskName = taskName,
      resourcePath = resourcePath
    )
  }

  /**
   * 创建翻译监听器
   */
  private fun createMtListener(): AIMtListener {
    return object : AIMtListener() {
      override fun onInit(p0: Int) {
        AILog.d(TAG, "onInit status: $p0")
        initializationInProgress = false
        if (p0 == 0) {
          isEngineInitialized = true
          AILog.i(TAG, "Translation engine initialization completed successfully")
        } else {
          isEngineInitialized = false
          AILog.e(TAG, "Translation engine initialization failed with status: $p0")
        }

        // 通知状态变化
        notifyStatusChange()
      }

      override fun onError(error: AIError?) {
        AILog.e(TAG, "Translation error: ${error?.error ?: "Unknown error"}")
        launch {
          _translationResultFlow.emit("Error: ${error?.error ?: "Unknown error"}")
          AILog.d(TAG, "Emitted error to translation flow")
        }
      }

      override fun onResults(result: AIResult?) {
        val jsonString = result?.resultJSONObject?.toString() ?: ""
        AILog.d(TAG, "onResults: $result")
        AILog.d(TAG, "Translation result JSON: $jsonString")
        launch {
          if (jsonString.isNotEmpty()) {
            _translationResultFlow.emit(jsonString)
            AILog.d(TAG, "Emitted translation result to flow")
          } else {
            AILog.w(TAG, "Empty translation result, not emitting")
          }
        }
      }
    }
  }

  /**
   * 创建NER资源对象的辅助方法
   */
  fun createNerResObject(userPath: String, userVocabs: List<String>): JSONObject {
    val jsonObject = JSONObject()
    val ner = JSONObject()

    ner.put("userPath", userPath)

    val jsonArray = JSONArray()
    userVocabs.forEach { vocab ->
      jsonArray.put(vocab)
    }
    ner.put("userVocabs", jsonArray)

    jsonObject.put("ner", ner)
    return jsonObject
  }
}



/**
 * 翻译语言常量
 */
object TranslationLanguage {
  // 英语
  const val ENGLISH = "English"
  // 中文
  const val CHINESE = "Chinese"
  // 普通话
  const val MANDARIN = "Mandarin"
  // 自动检测
  const val AUTO = "Auto"
  // 泰语
  const val THAI = "Thai"
  // 西班牙语
  const val SPANISH = "Spanish"
  // 阿拉伯语
  const val ARABIC = "Arabic"
  // 俄语
  const val RUSSIAN = "Russian"
  // 葡萄牙语
  const val PORTUGUESE = "Portuguese"
  // 德语
  const val GERMAN = "German"
  // 法语
  const val FRENCH = "French"
  // 印尼语
  const val INDONESIAN = "Indonesian"
  // 马来语
  const val MALAY = "Malay"
  // 越南语
  const val VIETNAMESE = "Vietnamese"
  // 日语
  const val JAPANESE = "Japanese"
  // 韩语
  const val KOREAN = "Korean"
}

/**
 * 翻译领域常量
 */
object TranslationDomain {
  // 通用领域
  const val COMM = "comm"
  // 车载领域
  const val AICAR = "aicar"
  // 家居领域
  const val AIHOME = "aihome"
}

/**
 * 翻译配置类
 */
data class TranslationConfig(
  val taskName: String = MagnusRuntimeTask.MODEL_TRANS_DIALECT,
  val fromLanguage: String = TranslationLanguage.ENGLISH,
  val toLanguage: String = TranslationLanguage.CHINESE,
  val domain: String = TranslationDomain.AICAR,
  val resourcePath: String = "",
  val useNer: Boolean = false,
  val usePost: Boolean = false,
  val nerUserPath: String? = null,
  val nerUserVocabs: List<String> = emptyList()
) {
  /**
   * 创建NER资源对象
   */
  fun createNerResObject(): JSONObject? {
    return if (useNer && !nerUserPath.isNullOrEmpty() && nerUserVocabs.isNotEmpty()) {
      val jsonObject = JSONObject()
      val ner = JSONObject()

      ner.put("userPath", nerUserPath)

      val jsonArray = JSONArray()
      nerUserVocabs.forEach { vocab ->
        jsonArray.put(vocab)
      }
      ner.put("userVocabs", jsonArray)

      jsonObject.put("ner", ner)
      jsonObject
    } else {
      null
    }
  }
}

/**
 * 离线翻译引擎状态
 */
data class OfflineTranslationEngineStatus(
  val isInitialized: Boolean,
  val isInitializing: Boolean,
  val taskName: String,
  val resourcePath: String
)
