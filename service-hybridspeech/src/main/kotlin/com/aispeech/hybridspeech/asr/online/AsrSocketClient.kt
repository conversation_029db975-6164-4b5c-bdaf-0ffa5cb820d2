package com.aispeech.hybridspeech.asr.online

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.lib_ktx.SharedEventFlow
import com.aispeech.tablet.core.common.FileCacheUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.WebSocket
import okhttp3.WebSocketListener
import okio.ByteString.Companion.toByteString
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.TimeUnit

/*
 * 音频数据源类型枚举已简化
 * 原来的 OGG_CACHE 模式已被移除，统一使用 DIRECT 模式
 * 所有音频数据（OGG Opus 和 MP3）都通过 sendAudioData() 直接发送
 */

/**
 * ASR WebSocket客户端接口
 * 定义统一的WebSocket客户端接口，支持不同的实现方式
 */
interface IAsrSocketClient {
  val transcriptionResultFlow: SharedFlow<String>
  val connectionStatusFlow: SharedFlow<ConnectionStatus>

  fun setConfig(serverUrl: String)
  fun connect()
  fun disconnect()
  fun sendText(value: String): Boolean
  fun sendAudioData(audioData: ByteArray): Boolean
  fun getCurrentState(): ConnectionStatus
  fun getCurrentServerUrl(): String
  fun destroy()

  // 用于需要等待完成的场景
  suspend fun disconnectAndJoin()
  suspend fun destroyAndJoin()
}

/**
 * ASR WebSocket客户端工厂
 * 用于创建不同实现的客户端
 */
object AsrSocketClientFactory {
  enum class ClientType {
    OKHTTP,
    KTOR
  }

  fun create(type: ClientType = ClientType.OKHTTP): IAsrSocketClient {
    return when (type) {
      ClientType.OKHTTP -> AsrSocketClientOkHttp()
      else -> throw UnsupportedOperationException()
//      ClientType.KTOR -> AsrSocketClientKtor()
    }
  }
}

/**
 * ASR WebSocket客户端 (OkHttp实现)
 * 统一使用直接发送模式，不再使用应用层缓存
 * 信任 OkHttp 内部的队列管理和背压处理机制
 */
class AsrSocketClientOkHttp : IAsrSocketClient, CoroutineScope {
  companion object {
    private const val TAG = "AsrSocketClientOkHttp"
    private const val CONNECT_TIMEOUT = 10L // 秒
    private const val READ_TIMEOUT = 30L // 秒
    private const val WRITE_TIMEOUT = 30L // 秒
    private const val PING_INTERVAL = 15L // 秒

    //打印前几帧音频消息
    private const val MAX_TOP_N_AUDIO_MSG = 5
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  // --- Flows for communication ---
  private val _transcriptionResultFlow = SharedEventFlow<String>(replay = 0, extraBufferCapacity = 100)
  override val transcriptionResultFlow: SharedFlow<String> = _transcriptionResultFlow.asSharedFlow()

  private val _connectionStatusFlow = SharedEventFlow<ConnectionStatus>(replay = 1, extraBufferCapacity = 10)
  override val connectionStatusFlow: SharedFlow<ConnectionStatus> = _connectionStatusFlow.asSharedFlow()

  // --- State Management & Concurrency ---
  private val stateLock = Any()
  @Volatile
  private var state: ConnectionStatus = ConnectionStatus.DISCONNECTED

  // --- Configuration & Resources ---
  private var serverUrl: String = ""
  private var webSocket: WebSocket? = null

  //  --- Network ---
  private val okHttpClient: OkHttpClient = OkHttpClient.Builder()
    .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
    .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
    .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
    .build()

  // asr text queue & loop task
  private var blockQueue = LinkedBlockingQueue<String>()
  private var job: Job? = Job()

  //save debug encode audio
  private var encodeOutputStream: FileOutputStream? = null
  private val sdf = SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault())
  // debug log
  private var printQueueTimestamp: Long = 0
  private val thresholdQueueSize = 3200 * 50 //5s

  override fun setConfig(serverUrl: String) {
    this.serverUrl = serverUrl
    AILog.i(TAG, "ASR config updated: $serverUrl")
  }

  override fun sendText(value: String): Boolean {
    val success = webSocket?.send(value) == true
    if (success.not()) {
      AILog.w(TAG, "sendText Failed!!! $value")
    }
    return success
  }

  //周期性的打印日志
  private var schedulePrintCount = 0
  //打印音频topN消息
  private var printTopNAudioMsg = MAX_TOP_N_AUDIO_MSG
  override fun sendAudioData(audioData: ByteArray): Boolean {
    if (audioData.isEmpty()) {
      AILog.i(TAG, "====feed Empty Frame!!!====")
    } else {
      if (printTopNAudioMsg-- > 0) {
        AILog.i(TAG, "topN feed audio size: ${audioData.size}, state: $state, socket: $webSocket")
      }
    }

    if (webSocket == null || (state != ConnectionStatus.CONNECTED)) {
      AILog.e(TAG, "invalid feed audio size: ${audioData.size}, state: $state, socket: $webSocket")
    } else if(isDebugSaveEncodeAudio()) {
      kotlin.runCatching {
        encodeOutputStream?.write(audioData)
      }
    }

    val success = webSocket?.send(audioData.toByteString()) ?: false
    val queueSize = webSocket?.queueSize() ?: 0
    if (queueSize >= thresholdQueueSize) {
      val ts = System.currentTimeMillis()
      if (ts - printQueueTimestamp >= 5000) {
        printQueueTimestamp = ts
        AILog.w(TAG, "====~~~~~maybe network too slowly or reconnect~~~~~====. queue size = $queueSize")
      }
    }

    if (schedulePrintCount++ % 99 == 0) {
      AILog.i(TAG,"print feed audio buff len = ${audioData.size}, queue size = $queueSize, $webSocket")
      schedulePrintCount = 1;
    }
    return success
  }

  override fun getCurrentState(): ConnectionStatus = state

  override fun getCurrentServerUrl(): String = serverUrl

  override fun connect() {
    synchronized(stateLock) {
//      这里先让他进去，避免错误状态导致无法恢复
//      if (state == ConnectionStatus.CONNECTING || state == ConnectionStatus.CONNECTED) {
//        AILog.w(TAG, "Already connecting or connected. Current state: $state")
//        return
//      }

      if (serverUrl.isEmpty()) {
        AILog.e(TAG, "Server URL not set, cannot connect.")
        updateState(ConnectionStatus.ERROR)
        return
      }

      runCatching {
        webSocket?.let {
          //如果已连接，需要先断开
          disconnect()
        }
        resetData()
        initBindEvent()

        //开始连接
        updateState(ConnectionStatus.CONNECTING)

        val request = Request.Builder().url(serverUrl).build()
        val newClient = okHttpClient.newBuilder().pingInterval(PING_INTERVAL, TimeUnit.SECONDS).build()
        webSocket = newClient.newWebSocket(request, createWebSocketListener())
        AILog.i(TAG, "====connect url====:$serverUrl, $webSocket")

        if (webSocket == null) {
          AILog.e(TAG, "====connect-error====, client is null.")
          handleConnectionFailure()
        }
      }.onFailure {
        AILog.e(TAG, "====connect-error====: $it, $webSocket")
        handleConnectionFailure()
      }
    }
  }

  override fun disconnect() {
    synchronized(stateLock) {
      if (state == ConnectionStatus.DISCONNECTED) {
        AILog.i(TAG, "Already disconnected.")
        return
      }
      AILog.i(TAG, "====disconnect====$webSocket")
      updateState(ConnectionStatus.DISCONNECTING)

      // 清理网络资源
      cleanupNetworkResources()

      job?.cancel()
      resetData()

      updateState(ConnectionStatus.DISCONNECTED)
      AILog.i(TAG, "Disconnected successfully. $webSocket")
    }
  }

  override suspend fun disconnectAndJoin() {
    AILog.i(TAG, "disconnectAndJoin")
    disconnect()
  }

  override fun destroy() {
    AILog.i(TAG, "destroy")
    disconnect()
    shutdown("Client destroy")
    AILog.i(TAG, "Client destroyed successfully.")
  }

  override suspend fun destroyAndJoin() {
    AILog.i(TAG, "destroyAndJoin")
    destroy()
  }

  /**
   * 关闭客户端协程作用域
   */
  fun shutdown(reason: String = "AsrSocketClient shutdown") {
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "ASR socket client scope shut down: $reason")
  }

  private fun createWebSocketListener(): WebSocketListener {
    return object : WebSocketListener() {
      override fun onOpen(webSocket: WebSocket, response: Response) {
        AILog.i(TAG, "====onOpen==== $webSocket")
        synchronized(stateLock) {
          updateState(ConnectionStatus.CONNECTED)
        }

        if (isDebugSaveEncodeAudio()) {
          encodeOutputStream?.close()
          val encodeAudioPath = FileCacheUtils.getAudioCachePath() + sdf.format(System.currentTimeMillis())
          AILog.i(TAG, "save encode audio path = $encodeAudioPath")
          encodeOutputStream = FileOutputStream(encodeAudioPath, true)
        }
      }

      override fun onMessage(webSocket: WebSocket, text: String) {
        AILog.i(TAG, "====onMessage==== $text")
        blockQueue.add(text)
      }

      override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
        AILog.i(TAG, "====onClosing==== $code, $reason, $webSocket")
        synchronized(stateLock) {
          updateState(ConnectionStatus.DISCONNECTING)
        }
      }

      override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
        AILog.i(TAG, "====onClosed==== $code, $reason, $webSocket")
        synchronized(stateLock) {
          handleConnectionFailure()
        }
      }

      override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
        AILog.i(TAG, "====onFailure==== $t, $webSocket")
        synchronized(stateLock) {
          handleConnectionFailure()
        }
      }
    }
  }

  private fun handleConnectionFailure() {
    synchronized(stateLock) {
      // 如果是主动断开或正在断开，则不重连
      if (state == ConnectionStatus.DISCONNECTED || state == ConnectionStatus.DISCONNECTING) {
        AILog.i(TAG, "handleConnectionFailure. Connection Already Close. Current state: $state")
        return
      }

      AILog.i(TAG, "handleConnectionFailure. Current state: $state")
      updateState(ConnectionStatus.ERROR)
    }

    // 清理网络资源
    cleanupNetworkResources()
  }

  private fun initBindEvent() {
    AILog.i(TAG, "initBindEvent")
    job?.cancel()
    job = launch {
      AILog.i(TAG, " >>> Event Job Begin")
      while (isActive) {
        val data = blockQueue.poll(1000, TimeUnit.MILLISECONDS)
        if (data != null) {
          _transcriptionResultFlow.emit(data)
        }
      }
      AILog.i(TAG, "<<< Event Job End")
    }
  }

  private fun resetData() {
    AILog.i(TAG, "resetData")
    printTopNAudioMsg = MAX_TOP_N_AUDIO_MSG

    encodeOutputStream?.close()
    encodeOutputStream = null

    blockQueue.clear()
    blockQueue = LinkedBlockingQueue()
  }

  /**
   * 只清理网络资源，不触碰协程作用域 (异步版本)
   * 用于断开连接但保持客户端可重连的场景
   */
  private fun cleanupNetworkResources() {
    AILog.i(TAG, ">>> Begin Cleaning up network resources")

    // 直接关闭 WebSocket 连接
    webSocket?.close(4444,"disconnect")
    webSocket?.cancel()
    webSocket = null

//    // OkHttp 建议使用 shutdownNow 来立即停止所有任务
//    okHttpClient.dispatcher?.executorService?.shutdownNow()
//    okHttpClient.connectionPool?.evictAll()
//    okHttpClient = null

    AILog.i(TAG, "<<< Network resources cleanup completed")
  }

  private fun updateState(newState: ConnectionStatus) {
    synchronized(stateLock) {
      if (state != newState) {
        state = newState
        _connectionStatusFlow.tryEmit(newState)
        AILog.i(TAG, "State changed to: $newState")
      }
    }
  }

  private fun isDebugSaveEncodeAudio(): Boolean {
    //TODO 这里要由客户端传一个调试音频是否需要的开关过来
    return true
  }

}

/**
 * 连接状态枚举
 */
enum class ConnectionStatus {
  DISCONNECTED,
  CONNECTING,
  CONNECTED,
  DISCONNECTING,
  ERROR
}

