package com.aispeech.hybridspeech.asr.offline

import android.os.Environment
import android.util.Log
import com.aispeech.AIError
import com.aispeech.AIResult
import com.aispeech.aibase.AILog
import com.aispeech.export.config.AILocalLASRConfig
import com.aispeech.export.config.MagnusRuntimeConfig
import com.aispeech.export.config.MagnusRuntimeEnvInfo
import com.aispeech.export.engines2.AILocalLASREngine
import com.aispeech.export.engines2.MagnusRuntimeHelper
import com.aispeech.export.intent.AILocalLASRIntent
import com.aispeech.export.listeners.AIASRListener
import com.aispeech.hybridspeech.OfflineEngineConfig
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.lite.MagnusRuntimeTask
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import org.json.JSONObject
import java.io.File
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * OfflineEngine 的状态机密封类
 */
sealed class EngineState {
  /**
   * 空闲状态。
   */
  data object Idle : EngineState()

  /**
   * 正在初始化状态。
   */
  data class Initializing(val pcmBuffer: ConcurrentLinkedQueue<ByteArray>) : EngineState()

  /**
   * 已就绪状态。
   */
  data class Ready(val modelPath: String) : EngineState()

  /**
   * 正在运行状态。
   */
  data object Running : EngineState()

  /**
   * 暂停状态。
   */
  data object Paused : EngineState()

  /**
   * 错误状态。
   */
  data class Error(val error: AIError, val previousState: EngineState) : EngineState()
}

/**
 * 离线转写引擎
 * 本地离线转写引擎，处理PCM数据并生成转写结果
 */
class OfflineEngine : CoroutineScope {
  companion object {
    private const val TAG = "OfflineEngine"
    private const val FRAME_SIZE_MS = 20 // 20ms处理一次，降低延迟
    private const val SAMPLE_RATE = 16000
    private const val BYTES_PER_SAMPLE = 2 // 16位PCM
    private const val FRAME_SIZE_BYTES =
      SAMPLE_RATE * BYTES_PER_SAMPLE * FRAME_SIZE_MS / 1000 // 640字节
    private const val RING_BUFFER_SIZE = 64 * 1024 // 64KB环形缓冲区，可缓存约2秒音频
    private const val CHANNEL_CAPACITY = 50 // Channel容量
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val _transcriptionResultFlow = MutableSharedFlow<String>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val transcriptionResultFlow: SharedFlow<String> = _transcriptionResultFlow.asSharedFlow()

  private var processingJob: Job? = null
  private val ringBuffer = RingBuffer(RING_BUFFER_SIZE)

  private lateinit var pcmChannel: Channel<ByteArray>

  private var modelPath: String = ""

  private var aiLocalLASREngine: AILocalLASREngine? = null
  private var isMagnusInitialized = false
  private var initializationJob: Job? = null

  //结束监听
  private val _eofEndFlow = MutableSharedFlow<Boolean>()
  val eofEndFlow = _eofEndFlow.asSharedFlow()  // 对外暴露不可变的 SharedFlow

  // 处理配置
  private var useCustomFeed: Boolean = true
  private var useTxtSmooth: Boolean = true
  private var useWpInRec: Boolean = true
  private var useSensitiveWdsNorm: Boolean = true
  private var useStreamChar: Boolean = false
  private var useTprocess: Boolean = true
  private var usePhrase: Boolean = true

  // 状态机
  private val _currentState = MutableStateFlow<EngineState>(EngineState.Idle)
  val currentStateFlow: StateFlow<EngineState> = _currentState.asStateFlow()

  // 状态变化监听器
  private var statusChangeListener: ((OfflineEngineStatus) -> Unit)? = null

  /**
   * 状态转换方法
   */
  private fun transitionTo(newState: EngineState) {
    val oldState = _currentState.value
    _currentState.value = newState
    AILog.i(TAG, "State transition: ${oldState::class.simpleName} -> ${newState::class.simpleName}")
    notifyStatusChange()
  }


  /**
   * 设置配置
   * @param config 新的配置
   * @return true 如果需要初始化，false 如果配置相同无需初始化
   */
  fun setConfig(config: OfflineEngineConfig, enableSmooth: Boolean = true): Boolean {
    // 检查是否需要重新初始化（主要看模型路径）
    val needsInit = modelPath != config.modelPath || _currentState.value is EngineState.Idle

    if (needsInit) {
      AILog.i(TAG, "Model path changed from '$modelPath' to '${config.modelPath}' or engine is idle, will reinitialize")

      // 如果引擎正在运行，先释放
      if (_currentState.value !is EngineState.Idle) {
        AILog.i(TAG, "Releasing current engine for reinitialization")
        release()
      }
    }

    // 更新配置
    this.modelPath = config.modelPath
    this.useTxtSmooth = enableSmooth

    AILog.i(TAG, "Config updated: $config")
    return needsInit
  }


  /**
   * 设置状态变化监听器
   */
  fun setStatusChangeListener(listener: (OfflineEngineStatus) -> Unit) {
    this.statusChangeListener = listener
  }

  /**
   * 通知状态变化
   */
  private fun notifyStatusChange() {
    statusChangeListener?.invoke(getStatus())
  }

  /**
   * 初始化引擎
   */
  fun initialize(): Boolean {
    try {
      when (_currentState.value) {
        is EngineState.Initializing -> {
          AILog.w(TAG, "Initialization already in progress")
          return false
        }

        is EngineState.Ready -> {
          AILog.w(TAG, "Engine already initialized")
          return true
        }

        is EngineState.Running, is EngineState.Paused -> {
          AILog.w(TAG, "Engine is already running, cannot reinitialize")
          return false
        }

        is EngineState.Error -> {
          AILog.i(TAG, "Recovering from error state, starting initialization")
        }

        is EngineState.Idle -> {
          AILog.i(TAG, "Starting initialization from idle state")
        }
      }

      // 转换到初始化状态，创建PCM缓存队列
      val pcmBuffer = ConcurrentLinkedQueue<ByteArray>()
      transitionTo(EngineState.Initializing(pcmBuffer))

      // 启动异步初始化协程
      initializationJob = launch {
        performInitialization()
      }

      AILog.i(TAG, "Offline engine async initialization started...")
      return true
    } catch (e: Exception) {
      AILog.e(TAG, "Error starting async initialization", e)
      transitionTo(EngineState.Error(AIError(), _currentState.value))
      return false
    }
  }


  /**
   * 执行实际的初始化工作
   */
  private suspend fun performInitialization() {
    try {
      // 使用默认路径如果没有设置
      if (modelPath.isEmpty()) {
        modelPath = getDefaultModelPath()
        AILog.i(TAG, "Using default model path: $modelPath")
      }

      // 在IO线程中执行初始化
      withContext(Dispatchers.IO) {
        // 初始化Magnus运行时环境
        if (!initializeMagnusRuntime()) {
          AILog.e(TAG, "Failed to initialize Magnus runtime")
          transitionTo(EngineState.Error(AIError(), _currentState.value))
          return@withContext
        }

        // 创建AILocalLASREngine实例
        aiLocalLASREngine = AILocalLASREngine.createInstance()
        if (aiLocalLASREngine == null) {
          AILog.e(TAG, "Failed to create AILocalLASREngine instance")
          transitionTo(EngineState.Error(AIError(), _currentState.value))
          return@withContext
        }

        // 配置LASR引擎
        val lasrConfig = AILocalLASRConfig.Builder()
          .setTaskName(MagnusRuntimeTask.MODEL_LASR)
          .build()

        // 初始化引擎 - 这里会触发onInit回调
        aiLocalLASREngine!!.init(lasrConfig, createAsrListener())
      }

      AILog.i(TAG, "Offline engine initialization completed, waiting for onInit callback...")

    } catch (e: Exception) {
      AILog.e(TAG, "Error during initialization", e)
      transitionTo(EngineState.Error(AIError(), _currentState.value))
    }
  }


  /**
   * 获取默认模型路径
   */
  private fun getDefaultModelPath(): String {
    return File(Environment.getExternalStorageDirectory(), "hybrid_speech_debug/offline_model").path
  }

  /**
   * 开始处理
   */
  fun start(): Boolean {
    try {
      when (val state = _currentState.value) {
        is EngineState.Running -> {
          AILog.w(TAG, "Already running")
          return true
        }

        is EngineState.Paused -> {
          AILog.i(TAG, "Resuming from paused state")
          transitionTo(EngineState.Running)
          return true
        }

        is EngineState.Initializing -> {
          AILog.i(TAG, "Engine is initializing, will start processing once initialization completes")
          return true
        }

        is EngineState.Ready -> {
          AILog.i(TAG, "Starting engine processing from ready state")
          return startEngineProcessing()
        }

        is EngineState.Idle -> {
          AILog.e(TAG, "Engine not initialized, cannot start")
          return false
        }

        is EngineState.Error -> {
          AILog.e(TAG, "Engine in error state: ${state.error}, cannot start")
          return false
        }
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Error starting offline engine", e)
      return false
    }
  }

  /**
   * 启动引擎处理
   */
  private fun startEngineProcessing(): Boolean {
    try {
      // 检查引擎是否已初始化
      if (aiLocalLASREngine == null) {
        AILog.e(TAG, "Engine not initialized, cannot start processing")
        return false
      }

      // 为新的处理会话创建一个新的 Channel
      pcmChannel = Channel(
        capacity = CHANNEL_CAPACITY,
        onBufferOverflow = BufferOverflow.DROP_LATEST
      )

      //重置状态
      _eofEndFlow.tryEmit(false)

      val param = AILocalLASRIntent().apply {
        isUseCustomFeed = <EMAIL>
        isUseTxtSmooth = <EMAIL>
        isUseWpInRec = <EMAIL>
        isUseSensitiveWdsNorm = <EMAIL>
        isUseStreamChar = <EMAIL>
        isUseTprocess = <EMAIL>
        isUsePhrase = <EMAIL>
      }
      aiLocalLASREngine!!.start(param)

      processingJob = launch {
        processingLoop()
      }

      // 转换到运行状态
      transitionTo(EngineState.Running)

      AILog.i(TAG, "Offline engine processing started")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error starting engine processing", e)
      transitionTo(EngineState.Error(AIError(), _currentState.value))
      return false
    }
  }

  /**
   * 停止处理
   */
  fun stop(timeoutMs: Long = 500) {
    when (val state = _currentState.value) {
      is EngineState.Running, is EngineState.Paused -> {
        AILog.i(TAG, "Stopping offline engine...")

        try {
          if (::pcmChannel.isInitialized && !pcmChannel.isClosedForSend) {
            pcmChannel.close()
            AILog.i(TAG, "PCM channel closed.")
          }
        } catch (e: Exception) {
          AILog.w(TAG, "Exception while closing channel, might be already closed. with exception: ${e.message}")
        }

        runBlocking {
          try {
            withTimeoutOrNull(timeoutMs) {
              processingJob?.join()
              AILog.i(TAG, "Processing job finished joining.")
            }
          } catch (e: Exception) {
            AILog.e(TAG, "Error waiting for processing job to join", e)
          }
        }

        flushRemainingAudio()
        AILog.i(TAG, "Flushed remaining audio from RingBuffer.")

        aiLocalLASREngine?.stop()
        AILog.i(TAG, "Called engine.stop().")

        processingJob = null
        ringBuffer.clear()

        transitionTo(EngineState.Ready(modelPath))

        AILog.i(TAG, "Offline engine stopped successfully.")
      }

      is EngineState.Initializing -> {
        AILog.i(TAG, "Stopping engine during initialization...")

        // 取消初始化并清空缓存
        initializationJob?.cancel()
        initializationJob = null

        launch {
          clearCachedPcmData(state.pcmBuffer)
        }

        // 转换到Idle状态
        transitionTo(EngineState.Idle)

        AILog.i(TAG, "Engine initialization cancelled and stopped.")
      }

      is EngineState.Ready -> {
        AILog.w(TAG, "Engine already stopped (in Ready state)")
      }

      is EngineState.Idle, is EngineState.Error -> {
        AILog.w(
          TAG,
          "Engine not running, stop command ignored (current state: ${state::class.simpleName})"
        )
      }
    }
  }

  /**
   * 关闭引擎协程作用域
   * 取消所有子协程，但不影响其他模块
   */
  fun shutdown(reason: String = "OfflineEngine shutdown") {
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Offline engine scope shut down: $reason")
  }


  /**
   * 释放资源
   */
  fun release() {
    // 先停止引擎
    stop()

    // 取消初始化任务
    initializationJob?.cancel()
    initializationJob = null

    // 清空缓存的PCM数据（如果有的话）
    when (val state = _currentState.value) {
      is EngineState.Initializing -> {
        runBlocking {
          clearCachedPcmData(state.pcmBuffer)
        }
      }

      else -> {
      }
    }

    // 销毁引擎
    aiLocalLASREngine?.destroy()
    aiLocalLASREngine = null

    // 转换到Idle状态
    transitionTo(EngineState.Idle)

    // 关闭协程作用域
    shutdown("Engine released")
    AILog.i(TAG, "Offline engine released")
  }

  /**
   * 暂停处理（清空缓存但保留引擎状态）
   */
  fun pause() {
    when (_currentState.value) {
      is EngineState.Running -> {
        AILog.i(TAG, "Pausing offline engine processing")

        try {
          // 停止处理循环
          processingJob?.cancel()
          processingJob = null

          // 清空缓存数据，释放内存
          clearBuffers()

          // 转换到暂停状态
          transitionTo(EngineState.Paused)

          AILog.i(TAG, "Offline engine paused successfully")
        } catch (e: Exception) {
          AILog.e(TAG, "Error pausing offline engine", e)
          transitionTo(EngineState.Error(AIError(), _currentState.value))
        }
      }

      is EngineState.Paused -> {
        AILog.w(TAG, "Already paused")
      }

      else -> {
        AILog.w(TAG, "Cannot pause: not running (current state: ${_currentState.value::class.simpleName})")
      }
    }
  }

  /**
   * 恢复处理
   */
  fun resume() {
    when (_currentState.value) {
      is EngineState.Paused -> {
        AILog.i(TAG, "Resuming offline engine processing")

        try {
          // 重新创建Channel，因为暂停时可能已经关闭
          pcmChannel = Channel(
            capacity = CHANNEL_CAPACITY,
            onBufferOverflow = BufferOverflow.DROP_LATEST
          )

          // 重新启动处理循环
          processingJob = launch {
            processingLoop()
          }

          // 转换到运行状态
          transitionTo(EngineState.Running)

          AILog.i(TAG, "Offline engine resumed successfully")
        } catch (e: Exception) {
          AILog.e(TAG, "Error resuming offline engine", e)
          transitionTo(EngineState.Error(AIError(), _currentState.value))
        }
      }

      is EngineState.Running -> {
        AILog.w(TAG, "Not paused, no need to resume")
      }

      else -> {
        AILog.w(
          TAG,
          "Cannot resume: not in paused state (current state: ${_currentState.value::class.simpleName})"
        )
      }
    }
  }

  /**
   * 清空所有缓存数据
   */
  private fun clearBuffers() {
    try {
      val size = ringBuffer.size()
      // 清空环形缓冲区
      ringBuffer.clear()

      AILog.i(TAG, "Cleared all buffers, size = $size")
    } catch (e: Exception) {
      AILog.e(TAG, "Error clearing buffers", e)
    }
  }

  /**
   * 处理PCM数据
   * 根据当前状态决定是缓存数据还是直接发送
   */
  fun processPcmData(pcmData: ByteArray) {
    when (val state = _currentState.value) {
      is EngineState.Initializing -> {
        state.pcmBuffer.offer(pcmData.copyOf())
      }

      is EngineState.Running -> {
        if (::pcmChannel.isInitialized) {
          pcmChannel.trySend(pcmData)
        }
      }

      is EngineState.Ready, is EngineState.Paused, is EngineState.Idle, is EngineState.Error -> {
        // 其他状态下忽略数据
        AILog.i(TAG, "Ignoring PCM data in state: ${state::class.simpleName}")
      }
    }
  }

  /**
   * 处理循环
   */
  private suspend fun processingLoop() {
    val frame = ByteArray(FRAME_SIZE_BYTES)          // 20ms → 640字节
    try {
      for (data in pcmChannel) {                     // 挂起式消费
        ringBuffer.write(data)
        while (ringBuffer.read(frame)) {
          aiLocalLASREngine?.feedData(frame, frame.size)
        }
      }
    } catch (e: Exception) {
      AILog.e(TAG, "processingLoop error with exception: ${e.message}")
    }
  }


  /**
   * 处理初始化期间缓存的PCM数据
   */
  private fun processCachedPcmData(pcmBuffer: ConcurrentLinkedQueue<ByteArray>) {
    try {
      if (pcmBuffer.isNotEmpty()) {
        val cacheSize = pcmBuffer.size
        AILog.i(TAG, "Processing $cacheSize cached PCM data chunks")

        // 如果引擎正在运行，直接发送缓存的数据
        when (_currentState.value) {
          is EngineState.Running -> {
            if (::pcmChannel.isInitialized) {
              var cachedData = pcmBuffer.poll()
              while (cachedData != null) {
                pcmChannel.trySend(cachedData)
                cachedData = pcmBuffer.poll()
              }
            }
          }

          else -> {
            // 如果引擎没有运行，清空缓存
            pcmBuffer.clear()
          }
        }

        AILog.i(TAG, "Cached PCM data processed and cleared")
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Error processing cached PCM data", e)
    }
  }

  /**
   * 清空缓存的PCM数据
   */
  private suspend fun clearCachedPcmData(pcmBuffer: ConcurrentLinkedQueue<ByteArray>) {
    try {
      val cacheSize = pcmBuffer.size
      pcmBuffer.clear()
      AILog.i(TAG, "Cleared $cacheSize cached PCM data chunks due to initialization failure")
    } catch (e: Exception) {
      AILog.e(TAG, "Error clearing cached PCM data with exception: ${e.message}")
    }
  }

  /**
   * 刷新剩余音频数据
   */
  private fun flushRemainingAudio() {
    try {
      val remainingSize = ringBuffer.size()
      if (remainingSize > 0) {
        val lastBytes = ByteArray(remainingSize)
        if (ringBuffer.read(lastBytes)) {
          aiLocalLASREngine?.feedData(lastBytes, lastBytes.size)
        }
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Error flushing remaining audio", e)
    }
  }

  /**
   * 获取引擎状态
   */
  fun getStatus(): OfflineEngineStatus {
    val state = _currentState.value
    return OfflineEngineStatus(
      engineState = state,
      modelPath = when (state) {
        is EngineState.Ready -> state.modelPath
        is EngineState.Running, is EngineState.Paused -> modelPath
        else -> modelPath
      },
      bufferSize = ringBuffer.size(),
      cachedDataCount = when (state) {
        is EngineState.Initializing -> state.pcmBuffer.size
        else -> 0
      }
    )
  }

  /**
   * 初始化Magnus运行时环境
   */
  private fun initializeMagnusRuntime(): Boolean {
    try {
      if (isMagnusInitialized) {
        AILog.i(TAG, "Magnus runtime already initialized")
        return true
      }

      val magnusRuntimeEnvInfo = MagnusRuntimeEnvInfo.Builder()
        .setType(MagnusRuntimeEnvInfo.TYPE_CLIENT)
        .setLogLevel(Log.WARN)
        .create()

      MagnusRuntimeHelper.getInstance().init(
        MagnusRuntimeConfig.Builder()
          .setPath(modelPath)
          .setInfo(magnusRuntimeEnvInfo.toJson())
          .setServerType(true)
          .create()
      )

      isMagnusInitialized = true
      AILog.i(TAG, "Magnus runtime initialized successfully")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error initializing Magnus runtime", e)
      return false
    }
  }

  /**
   * 创建ASR监听器
   */
  private fun createAsrListener(): AIASRListener {
    return object : AIASRListener {
      override fun onInit(p0: Int) {
        AILog.i(TAG, "onInit status: $p0")

        when (val state = _currentState.value) {
          is EngineState.Initializing -> {
            if (p0 == 0) {
              AILog.i(TAG, "Engine initialization completed successfully")

              // 转换到Ready状态
              transitionTo(EngineState.Ready(modelPath))

              // 启动引擎处理并处理缓存数据
              if (startEngineProcessing()) {
                launch {
                  processCachedPcmData(state.pcmBuffer)
                }
              } else {
                // 启动失败，清空缓存
                launch {
                  clearCachedPcmData(state.pcmBuffer)
                }
              }
            } else {
              AILog.e(TAG, "Engine initialization failed with status: $p0")

              // 初始化失败，转换到错误状态并清空缓存
              val error = AIError() // 可以根据p0设置具体错误信息
              transitionTo(EngineState.Error(error, state))

              launch {
                clearCachedPcmData(state.pcmBuffer)
              }
            }
          }

          else -> {
            AILog.w(TAG, "Received onInit callback in unexpected state: ${state::class.simpleName}")
          }
        }

        // 通知状态变化
        notifyStatusChange()
      }

      override fun onError(p0: AIError?) {
        AILog.i(TAG, "onError error: $p0")
      }

      override fun onReadyForSpeech() {
        AILog.i(TAG, "onReadyForSpeech")
      }

      override fun onResultDataReceived(p0: ByteArray?, p1: Int) {
        // 不需要处理
      }

      override fun onResultDataReceived(p0: ByteArray?, p1: Int, p2: Int) {
        // 不需要处理
      }

      override fun onRawDataReceived(p0: ByteArray?, p1: Int) {
        // 不需要处理
      }

      override fun onResults(result: AIResult?) {
        AILog.i(TAG, "onResults: $result")
        launch {
          val json = result?.resultJSONObject
          val eof = json?.optInt("eof", 0)

          // 拆分
          // 第一个 JSON（rec + rec_words）
          if (json?.optString("rec", "")?.isNotEmpty() == true) {
            val recJson = JSONObject().apply {
              put("rec", json?.optString("rec"))
              put("rec_words", json?.optJSONArray("rec_words"))
            }
            _transcriptionResultFlow.emit(recJson.toString())
          }

          // 第二个 JSON（var + var_begin）
          if (json?.optString("var", "")?.isNotEmpty() == true) {
            val varJson = JSONObject().apply {
              put("var", json?.optString("var"))
              put("var_begin", json?.optLong("var_begin"))
            }
            _transcriptionResultFlow.emit(varJson.toString())
          }

          if(eof == 1) {//结束成功
            AILog.i(TAG, "===received eof=1, stop end===")
            delay(50) //等一会儿，尽量等结果flow发完再cancel
            _eofEndFlow.emit(true)
          }
        }
      }

      override fun onRmsChanged(p0: Float) {
        // 音量变化，可以根据需要处理
      }

      override fun onBeginningOfSpeech() {
        AILog.i(TAG, "onBeginningOfSpeech")
      }

      override fun onEndOfSpeech() {
        AILog.i(TAG, "onEndOfSpeech")
      }

      override fun onNotOneShot() {
        AILog.i(TAG, "onNotOneShot")
      }

    }
  }

}

/**
 * 使用固定长度的环形缓冲区，线程安全且零拷贝
 */
private class RingBuffer(capacity: Int) {
  private val buf = ByteArray(capacity)
  private var readPos = 0
  private var writePos = 0
  private var available = 0
  private val lock = Any()

  private fun discard(count: Int) {
    val real = minOf(count, available)
    readPos = (readPos + real) % buf.size
    available -= real
  }

  fun write(src: ByteArray) = synchronized(lock) {
    if (src.size > buf.size - available) {
      discard(src.size - (buf.size - available))
    }
    var offset = 0
    var len = src.size
    while (len > 0) {
      val space = minOf(len, buf.size - writePos)
      System.arraycopy(src, offset, buf, writePos, space)
      writePos = (writePos + space) % buf.size
      offset += space
      len -= space
      available += space
    }
  }

  fun read(dst: ByteArray): Boolean {
    synchronized(lock) {
      if (available < dst.size) return false
      var offset = 0
      var len = dst.size
      while (len > 0) {
        val chunk = minOf(len, buf.size - readPos)
        System.arraycopy(buf, readPos, dst, offset, chunk)
        readPos = (readPos + chunk) % buf.size
        offset += chunk
        len -= chunk
        available -= chunk
      }
      return true
    }
  }

  fun size(): Int = synchronized(lock) { available }

  // 添加清空缓冲区的方法
  fun clear() = synchronized(lock) {
    readPos = 0
    writePos = 0
    available = 0
  }
}

/**
 * 离线引擎状态
 */
data class OfflineEngineStatus(
  val engineState: EngineState,
  val modelPath: String,
  val bufferSize: Int,
  val cachedDataCount: Int = 0
) {
  val isRunning: Boolean get() = engineState is EngineState.Running
  val isInitialized: Boolean get() = engineState is EngineState.Ready || engineState is EngineState.Running || engineState is EngineState.Paused
  val isInitializing: Boolean get() = engineState is EngineState.Initializing
  val isPaused: Boolean get() = engineState is EngineState.Paused
  val isIdle: Boolean get() = engineState is EngineState.Idle
  val isError: Boolean get() = engineState is EngineState.Error
}
