package com.aispeech.hybridspeech

import android.app.Application
import android.os.Environment
import com.aispeech.DUILiteConfig
import com.aispeech.DUILiteSDK
import com.aispeech.aibase.AILog
import com.aispeech.aibase.logger.LogLevel
import com.aispeech.export.config.AuthConfig
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.hybridspeech.util.DuiAuthParamsProvider
import com.aispeech.hybridspeech.worker.DuiAuthManager
import com.aispeech.lite.AuthType
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.PathUtils
import java.io.File

class HybridSpeechApplication : Application() {

  companion object {
    const val TAG = "HybridSpeechApplication"
    private const val MAX_LOG_SIZE = 3 * 1024 * 1024
  }

  override fun onCreate() {
    super.onCreate()
    val ts = System.currentTimeMillis()
    AILog.i(TAG, "onCreate begin")
    initAiLog()
    initializeThirdPartySDKs()
    AILog.i(TAG,"====HybridSpeechApp onCreate, cost: ${System.currentTimeMillis() - ts}, version: ${AppUtils.getAppVersionName()}, ${AppUtils.getAppVersionCode()}====")
  }

  override fun onTerminate() {
    super.onTerminate()
    CoroutineScopeManager.shutdown()
  }

  /**
   * 初始化 AILOG
   */
  private fun initAiLog() {
    var parentPath = PathUtils.getExternalStoragePath()
    if (parentPath.length < 2) {
      parentPath = Environment.getExternalStorageDirectory().absolutePath
    }

    var logLevel = LogLevel.RELEASE
    if (BuildConfig.DEBUG) {
      logLevel = LogLevel.FULL
    }

    AILog.Init(
      this, logLevel, MAX_LOG_SIZE, BuildConfig.DEBUG,
      parentPath + File.separator + "AISpeechLog" + File.separator, "hybridspeech"
    )
  }

  /**
   * 初始化 duilite sdk
   */
  private fun initializeThirdPartySDKs() {
    val config = DUILiteConfig.Builder()
      .setApiKey(DuiAuthParamsProvider.getApiKey())
      .setProductId(DuiAuthParamsProvider.getProductId())
      .setProductKey(DuiAuthParamsProvider.getProductKey())
      .setProductSecret(DuiAuthParamsProvider.getProductSecret())
      .setAuthConfig(
        AuthConfig.Builder()
          .setAuthTimeout(DuiAuthParamsProvider.getAuthTimeout())
          .setCustomDeviceName(DuiAuthParamsProvider.getCustomDeviceName())
          .setType(AuthType.ONLINE)
          .setAuthServer(DuiAuthParamsProvider.getAuthServer())
          .create()
      )
      .create()

    // SDK预初始化方法
    // init预初始化方法耗时极少，不会影响App首次冷启动用户体验
    DUILiteSDK.init(applicationContext)
    DUILiteSDK.setJavaLiteLogLevel(3)
    DUILiteSDK.setNativeLogLevel(3)

    // SDK 授权
    DUILiteSDK.doAuth(applicationContext, config, object : DUILiteSDK.InitListener {
      override fun success() {
        AILog.i(TAG, "授权成功! ")
      }

      override fun error(errorCode: String, errorInfo: String) {
        AILog.i(TAG, "授权失败, errorcode: $errorCode,errorInfo:$errorInfo")

        DuiAuthManager.scheduleAuth(
          context = applicationContext,
          apiKey = DuiAuthParamsProvider.getApiKey(),
          productId = DuiAuthParamsProvider.getProductId(),
          productKey = DuiAuthParamsProvider.getProductKey(),
          productSecret = DuiAuthParamsProvider.getProductSecret(),
          authTimeout = DuiAuthParamsProvider.getAuthTimeout(),
          customDeviceName = DuiAuthParamsProvider.getCustomDeviceName(),
          authServer = DuiAuthParamsProvider.getAuthServer()
        )
      }
    })
  }
}