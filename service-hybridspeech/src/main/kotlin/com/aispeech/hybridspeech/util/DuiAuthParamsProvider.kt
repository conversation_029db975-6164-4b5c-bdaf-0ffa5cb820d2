package com.aispeech.hybridspeech.util

import android.os.Build
import android.text.TextUtils
import com.aispeech.tablet.core.common.AppSign

object DuiAuthParamsProvider {

  private val keyStoreType: String by lazy {
    AppSign.KEY_STORE
  }

  fun getApiKey(): String {
    return when (keyStoreType) {
      "2" -> "b91ae261a9f5b91ae261a9f5684b97db"
      else -> "b9ee4167ffd8b9ee4167ffd868626398"
    }
  }

  fun getProductId(): String {
    return when (keyStoreType) {
      "2" -> "279630735"
      else -> "279631050"
    }
  }

  fun getProductKey(): String {
    return when (keyStoreType) {
      "2" -> "6d8289e5937ce3c6240d2a9a853bb8ed"
      else -> "16b66b1ee6bcd03054f44dfdf51bacea"
    }
  }

  fun getProductSecret(): String {
    return when (keyStoreType) {
      "2" -> "6c196197235e7378c9ce2c1ca1f40adf"
      else -> "72574917e9f375fce9f85f02883210b5"
    }
  }

  fun getAuthTimeout() = 5000
  fun getAuthServer() = "https://auth.duiopen.com"

  fun getCustomDeviceName(): String {
    return getDeviceUniqueId()
  }

  /**
   * 获取设备 id
   */
  private fun getDeviceUniqueId(): String {
    var deviceId = Build.getSerial()
    if (deviceId.isEmpty() || TextUtils.equals("0123456789ABCDEF", deviceId)) {
      deviceId = com.blankj.utilcode.util.DeviceUtils.getUniqueDeviceId()
    }
    return deviceId
  }
}
