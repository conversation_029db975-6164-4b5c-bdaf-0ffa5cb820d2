package com.aispeech.hybridspeech.worker

import android.content.Context
import androidx.lifecycle.LifecycleOwner
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.Data
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkInfo
import androidx.work.WorkManager
import androidx.work.WorkRequest
import com.aispeech.DUILiteSDK
import com.aispeech.aibase.AILog
import java.util.concurrent.TimeUnit

object DuiAuthManager {

  fun scheduleAuth(
    context: Context,
    apiKey: String,
    productId: String,
    productKey: String,
    productSecret: String,
    authTimeout: Int = 5000,
    customDeviceName: String,
    authServer: String = "https://auth.duiopen.com"
  ) {
    if (DUILiteSDK.isAuthorized(context)) {
      AILog.i(DuiAuthWorker.TAG, "Already authorized. No need to schedule work.")
      return
    }

    val constraints = Constraints.Builder()
      .setRequiredNetworkType(NetworkType.CONNECTED)
      .setRequiresBatteryNotLow(true)
      .build()

    val inputData = Data.Builder()
      .putString("apiKey", apiKey)
      .putString("productId", productId)
      .putString("productKey", productKey)
      .putString("productSecret", productSecret)
      .putInt("authTimeout", authTimeout)
      .putString("customDeviceName", customDeviceName)
      .putString("authServer", authServer)
      .build()

    val authWorkRequest = OneTimeWorkRequestBuilder<DuiAuthWorker>()
      .setConstraints(constraints)
      .setBackoffCriteria(
        BackoffPolicy.EXPONENTIAL,
        WorkRequest.MIN_BACKOFF_MILLIS,
        TimeUnit.MILLISECONDS
      )
      .setInputData(inputData)
      .build()

    WorkManager.getInstance(context).enqueueUniqueWork(
      DuiAuthWorker.UNIQUE_WORK_NAME,
      ExistingWorkPolicy.REPLACE,
      authWorkRequest
    )

    AILog.i(DuiAuthWorker.TAG, "DUI auth work scheduled.")
  }

  fun observeAuthStatus(
    context: Context,
    lifecycleOwner: LifecycleOwner,
    onSuccess: () -> Unit,
    onFailure: () -> Unit,
    onRunning: () -> Unit = {},
  ) {
    WorkManager.getInstance(context)
      .getWorkInfosForUniqueWorkLiveData(DuiAuthWorker.UNIQUE_WORK_NAME)
      .observe(lifecycleOwner) { workInfos ->
        val workInfo = workInfos?.firstOrNull() ?: return@observe
        when (workInfo.state) {
          WorkInfo.State.SUCCEEDED -> {
            AILog.i(DuiAuthWorker.TAG, "Worker SUCCEEDED.")
            onSuccess()
          }

          WorkInfo.State.FAILED -> {
            AILog.e(DuiAuthWorker.TAG, "Worker FAILED.")
            onFailure()
          }

          WorkInfo.State.RUNNING -> {
            AILog.i(DuiAuthWorker.TAG, "Worker RUNNING.")
            onRunning()
          }

          else -> {
            AILog.i(DuiAuthWorker.TAG, "Worker state: ${workInfo.state}")
          }
        }
      }
  }
}
