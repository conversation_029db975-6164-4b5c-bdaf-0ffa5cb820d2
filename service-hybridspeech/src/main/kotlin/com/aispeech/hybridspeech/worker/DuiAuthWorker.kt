package com.aispeech.hybridspeech.worker

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.aispeech.DUILiteConfig
import com.aispeech.DUILiteSDK
import com.aispeech.aibase.AILog
import com.aispeech.export.config.AuthConfig
import com.aispeech.lite.AuthType
import com.aispeech.tablet.core.common.DeviceUtils
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

class DuiAuthWorker(
  appContext: Context,
  workerParams: WorkerParameters
) : CoroutineWorker(appContext, workerParams) {

  companion object {
    const val TAG = "DuiAuthWorker"
    const val UNIQUE_WORK_NAME = "com.aispeech.tablet.DUI_AUTH_WORK"
  }

  override suspend fun doWork(): Result {
    if (DUILiteSDK.isAuthorized(applicationContext)) {
      AILog.i(TAG, "Already authorized. Work finished.")
      return Result.success()
    }

    AILog.i(TAG, "Start DUI auth work. Attempt: $runAttemptCount")

    return try {
      val authSuccess = performAuth()
      if (authSuccess) {
        AILog.i(TAG, "Auth success! Work finished.")
        Result.success()
      } else {
        AILog.e(TAG, "Auth failed. Will retry later.")
        Result.retry()
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Exception in doWork: ${e.message}")
      Result.retry()
    }
  }

  private suspend fun performAuth(): Boolean {
    return suspendCancellableCoroutine { continuation ->
      try {
        val apiKey = inputData.getString("apiKey") ?: ""
        val productId = inputData.getString("productId") ?: ""
        val productKey = inputData.getString("productKey") ?: ""
        val productSecret = inputData.getString("productSecret") ?: ""
        val customDeviceName = inputData.getString("customDeviceName") ?: ""
        val authTimeout = inputData.getInt("authTimeout", 5000)
        val authServer = inputData.getString("authServer") ?: "https://auth.duiopen.com"

        val authConfig = AuthConfig.Builder()
          .setAuthTimeout(authTimeout)
          .setCustomDeviceName(customDeviceName)
          .setType(AuthType.ONLINE)
          .setAuthServer(authServer)
          .create()

        val config = DUILiteConfig.Builder()
          .setApiKey(apiKey)
          .setProductId(productId)
          .setProductKey(productKey)
          .setProductSecret(productSecret)
          .setAuthConfig(authConfig)
          .create()

        val deviceId = DeviceUtils.getSN().ifEmpty {
          com.blankj.utilcode.util.DeviceUtils.getUniqueDeviceId()
        }
        config.setExtraParameter("DEVICE_ID", deviceId)

        DUILiteSDK.doAuth(applicationContext, config, object : DUILiteSDK.InitListener {
          override fun success() {
            if (continuation.isActive) {
              continuation.resume(true)
            }
          }

          override fun error(errorCode: String, errorInfo: String) {
            AILog.e(TAG, "Auth failed: Code=$errorCode, Info=$errorInfo")
            if (continuation.isActive) {
              continuation.resume(false)
            }
          }
        })

        continuation.invokeOnCancellation {
          AILog.w(TAG, "Auth coroutine was cancelled.")
        }

      } catch (e: Exception) {
        AILog.e(TAG, "Exception during performAuth: ${e.message}")
        if (continuation.isActive) {
          continuation.resume(false)
        }
      }
    }
  }
}
