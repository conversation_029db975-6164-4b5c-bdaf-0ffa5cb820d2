package com.aispeech.hybridspeech.session

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.IRecordingSessionCallback
import com.aispeech.hybridspeech.ServiceStatus
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.hybridspeech.core.MainAppController
import com.aispeech.hybridspeech.core.safeCollect
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * 会话事件订阅管理器
 */
class SessionEventSubscriptionManager(
  private val sessionId: String,
  private val sessionScope: CoroutineScope,
  private val mainController: MainAppController,
  private val clientCallback: IRecordingSessionCallback,
  private val onStatusChanged: (String, Int) -> Unit,
) {
  @Suppress("VariableNaming")
  private val TAG = "SessionEventManager_$sessionId"
  
  private var eventSubscriptionJob: Job? = null
  private var isSubscribed = false
  
  /**
   * 开始订阅事件
   */
  fun subscribeToEvents() {
    if (isSubscribed) {
      AILog.w(TAG, "Already subscribed to events")
      return
    }
    
    AILog.i(TAG, "Starting event subscription")
    isSubscribed = true
    
    eventSubscriptionJob = sessionScope.launch {
      // 订阅转写结果
      launch {
        mainController.transcriptionResultFlow.safeCollect(
          tag = TAG,
          onError = { error ->
            AILog.e(TAG, "Error in transcription result flow", error)
            handleFlowError("Transcription flow error: ${error.message}")
          }
        ) { result ->
          handleTranscriptionResult(result)
        }
      }
      
      // 订阅错误
      launch {
        mainController.errorFlow.safeCollect(
          tag = TAG,
          onError = { error ->
            AILog.e(TAG, "Error in error flow", error)
          }
        ) { error ->
          handleError(error)
        }
      }
      
      // 订阅状态变化
      launch {
        mainController.statusChangeFlow.safeCollect(
          tag = TAG,
          onError = { error ->
            AILog.e(TAG, "Error in status change flow", error)
          }
        ) { status ->
          handleStatusChange(status)
        }
      }
    }
  }
  
  /**
   * 停止订阅事件
   */
  fun unsubscribeFromEvents() {
    if (!isSubscribed) {
      AILog.w(TAG, "Not subscribed to events")
      return
    }
    
    AILog.i(TAG, "Stopping event subscription")
    isSubscribed = false
    
    eventSubscriptionJob?.cancel()
    eventSubscriptionJob = null
  }
  
  /**
   * 处理转写结果
   */
  private fun handleTranscriptionResult(result: TranscriptionResult) {
    try {
      AILog.d(TAG, "Received transcription result: ${result::class.simpleName}")
      clientCallback.onTranscriptionUpdate(result)
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling transcription result", e)
      handleError("Failed to process transcription result: ${e.message}")
    }
  }
  
  /**
   * 处理错误
   */
  private fun handleError(error: String) {
    try {
      AILog.e(TAG, "Received error: $error")
      
      // 更新状态为错误，通知客户端
      onStatusChanged(sessionId, ServiceStatus.ERROR)
      clientCallback.onError(0, error)
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling error event", e)
    }
  }
  
  /**
   * 处理状态变化
   */
  private fun handleStatusChange(status: Int) {
    try {
      AILog.d(TAG, "Status changed to: $status")
      onStatusChanged(sessionId, status)
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling status change", e)
    }
  }
  
  /**
   * 处理流错误
   */
  private fun handleFlowError(errorMessage: String) {
    try {
      onStatusChanged(sessionId, ServiceStatus.ERROR)
      clientCallback.onError(0, errorMessage)
    } catch (e: Exception) {
      AILog.e(TAG, "Error notifying flow error", e)
    }
  }
  
  /**
   * 检查是否已订阅
   */
  fun isSubscribed(): Boolean = isSubscribed
}


