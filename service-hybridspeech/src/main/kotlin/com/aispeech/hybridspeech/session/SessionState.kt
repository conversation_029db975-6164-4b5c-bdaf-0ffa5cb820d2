package com.aispeech.hybridspeech.session

import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.ServiceStatus
import kotlinx.serialization.Serializable

/**
 * 会话状态数据类
 */
@Serializable
data class SessionState(
  // 基本信息
  val sessionId: String,
  val status: Int,
  val creationTime: Long,
  val lastUpdateTime: Long,

  // 配置信息
  val config: RecordingConfig?,

  // 恢复相关
  val canRestore: Boolean = true,
  val restoreAttempts: Int = 0,
  val maxRestoreAttempts: Int = 3
) {

  /**
   * 检查会话是否已过期
   */
  fun isExpired(timeoutMs: Long = 30 * 60 * 1000L): <PERSON><PERSON>an {
    return System.currentTimeMillis() - lastUpdateTime > timeoutMs
  }

  /**
   * 检查是否为活跃状态
   */
  fun isActive(): Boolean {
    return status == ServiceStatus.RECORDING ||
      status == ServiceStatus.PAUSED ||
      status == ServiceStatus.PROCESSING
  }
}