package com.aispeech.hybridspeech.audio

import android.annotation.SuppressLint
import android.media.AudioFormat
import android.media.AudioRecord
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.AudioRecordingConfig
import com.aispeech.hybridspeech.audio.bluetooth.BluetoothAudioRouter
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 音频采集硬件接口
 * 专注于音频录制，音频源管理交给 AudioSourceManager
 */
class AudioRecorderDevice : CoroutineScope {
  companion object {
    private const val TAG = "AudioRecorderDevice"
    private const val SAMPLE_RATE = 16000
    private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
    private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
    private const val BUFFER_SIZE_FACTOR = 2
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private var audioRecord: AudioRecord? = null
  private val isRecording = AtomicBoolean(false)
  private val isWaitingForAudioSource = AtomicBoolean(false)
  private var recordingJob: Job? = null
  private var audioSourceMonitorJob: Job? = null

  // 当前配置
  private var currentConfig: AudioRecordingConfig = AudioRecordingConfig.createDefault()

  // 音频源管理器
  private val audioSourceManager = AudioSourceManager.instance

  // 音频源状态回调
  var onAudioSourceChanged: ((AudioSourceInfo) -> Unit)? = null

  private val _pcmDataFlow = MutableSharedFlow<ByteArray>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val pcmDataFlow: SharedFlow<ByteArray> = _pcmDataFlow.asSharedFlow()

  private var bufferSize = AudioRecord.getMinBufferSize(
    SAMPLE_RATE,
    CHANNEL_CONFIG,
    AUDIO_FORMAT
  ) * BUFFER_SIZE_FACTOR

  init {
    // 确保AudioSourceManager已初始化
    audioSourceManager.initialize()
  }

  /**
   * 开始录音
   * @param config 录音配置
   * @return 是否成功启动录音请求
   */
  @SuppressLint("MissingPermission")
  fun startRecordingWithConfig(config: AudioRecordingConfig): Boolean {
    if (isRecording.get()) {
      AILog.w(TAG, "Already recording")
      return true
    }

    if (isWaitingForAudioSource.get()) {
      AILog.w(TAG, "Already waiting for audio source")
      return false
    }

    AILog.i(
      TAG,
      "Starting recording with config: sampleRate=${config.sampleRate}, channels=${config.channels}"
    )
    currentConfig = config

    return try {
      // 启动音频源状态监听
      startAudioSourceMonitoring()

      // 异步设置音频源并启动录音
      setupAudioSourceAndStartRecording(config)

      true
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to start recording", e)
      cleanup()
      false
    }
  }

  /**
   * 停止录音
   */
  fun stopRecording() {
    AILog.i(TAG, "Stopping recording")

    isRecording.set(false)
    isWaitingForAudioSource.set(false)

    // 停止各种任务
    recordingJob?.cancel()
    audioSourceMonitorJob?.cancel()

    // 释放AudioRecord
    releaseAudioRecord()

    // 释放音频源
    audioSourceManager.releaseCurrentSource()
    AILog.i(TAG, "Recording stopped")
  }

  /**
   * 暂停录音（公共方法）
   */
  fun pauseRecording(): Boolean {
    return try {
      if (!isRecording.get()) {
        AILog.w(TAG, "Not recording, cannot pause")
        return false
      }

      pauseRecordingInternal()
      AILog.i(TAG, "Recording paused")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to pause recording", e)
      false
    }
  }

  /**
   * 继续录音（公共方法）
   */
  fun resumeRecording(): Boolean {
    return try {
      if (!isRecording.get()) {
        AILog.w(TAG, "Not recording, cannot resume")
        return false
      }

      resumeRecordingInternal()
      AILog.i(TAG, "Recording resumed")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to resume recording", e)
      false
    }
  }


  /**
   * 关闭设备协程作用域
   */
  fun shutdown(reason: String = "AudioRecorderDevice shutdown") {
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Audio recorder device scope shut down: $reason")
  }

  /**
   * 检查是否正在等待音频源
   */
  fun isWaitingForAudioSource(): Boolean = isWaitingForAudioSource.get()

  /**
   * 检查是否正在录音
   */
  fun isRecording(): Boolean = isRecording.get()

  /**
   * 获取当前音频源信息
   */
  fun getCurrentAudioSourceInfo(): AudioSourceInfo? {
    return audioSourceManager.currentAudioSourceFlow.value
  }

  /**
   * 获取音频配置信息
   */
  fun getAudioConfig(): AudioConfig {
    return AudioConfig(
      sampleRate = currentConfig.sampleRate,
      channels = currentConfig.channels,
      bitsPerSample = currentConfig.bitsPerSample
    )
  }

  /**
   * 销毁设备
   */
  suspend fun destroy() {
    stopRecording()
    shutdown("$TAG destroyed")
    AILog.i(TAG, "AudioRecorderDevice destroyed")
  }

  // =================== 私有方法 ===================

  /**
   * 设置音频源并启动录音
   */
  private fun setupAudioSourceAndStartRecording(config: AudioRecordingConfig) {
    launch {
      try {
        isWaitingForAudioSource.set(true)

        AILog.i(TAG, "Selecting best audio source...")

        // 选择最佳音频源
        val audioSource = audioSourceManager.selectBestAudioSource()

        AILog.i(TAG, "Selected audio source: ${audioSource.getDescription()}")

        // 创建AudioRecord
        if (!createAudioRecord(config, audioSource.getAudioSource())) {
          AILog.e(TAG, "Failed to create AudioRecord")
          throw RuntimeException("Failed to create AudioRecord")
        }

        // 启动录音
        if (!startAudioRecording()) {
          AILog.e(TAG, "Failed to start audio recording")
          releaseAudioRecord()
          throw RuntimeException("Failed to start audio recording")
        }

        // 启动数据采集
        isRecording.set(true)
        recordingJob = launch {
          recordAudio()
        }

        AILog.i(TAG, "Recording started successfully with: ${audioSource.getDescription()}")
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to setup audio source and start recording", e)
        cleanup()
      } finally {
        isWaitingForAudioSource.set(false)
      }
    }
  }

  /**
   * 启动音频源状态监听
   */
  private fun startAudioSourceMonitoring() {
    audioSourceMonitorJob = launch {
      audioSourceManager.currentAudioSourceFlow.collectLatest { sourceInfo ->
        if (sourceInfo != null) {
          AILog.i(TAG, "Audio source changed: ${sourceInfo.description}")
          withContext(Dispatchers.Main) {
            onAudioSourceChanged?.invoke(sourceInfo)
          }
        }
      }
    }
  }

  /**
   * 创建AudioRecord
   */
  @SuppressLint("MissingPermission")
  private fun createAudioRecord(config: AudioRecordingConfig, audioSource: Int): Boolean {
    try {
      // 根据配置计算参数
      val channelConfig =
        if (config.channels == 1) AudioFormat.CHANNEL_IN_MONO else AudioFormat.CHANNEL_IN_STEREO
      val audioFormat = when (config.bitsPerSample) {
        8 -> AudioFormat.ENCODING_PCM_8BIT
        16 -> AudioFormat.ENCODING_PCM_16BIT
        else -> AudioFormat.ENCODING_PCM_16BIT
      }

      val minBufferSize =
        AudioRecord.getMinBufferSize(config.sampleRate, channelConfig, audioFormat)
      if (minBufferSize == AudioRecord.ERROR_BAD_VALUE || minBufferSize == AudioRecord.ERROR) {
        AILog.e(TAG, "Invalid audio configuration")
        return false
      }

      bufferSize = (minBufferSize * config.bufferSizeFactor).toInt()
      AILog.i(TAG, "Creating AudioRecord - source: $audioSource, bufferSize: $bufferSize")

      // 创建AudioRecord
      audioRecord = AudioRecord(
        audioSource,
        config.sampleRate,
        channelConfig,
        audioFormat,
        bufferSize
      )

      val state = audioRecord?.state
      if (state != AudioRecord.STATE_INITIALIZED) {
        AILog.e(TAG, "AudioRecord initialization failed, state: $state")
        releaseAudioRecord()
        return false
      }

      AILog.i(TAG, "AudioRecord created successfully")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Failed to create AudioRecord", e)
      releaseAudioRecord()
      return false
    }
  }

  /**
   * 启动AudioRecord录音
   */
  private fun startAudioRecording(): Boolean {
    return try {
      audioRecord?.startRecording()
      val recordingState = audioRecord?.recordingState

      if (recordingState != AudioRecord.RECORDSTATE_RECORDING) {
        AILog.e(TAG, "Failed to start recording, state: $recordingState")
        false
      } else {
        AILog.i(TAG, "AudioRecord recording started")
        true
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Exception starting AudioRecord", e)
      false
    }
  }

  /**
   * 释放AudioRecord
   */
  private fun releaseAudioRecord() {
    audioRecord?.apply {
      try {
        if (state == AudioRecord.STATE_INITIALIZED &&
          recordingState == AudioRecord.RECORDSTATE_RECORDING) {
          stop()
          AILog.d(TAG, "AudioRecord stopped")
        }
      } catch (e: Exception) {
        AILog.e(TAG, "Error stopping AudioRecord", e)
      } finally {
        try {
          release()
          AILog.d(TAG, "AudioRecord released")
        } catch (e: Exception) {
          AILog.e(TAG, "Error releasing AudioRecord", e)
        }
      }
    }
    audioRecord = null
  }

  /**
   * 暂停录音
   */
  private fun pauseRecordingInternal() {
    audioRecord?.apply {
      try {
        if (state == AudioRecord.STATE_INITIALIZED && recordingState == AudioRecord.RECORDSTATE_RECORDING) {
          stop()
          AILog.d(TAG, "AudioRecord stopped for pause")
        }
      } catch (e: Exception) {
        AILog.e(TAG, "Error stopping AudioRecord for pause", e)
      }
    }

    // 然后取消录音协程
    recordingJob?.cancel()

    // 最后释放AudioRecord资源
    releaseAudioRecord()
    AILog.i(TAG, "Recording paused internally")
  }

  /**
   * 恢复录音
   */
  private fun resumeRecordingInternal() {
    if (audioRecord == null) {
      AILog.d(TAG, "AudioRecord is null, recreating for resume")
      if (!createAudioRecord(currentConfig, audioSourceManager.currentAudioSourceFlow.value?.audioSource ?: android.media.MediaRecorder.AudioSource.DEFAULT)) {
        AILog.e(TAG, "Failed to recreate AudioRecord for resume")
        return
      }
    }

    if (startAudioRecording()) {
      recordingJob = launch {
        recordAudio()
      }
      AILog.i(TAG, "Recording resumed internally")
    } else {
      AILog.e(TAG, "Failed to resume recording internally")
    }
  }

  /**
   * 录音数据采集循环
   */
  private fun recordAudio() {
    val buffer = ByteArray(bufferSize)

    while (isRecording.get() && !Thread.currentThread().isInterrupted) {
      try {
        val audioRecord = this.audioRecord
        if (audioRecord == null || audioRecord.state != AudioRecord.STATE_INITIALIZED) {
          AILog.d(TAG, "AudioRecord is null or not initialized, stopping record loop")
          break
        }

        val bytesRead = audioRecord.read(buffer, 0, buffer.size)

        if (bytesRead > 0) {
          val audioData = buffer.copyOf(bytesRead)
          _pcmDataFlow.tryEmit(audioData)
        } else if (bytesRead < 0) {
          if (handleReadError(bytesRead)) {
            break
          }
        }

      } catch (e: Exception) {
        AILog.e(TAG, "Error reading audio data", e)
        break
      }
    }

    AILog.d(TAG, "Record audio loop ended")
  }

  /**
   * 处理读取的错误
   */
  private fun handleReadError(bytesRead: Int): Boolean {
    return when (bytesRead) {
      AudioRecord.ERROR_INVALID_OPERATION -> {
        AILog.d(TAG, "AudioRecord read returned ERROR_INVALID_OPERATION, likely paused or stopped")
        true
      }
      AudioRecord.ERROR_BAD_VALUE -> {
        AILog.w(TAG, "AudioRecord read returned ERROR_BAD_VALUE")
        true
      }
      AudioRecord.ERROR_DEAD_OBJECT -> {
        AILog.w(TAG, "AudioRecord read returned ERROR_DEAD_OBJECT")
        true
      }
      else -> {
        AILog.w(TAG, "AudioRecord read error: $bytesRead")
        false
      }
    }
  }

  /**
   * 创建音频源信息
   */
  private fun createAudioSourceInfo(source: AudioSource): AudioSourceInfo {
    return AudioSourceInfo(
      type = when (source) {
        is BluetoothAudioSource -> "bluetooth"
        is DefaultAudioSource -> "default"
        else -> "unknown"
      },
      description = source.getDescription(),
      audioSource = source.getAudioSource(),
      isActive = source.isActive(),
      bluetoothDevice = if (source is BluetoothAudioSource) {
        BluetoothAudioRouter.getCurrentDevice()
      } else null
    )
  }

  /**
   * 清理资源
   */
  private fun cleanup() {
    isRecording.set(false)
    isWaitingForAudioSource.set(false)
    recordingJob?.cancel()
    audioSourceMonitorJob?.cancel()
    releaseAudioRecord()

    launch {
      audioSourceManager.releaseCurrentSource()
    }
  }
}

/**
 * 音频配置数据类
 */
data class AudioConfig(
  val sampleRate: Int,
  val channels: Int,
  val bitsPerSample: Int
)
