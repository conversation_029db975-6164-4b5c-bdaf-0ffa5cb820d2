package com.aispeech.hybridspeech.audio

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.AudioRecordingConfig
import com.aispeech.hybridspeech.IRecordProgressCallback
import com.aispeech.hybridspeech.Mp3EncodingConfig
import com.aispeech.hybridspeech.RecordingProgressConfig
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.hybridspeech.core.RecordingProgressManager
import com.aispeech.hybridspeech.core.safeCollect
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.launch

/**
 * 音频管理器
 * 管理音频设备，提供PCM与MP3流，可配置输出格式和参数
 */
class AudioManager: CoroutineScope {
  companion object {
    private const val TAG = "AudioManager"
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val audioRecorderDevice = AudioRecorderDevice()
  private val pcmMasterStream = PcmMasterStream()
  private val mp3Encoder = Mp3Encoder()
  private val progressManager = RecordingProgressManager()

  private var isRunning = false
  private var dataFlowJob: Job? = null

  // 对外暴露的数据流
  val pcmDataFlow: SharedFlow<ByteArray> = pcmMasterStream.pcmDataFlow
  val mp3DataFlow: SharedFlow<ByteArray> = mp3Encoder.mp3DataFlow

  /**
   * 开始音频采集和处理（支持完整配置）
   */
  fun startAudioCaptureWithFullConfig(
    audioConfig: AudioRecordingConfig,
    mp3Config: Mp3EncodingConfig,
    progressConfig: RecordingProgressConfig? = null
  ): Boolean {
    try {
      if (isRunning) {
        AILog.w(TAG, "Audio capture already running")
        return true
      }

      AILog.i(TAG, "Starting audio capture with full config: sampleRate=${audioConfig.sampleRate}, channels=${audioConfig.channels}")
      AILog.i(TAG, "MP3 config: bitRate=${mp3Config.bitRate}, quality=${mp3Config.quality}")

      // 启动音频录制设备（使用配置）
      if (!audioRecorderDevice.startRecordingWithConfig(audioConfig)) {
        AILog.e(TAG, "Failed to start audio recording with config")
        return false
      }

      // 启动MP3编码器（使用配置）
      if (!mp3Encoder.startEncodingWithConfig(mp3Config)) {
        AILog.e(TAG, "Failed to start MP3 encoding with config")
        audioRecorderDevice.stopRecording()
        return false
      }

      // 启动数据流处理
      startDataFlow()

      // 启动录音进度监控
      AILog.d(TAG, "startAudioCaptureWithFullConfig: $progressConfig")
      progressConfig?.let { config ->
        if (config.enabled) {
          progressManager.startProgressMonitoring(pcmDataFlow, audioConfig, config)
          AILog.i(TAG, "Progress monitoring started")
        }
      }

      isRunning = true
      AILog.i(TAG, "Audio capture started successfully with full config")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Error starting audio capture with full config", e)
      return false
    }
  }

  /**
   * 停止音频采集和处理
   */
  fun stopAudioCapture() {
    isRunning = false

    // 停止录音进度监控
    progressManager.stopProgressMonitoring()

    // 停止数据流处理
    dataFlowJob?.cancel()

    // 停止MP3编码器
    mp3Encoder.stopEncoding()

    // 停止音频录制设备
    audioRecorderDevice.stopRecording()

    // 清空PCM缓冲区
    pcmMasterStream.clear()

    AILog.i(TAG, "Audio capture stopped")
  }


  /**
   * 暂停音频采集和处理
   */
  fun pauseAudioCapture(): Boolean {
    return try {
      if (!isRunning) {
        AILog.w(TAG, "Audio capture is not running, cannot pause")
        return false
      }

      // 暂停音频录制设备（这会停止PCM数据流的产生）
      if (!audioRecorderDevice.pauseRecording()) {
        AILog.e(TAG, "Failed to pause audio recorder device")
        return false
      }

      AILog.i(TAG, "Audio capture paused")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to pause audio capture", e)
      false
    }
  }

  /**
   * 继续音频采集和处理
   */
  fun resumeAudioCapture(): Boolean {
    return try {
      if (!isRunning) {
        AILog.w(TAG, "Audio capture is not running, cannot resume")
        return false
      }

      // 继续音频录制设备（这会恢复PCM数据流的产生）
      if (!audioRecorderDevice.resumeRecording()) {
        AILog.e(TAG, "Failed to resume audio recorder device")
        return false
      }

      AILog.i(TAG, "Audio capture resumed")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to resume audio capture", e)
      false
    }
  }

  /**
   * 启动数据流处理
   */
  private fun startDataFlow() {
    dataFlowJob = launch {
      // 监听音频设备的PCM数据
      audioRecorderDevice.pcmDataFlow.safeCollect(
        tag = TAG,
        onError = { error ->
          AILog.e(TAG, "Error in audio data flow", error)
        }
      ) { pcmData ->
        // 将PCM数据送入主流
        pcmMasterStream.feedPcmData(pcmData)

        // 将PCM数据送入MP3编码器
        mp3Encoder.encodePcmData(pcmData)
      }
    }
  }

  /**
   * 关闭音频管理器
   * 包括停止音频采集、清理子组件和关闭协程作用域
   */
  fun shutdown(reason: String = "AudioManager shutdown") {
    // 先停止音频采集
    if (isRunning) {
      stopAudioCapture()
    }

    // 关闭子组件的协程作用域
    mp3Encoder.shutdown("$reason - mp3Encoder")
    audioRecorderDevice.shutdown("$reason - audioRecorderDevice")
    progressManager.unregisterAllProgressCallbacks() // 清理进度管理器的回调

    // 关闭自己的协程作用域
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "Audio manager shut down: $reason")
  }

  /**
   * 获取音频配置
   */
  fun getAudioConfig(): AudioConfig? {
    return audioRecorderDevice.getAudioConfig()
  }

  /**
   * 检查是否正在运行
   */
  fun isRunning(): Boolean {
    return isRunning
  }

  // ============ 录音进度相关方法 ============

  /**
   * 获取当前录音时长
   */
  fun getRecordingDuration(): Long {
    return progressManager.getCurrentDuration()
  }

  /**
   * 注册录音进度回调
   */
  fun registerProgressCallback(callback: IRecordProgressCallback, intervalMs: Int) {
    progressManager.registerProgressCallback(callback, intervalMs)
  }

  /**
   * 取消注册录音进度回调
   */
  fun unregisterProgressCallback(callback: IRecordProgressCallback) {
    progressManager.unregisterProgressCallback(callback)
  }

  /**
   * 取消所有录音进度回调
   */
  fun unregisterAllProgressCallbacks() {
    progressManager.unregisterAllProgressCallbacks()
  }

  /**
   * 检查录音进度监控是否运行
   */
  fun isProgressMonitoringActive(): Boolean {
    return progressManager.isRecording()
  }

  /**
   * 获取已注册的进度回调数量
   */
  fun getProgressCallbackCount(): Int {
    return progressManager.getCallbackCount()
  }
}
