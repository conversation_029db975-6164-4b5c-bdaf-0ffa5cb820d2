package com.aispeech.hybridspeech.audio

import com.aispeech.aibase.AILog
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow

/**
 * 原始PCM主数据流
 * 负责接收来自AudioRecorderDevice的PCM数据，并分发给各个消费者
 */
class PcmMasterStream {
  companion object {
    private const val TAG = "PcmMasterStream"
  }

  private val _pcmDataFlow = MutableSharedFlow<ByteArray>(
    replay = 0,
    extraBufferCapacity = 200
  )
  val pcmDataFlow: SharedFlow<ByteArray> = _pcmDataFlow.asSharedFlow()

  /**
   * 接收PCM数据
   */
  fun feedPcmData(data: ByteArray) {
    try {
      _pcmDataFlow.tryEmit(data)
    } catch (e: Exception) {
      AILog.e(TAG, "Error feeding PCM data", e)
    }
  }

  /**
   * 清空流状态（如果需要重置）
   */
  fun clear() {
    AILog.i(TAG, "PCM stream cleared")
  }
}

