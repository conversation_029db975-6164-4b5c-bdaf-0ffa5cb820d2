package com.aispeech.hybridspeech.audio

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.Mp3EncodingConfig
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import java.nio.ByteBuffer
import java.nio.ByteOrder
import java.util.concurrent.ConcurrentLinkedQueue
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * MP3编码器
 * 将PCM数据编码为MP3格式，使用LAME库进行实际编码
 */
class Mp3Encoder: CoroutineScope {
  companion object {
    private const val TAG = "Mp3Encoder"
    private const val SAMPLE_RATE = 16000
    private const val CHANNELS = 1
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  private val _mp3DataFlow = MutableSharedFlow<ByteArray>(
    replay = 0,
    extraBufferCapacity = 100
  )
  val mp3DataFlow: SharedFlow<ByteArray> = _mp3DataFlow.asSharedFlow()

  private var isEncoding = false
  private var encodingJob: Job? = null
  private val pcmQueue = ConcurrentLinkedQueue<ByteArray>()
  private var currentConfig: Mp3EncodingConfig? = null

  /**
   * 开始编码（支持配置）
   */
  fun startEncodingWithConfig(config: Mp3EncodingConfig): Boolean {
    try {
      if (isEncoding) {
        AILog.w(TAG, "Already encoding")
        return true
      }

      currentConfig = config

      // 初始化LAME编码器，使用配置参数
      // 将kbps转换为LAME的bitRate参数（kbps）
      val bitRateKbps = config.bitRate / 1000
      LameHelper.createWithParam(SAMPLE_RATE, CHANNELS, bitRateKbps, config.quality)
      isEncoding = true

      encodingJob = launch {
        encodeLoop()
      }

      AILog.i(TAG, "MP3 encoding started with LAME - bitRate: ${config.bitRate}, quality: ${config.quality}")
      return true

    } catch (e: Exception) {
      AILog.e(TAG, "Failed to start MP3 encoding with config", e)
      return false
    }
  }

  /**
   * 关闭编码器协程作用域
   * 取消所有子协程，但不影响其他模块
   */
  fun shutdown(reason: String = "Mp3Encoder shutdown") {
    scopeDelegate.shutdown(reason)
    AILog.i(TAG, "MP3 encoder scope shut down: $reason")
  }

  /**
   * 停止编码
   */
  fun stopEncoding() {
    isEncoding = false
    encodingJob?.cancel()

    // 处理剩余的PCM数据
    flushEncoder()

    // 销毁LAME编码器
    LameHelper.destroy()

    pcmQueue.clear()
    AILog.i(TAG, "MP3 encoding stopped")
  }

  /**
   * 输入PCM数据进行编码
   */
  fun encodePcmData(pcmData: ByteArray) {
    if (isEncoding) {
      pcmQueue.offer(pcmData)
    }
  }

  /**
   * 编码循环
   */
  private suspend fun encodeLoop() {
    while (isEncoding && !Thread.currentThread().isInterrupted) {
      try {
        val pcmData = pcmQueue.poll()
        if (pcmData != null) {
          // 将ByteArray转换为ShortArray（PCM 16位数据）
          val shortArray = convertByteArrayToShortArray(pcmData)

          // 使用LAME编码器进行编码
          val latch = CountDownLatch(1)
          var mp3Data: ByteArray? = null
          var mp3Size = 0

          LameHelper.encoder(shortArray) { buffer, size ->
            if (size > 0) {
              mp3Data = buffer.copyOf(size)
              mp3Size = size
            }
            latch.countDown()
          }

          // 等待编码完成
          if (latch.await(1000, TimeUnit.MILLISECONDS)) {
            mp3Data?.let { data ->
              if (mp3Size > 0) {
                _mp3DataFlow.tryEmit(data)
              }
            }
          } else {
            AILog.w(TAG, "Encoding timeout")
          }
        } else {
          delay(10) // 等待新数据
        }
      } catch (e: Exception) {
        AILog.e(TAG, "Error in encoding loop", e)
        break
      }
    }
  }


  /**
   * 刷新编码器，处理剩余数据
   */
  private fun flushEncoder() {
    try {
      // 处理队列中剩余的PCM数据
      while (pcmQueue.isNotEmpty()) {
        val pcmData = pcmQueue.poll()
        if (pcmData != null) {
          val shortArray = convertByteArrayToShortArray(pcmData)
          val latch = CountDownLatch(1)

          LameHelper.encoder(shortArray) { buffer, size ->
            if (size > 0) {
              _mp3DataFlow.tryEmit(buffer.copyOf(size))
            }
            latch.countDown()
          }

          latch.await(1000, TimeUnit.MILLISECONDS)
        }
      }

      // 获取编码器的最终数据
      val latch = CountDownLatch(1)
      LameHelper.flush { buffer, size ->
        if (size > 0) {
          _mp3DataFlow.tryEmit(buffer.copyOf(size))
        }
        latch.countDown()
      }
      latch.await(1000, TimeUnit.MILLISECONDS)

    } catch (e: Exception) {
      AILog.e(TAG, "Error flushing encoder", e)
    }
  }

  /**
   * 将ByteArray转换为ShortArray
   * PCM数据通常是16位，需要转换为Short数组
   */
  private fun convertByteArrayToShortArray(byteArray: ByteArray): ShortArray {
    val shortArray = ShortArray(byteArray.size / 2)
    val byteBuffer = ByteBuffer.wrap(byteArray)
    byteBuffer.order(ByteOrder.LITTLE_ENDIAN)

    for (i in shortArray.indices) {
      shortArray[i] = byteBuffer.short
    }

    return shortArray
  }
}
