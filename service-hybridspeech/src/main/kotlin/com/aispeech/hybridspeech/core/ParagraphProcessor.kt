package com.aispeech.hybridspeech.core

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.TranscriptionResult
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * 段落统计的数据类
 */
data class ParagraphStatistics(
  var sentenceNum: Int = 0,
  var end: Long? = null,
  var recNum: Int = 0,
  var tokenNum: Int = 0,
  var tokenNumSmooth: Int = 0,
  var isSentenceComplete: Boolean = false,
  // 添加调试信息
  var currentParagraphContent: StringBuilder = StringBuilder(),
  var lastProcessedText: String = "",
  var createdTime: Long = System.currentTimeMillis()
) {

  companion object {
    /** 一次段落最多 8 句 */
    private const val PARAGRAPH_SENTENCE_NUM = 8

    /** 判定新段落的最大停顿 */
    private const val MAX_PAUSE_MS = 5_000

    /** 句末标点 */
    private val SENTENCE_END = setOf("。", "？", ".", "?")

    /** 口语顺滑标记 */
    private val SMOOTH_REGEX = "#<#.*?#>#".toRegex()

    const val TAG = "ParagraphStatistics"
  }

  /**
   * 本条 ASR 加进来以后是否要分段
   */
  fun shouldLineFeed(result: TranscriptionResult.ProcessingTextResult, maxWords: Int): Boolean {
    val tooManySentences = sentenceNum >= PARAGRAPH_SENTENCE_NUM
    val tooLongPause =
      end?.let { (result.begin - it) >= MAX_PAUSE_MS } ?: false
    val tooManyWords = tokenNum >= maxWords && isSentenceComplete

    val shouldFeed = tooManySentences || tooLongPause || tooManyWords

    // 只记录分段决策的关键日志
    if (shouldFeed) {
      val pauseDuration = end?.let { result.begin - it }
      AILog.i(TAG, "分段决策: 需要分段 - " +
        "句子数过多:$tooManySentences($sentenceNum>=$PARAGRAPH_SENTENCE_NUM), " +
        "停顿过长:$tooLongPause(${pauseDuration}ms>=$MAX_PAUSE_MS), " +
        "字数过多:$tooManyWords($tokenNum>=${maxWords}且句子完整:$isSentenceComplete)")
    }

    return shouldFeed
  }

  /** 把一条 ASR 计入统计 */
  operator fun plusAssign(result: TranscriptionResult.ProcessingTextResult) {
    end = result.end
    recNum++
    tokenNum += result.text.length

    // 计算 smooth token 数时不再整体 replace，节省一次字符串创建
    val smoothChars = SMOOTH_REGEX.findAll(result.text).sumOf { it.value.length }
    tokenNumSmooth += result.text.length - smoothChars

    val newSentences = result.text.count { SENTENCE_END.contains(it.toString()) }
    sentenceNum += newSentences
    isSentenceComplete = SENTENCE_END.any { result.text.endsWith(it) }

    // 更新段落内容缓存
    if (currentParagraphContent.isNotEmpty()) {
      currentParagraphContent.append(" ")
    }
    currentParagraphContent.append(result.text)
    lastProcessedText = result.text
  }

  /**
   * 重置段落
   */
  fun reset() {
    val oldContent = currentParagraphContent.toString()
    val duration = System.currentTimeMillis() - createdTime

    // 只记录段落完成的关键信息
    if (oldContent.isNotEmpty()) {
      AILog.i(TAG, "段落完成: " +
        "统计(句:$sentenceNum, 字:$tokenNum, 记录:$recNum), 持续时间:${duration}ms")
    }

    sentenceNum = 0
    end = null
    recNum = 0
    tokenNum = 0
    tokenNumSmooth = 0
    isSentenceComplete = false
    currentParagraphContent.clear()
    lastProcessedText = ""
    createdTime = System.currentTimeMillis()

    AILog.i("ParagraphStatistics", "统计已重置，开始新段落")
  }
}


class ParagraphProcessor(private val maxParagraphWords: Int = 150) {

  companion object {
    private const val TAG = "ParagraphProcessor"
  }

  private val stat = ParagraphStatistics()
  private val lock = Mutex() // 使用协程锁保证状态更新的原子性

  // 添加段落历史记录，用于调试
  private val paragraphHistory = mutableListOf<ParagraphHistoryItem>()
  private var paragraphCounter = 0

  /**
   * 处理传入的 ASR 结果，并返回一个可能带有换行标记的新结果。
   * 这是一个纯粹的转换函数。
   *
   * @param currentResult 当前收到的 ASR 结果。
   * @return 返回一个 ProcessingTextResult，如果需要分段，其 lineFeed 会被设为 1。
   */
  suspend fun process(currentResult: TranscriptionResult.ProcessingTextResult): TranscriptionResult.ProcessingTextResult {
    // 空文本直接返回，不参与统计
    if (currentResult.text.isBlank()) {
      AILog.w(TAG, "空文本，直接返回: '${currentResult.text}'")
      return currentResult
    }

    // 使用 withLock 保证在并发调用时，对 stat 的读写是线程安全的
    return lock.withLock {
      AILog.i(TAG, "处理ASR结果: '${currentResult.text}', 当前段落状态: ${getCurrentStatus()}")

      // 1. 判断是否需要换行
      val needLF = stat.shouldLineFeed(currentResult, maxParagraphWords)

      // 2. 根据判断结果，决定最终返回的对象
      val resultToReturn = if (needLF) {
        paragraphCounter++
        AILog.i(TAG, "分段决策: 需要换行。段落#$paragraphCounter 完成，重置统计数据。")

        // 保存当前段落到历史记录
        saveParagraphToHistory()

        // 先重置统计，为新段落做准备
        stat.reset()
        // 创建一个带有换行标记的新对象
        currentResult.copy(lineFeed = 1)
      } else {
        AILog.v(TAG, "分段决策: 继续当前段落")
        // 不需要换行，直接返回原始对象
        currentResult
      }

      // 3. 将当前结果计入统计（计入旧段落或新段落的第一句）
      stat += currentResult

      AILog.i(TAG, "处理完成: lineFeed=${resultToReturn.lineFeed}, 新状态: ${getCurrentStatus()}")

      // 4. 返回处理后的结果
      resultToReturn
    }
  }

  /**
   * 如果外部需要重置处理器的状态
   */
  suspend fun resetState() {
    lock.withLock {
      // 保存当前段落到历史记录（如果有内容）
      if (stat.currentParagraphContent.isNotEmpty()) {
        saveParagraphToHistory()
      }

      stat.reset()
      paragraphCounter = 0
      AILog.i(TAG, "处理器状态已重置，历史段落数: ${paragraphHistory.size}")

      // 清理过多的历史记录，只保留最近的50个段落
      if (paragraphHistory.size > 50) {
        val removed = paragraphHistory.size - 50
        paragraphHistory.subList(0, removed).clear()
        AILog.i(TAG, "清理了 $removed 个历史段落记录")
      }
    }
  }

  /**
   * 保存当前段落到历史记录
   */
  private fun saveParagraphToHistory() {
    if (stat.currentParagraphContent.isNotEmpty()) {
      val historyItem = ParagraphHistoryItem(
        id = paragraphCounter,
        content = stat.currentParagraphContent.toString(),
        sentenceCount = stat.sentenceNum,
        tokenCount = stat.tokenNum,
        recordCount = stat.recNum,
        duration = System.currentTimeMillis() - stat.createdTime,
        timestamp = System.currentTimeMillis()
      )
      paragraphHistory.add(historyItem)
      AILog.i(TAG, "段落#${paragraphCounter} 已保存到历史: ${historyItem}")
    }
  }

  /**
   * 获取当前处理状态的字符串描述
   */
  private fun getCurrentStatus(): String {
    return "句:${stat.sentenceNum}, 字:${stat.tokenNum}, 记录:${stat.recNum}, " +
      "完整:${stat.isSentenceComplete}, 内容长度:${stat.currentParagraphContent.length}"
  }
}

/**
 * 段落历史记录项
 */
data class ParagraphHistoryItem(
  val id: Int,
  val content: String,
  val sentenceCount: Int,
  val tokenCount: Int,
  val recordCount: Int,
  val duration: Long,
  val timestamp: Long
) {
  override fun toString(): String {
    return "段落#$id: '$content' (句:$sentenceCount, 字:$tokenCount, 时长:${duration}ms)"
  }
}
