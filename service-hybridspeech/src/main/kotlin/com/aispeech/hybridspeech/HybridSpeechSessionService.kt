package com.aispeech.hybridspeech

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.hybridspeech.session.RecordingSessionImpl
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.UUID
import java.util.concurrent.ConcurrentHashMap

/**
 * 基于会话（Session）模式的混合语音识别服务
 * 通过工厂模式创建和管理独立的录音会话。
 */
class HybridSpeechSessionService : Service() {

  companion object {
    private const val TAG = "HybridSpeechSessionSvc"
    private const val NOTIFICATION_ID = 1002
    private const val CHANNEL_ID = "hybrid_speech_session_channel"
    private const val SESSION_TIMEOUT_MS = 30 * 60 * 1000L
    private const val MAX_CONCURRENT_SESSIONS = 10
  }

  // 用于管理所有活跃的会话
  private val activeSessions = ConcurrentHashMap<String, RecordingSessionImpl>()

  // 全局配置提供者，由所有会话共享
  private var configProvider: IHybridSpeechConfigProvider? = null

  // 服务级别的协程作用域管理
  private val serviceScopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = "HybridSpeechSessionService-${this.hashCode()}",
    parentScope = CoroutineScopeManager.getMainParentScope()
  )
  private val serviceScope: CoroutineScope = serviceScopeDelegate

  // 会话监控和清理任务
  private var sessionMonitorJob: Job? = null
  private var notificationUpdateJob: Job? = null

  /**
   * 这是暴露给客户端的 Binder 对象，实现了会话工厂接口。
   */
  private val factoryBinder = object : IHybridSpeechSessionFactory.Stub() {
    override fun createRecordingSession(config: RecordingConfig, callback: IRecordingSessionCallback) {
      val sessionId = UUID.randomUUID().toString()
      AILog.i(TAG, "Request to create new session with ID: $sessionId")

      // 检查并发会话数限制
      if (activeSessions.size >= MAX_CONCURRENT_SESSIONS) {
        AILog.w(TAG, "Maximum concurrent sessions reached: ${activeSessions.size}")
        try {
          callback.onSessionCreateFailed("Maximum concurrent sessions reached: $MAX_CONCURRENT_SESSIONS")
        } catch (e: Exception) {
          AILog.e(TAG, "Failed to notify client about session limit", e)
        }
        return
      }

      try {
        // session 实现类
        val sessionImpl = RecordingSessionImpl(
          context = applicationContext,
          sessionId = sessionId,
          clientCallback = callback,
          onRelease = { id ->
            AILog.i(TAG, "Session $id released, removing from active list.")
            activeSessions.remove(id)
          },
          onStatusChanged = { id, status ->
            AILog.i(TAG, "Session $id status changed = $status")
          }
        )

        activeSessions[sessionId] = sessionImpl

        val sessionStub = createSessionStub(sessionId)
        sessionImpl.start(config, configProvider)

        callback.onSessionCreated(sessionStub)
        AILog.i(TAG, "Session $sessionId created and returned to client. Active sessions: ${activeSessions.size}")
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to create session $sessionId", e)
        activeSessions.remove(sessionId)
        try {
          callback.onSessionCreateFailed("Failed to create session: ${e.message}")
        } catch (re: Exception) {
          AILog.e(TAG, "Failed to notify client about session creation failure", re)
        }
      }
    }

    override fun registerConfigProvider(provider: IHybridSpeechConfigProvider?) {
      AILog.i(TAG, "Global config provider registered.")
      <EMAIL> = provider
    }

    override fun unregisterConfigProvider() {
      AILog.i(TAG, "Global config provider unregistered.")
      <EMAIL> = null
    }
  }

  /**
   * 为指定的 sessionId 创建一个 IRecordingSession.Stub 实例。
   */
  private fun createSessionStub(sessionId: String): IRecordingSession.Stub {
    return object : IRecordingSession.Stub() {
      private fun getSession(): RecordingSessionImpl? {
        val session = activeSessions[sessionId]
        if (session == null) {
          AILog.w(TAG, "Attempted to operate on a released or non-existent session: $sessionId")
        }
        return session
      }

      override fun pause() { getSession()?.pause() }
      override fun resume() { getSession()?.resume() }
      override fun stop() { getSession()?.stop() }
      override fun getStatus(): Int = getSession()?.getStatus() ?: ServiceStatus.IDLE // 返回默认状态
      override fun getRecordingDuration(): Long = getSession()?.getRecordingDuration() ?: 0L
      override fun release() { getSession()?.release() }
    }
  }

  @SuppressLint("InlinedApi")
  override fun onCreate() {
    super.onCreate()
    AILog.i(TAG, "Service onCreate")

    try {
      createNotificationChannel()

      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        startForeground(
          NOTIFICATION_ID,
          createNotification(),
          android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE
        )
      } else {
        startForeground(NOTIFICATION_ID, createNotification())
      }

      // 启动会话监控和清理任务
      startSessionMonitoring()

      AILog.i(TAG, "Service started in foreground and monitoring initialized.")
    } catch (e: Exception) {
      AILog.e(TAG, "Service onCreate failed", e)
      throw e
    }
  }

  override fun onBind(intent: Intent?): IBinder {
    AILog.i(TAG, "Service onBind, returning session factory binder.")
    return factoryBinder
  }

  override fun onUnbind(intent: Intent?): Boolean {
    AILog.i(TAG, "Service onUnbind. Active sessions: ${activeSessions.size}")
    return super.onUnbind(intent)
  }

  override fun onDestroy() {
    AILog.i(TAG, "Service onDestroy")

    // 停止监控任务
    sessionMonitorJob?.cancel()
    notificationUpdateJob?.cancel()

    // 清理所有仍然活跃的会话
    AILog.i(TAG, "Releasing all active sessions...")
    val sessionCount = activeSessions.size
    activeSessions.values.forEach { it.release() }
    activeSessions.clear()
    AILog.i(TAG, "Released $sessionCount sessions")

    // 清理协程作用域
    serviceScopeDelegate.shutdown()
    AILog.i(TAG, "USER_PRESENT broadcast receiver unregistered.")

    super.onDestroy()
  }

  // --- 通知相关代码 (与你原有的实现基本一致) ---

  private fun createNotificationChannel() {
    val channel = NotificationChannel(
      CHANNEL_ID,
      "Hybrid Speech Session Service",
      NotificationManager.IMPORTANCE_LOW
    ).apply {
      description = "Manages hybrid speech recognition sessions"
      setShowBadge(false)
    }
    getSystemService(NotificationManager::class.java).createNotificationChannel(channel)
  }

  private fun createNotification(): Notification {
    return NotificationCompat.Builder(this, CHANNEL_ID)
      .setContentTitle("混合语音服务")
      .setContentText("活跃会话")
      .setSmallIcon(android.R.drawable.ic_btn_speak_now)
      .setOngoing(true)
      .setShowWhen(false)
      .build()
  }

  private fun updateNotification() {
    val notification = createNotification()
    getSystemService(NotificationManager::class.java).notify(NOTIFICATION_ID, notification)
  }

  private fun startSessionMonitoring() {
    sessionMonitorJob = serviceScope.launch {
      while (true) {
        delay(60_000)

        try {
          cleanupExpiredSessions()
          logSessionStatistics()
        } catch (e: Exception) {
          AILog.e(TAG, "Error in session monitoring", e)
        }
      }
    }
  }

  private fun cleanupExpiredSessions() {
    val currentTime = System.currentTimeMillis()
    val expiredSessions = mutableListOf<String>()

    activeSessions.forEach { (sessionId, session) ->
      if (currentTime - session.getCreationTime() > SESSION_TIMEOUT_MS) {
        expiredSessions.add(sessionId)
      }
    }

    expiredSessions.forEach { sessionId ->
      activeSessions[sessionId]?.let { session ->
        try {
          session.release()
        } catch (e: Exception) {
          AILog.e(TAG, "Error releasing expired session $sessionId", e)
        }
      }
    }

    if (expiredSessions.isNotEmpty()) {
      AILog.i(TAG, "Cleaned up ${expiredSessions.size} expired sessions")
    }
  }

  private fun logSessionStatistics() {
    if (activeSessions.isNotEmpty()) {
      val statusCounts = activeSessions.values.groupingBy { it.getStatus() }.eachCount()
      AILog.d(TAG, "Session statistics - Active: ${activeSessions.size}, Status counts: $statusCounts")
    }
  }
}