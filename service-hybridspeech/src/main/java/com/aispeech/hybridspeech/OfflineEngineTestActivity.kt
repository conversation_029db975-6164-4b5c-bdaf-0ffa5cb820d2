package com.aispeech.hybridspeech

import android.Manifest
import android.app.Activity
import android.content.pm.PackageManager
import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.aispeech.hybridspeech.asr.offline.OfflineEngine
import com.aispeech.hybridspeech.asr.offline.OfflineEngineStatus
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 离线引擎测试Activity
 * 专门用于测试OfflineEngine的各项功能
 */
class OfflineEngineTestActivity : Activity() {

  companion object {
    private const val TAG = "OfflineEngineTest"
    private const val PERMISSION_REQUEST_CODE = 1001
    private val REQUIRED_PERMISSIONS = arrayOf(
      Manifest.permission.RECORD_AUDIO,
      Manifest.permission.WRITE_EXTERNAL_STORAGE,
      Manifest.permission.READ_EXTERNAL_STORAGE
    )
  }

  // UI组件
  private lateinit var tvInitStatus: TextView
  private lateinit var tvRunningStatus: TextView
  private lateinit var tvModelPath: TextView
  private lateinit var tvBufferSize: TextView
  private lateinit var etModelPath: EditText
  private lateinit var btnSetModelPath: Button
  private lateinit var btnInitialize: Button
  private lateinit var btnStart: Button
  private lateinit var btnStop: Button
  private lateinit var btnRelease: Button
  private lateinit var btnSendTestData: Button
  private lateinit var btnSendContinuousData: Button
  private lateinit var btnClearResults: Button
  private lateinit var btnClearLogs: Button
  private lateinit var tvTranscriptionResults: TextView
  private lateinit var tvDebugLogs: TextView

  // 离线引擎
  private lateinit var offlineEngine: OfflineEngine

  // 状态
  private var isContinuousDataSending = false
  private val dateFormat = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault())

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    createUI()

    initOfflineEngine()
    setupClickListeners()
    checkPermissions()

    appendLog("离线引擎测试页面初始化完成")
    updateUI()
  }

  private fun createUI() {
    // 创建主滚动视图
    val scrollView = ScrollView(this)

    val layout = LinearLayout(this).apply {
      orientation = LinearLayout.VERTICAL
      setPadding(32, 32, 32, 32)
    }

    // 标题
    val titleText = TextView(this).apply {
      text = "离线引擎测试"
      textSize = 24f
      setPadding(0, 0, 0, 32)
    }
    layout.addView(titleText)

    // 引擎状态
    val statusLayout = LinearLayout(this).apply {
      orientation = LinearLayout.VERTICAL
      setPadding(0, 0, 0, 16)
    }

    val statusTitle = TextView(this).apply {
      text = "引擎状态"
      textSize = 18f
      setPadding(0, 0, 0, 8)
    }
    statusLayout.addView(statusTitle)

    tvInitStatus = TextView(this).apply {
      text = "初始化状态: 未初始化"
      textSize = 14f
      setPadding(0, 0, 0, 4)
    }
    statusLayout.addView(tvInitStatus)

    tvRunningStatus = TextView(this).apply {
      text = "运行状态: 已停止"
      textSize = 14f
      setPadding(0, 0, 0, 4)
    }
    statusLayout.addView(tvRunningStatus)

    tvModelPath = TextView(this).apply {
      text = "模型路径: 未设置"
      textSize = 14f
      setPadding(0, 0, 0, 4)
    }
    statusLayout.addView(tvModelPath)

    tvBufferSize = TextView(this).apply {
      text = "缓冲区大小: 0 bytes"
      textSize = 14f
      setPadding(0, 0, 0, 8)
    }
    statusLayout.addView(tvBufferSize)

    layout.addView(statusLayout)

    // 配置区域
    val configTitle = TextView(this).apply {
      text = "引擎配置"
      textSize = 18f
      setPadding(0, 16, 0, 8)
    }
    layout.addView(configTitle)

    etModelPath = EditText(this).apply {
      hint = "/sdcard/hybrid_speech_debug/offline_model"
      setPadding(8, 8, 8, 8)
    }
    layout.addView(etModelPath)

    btnSetModelPath = Button(this).apply {
      text = "设置模型路径"
    }
    layout.addView(btnSetModelPath)

    btnInitialize = Button(this).apply {
      text = "初始化引擎"
      isEnabled = true
    }
    layout.addView(btnInitialize)

    // 控制按钮
    val controlTitle = TextView(this).apply {
      text = "引擎控制"
      textSize = 18f
      setPadding(0, 16, 0, 8)
    }
    layout.addView(controlTitle)

    val controlLayout = LinearLayout(this).apply {
      orientation = LinearLayout.HORIZONTAL
    }

    btnStart = Button(this).apply {
      text = "启动引擎"
      isEnabled = false
    }
    controlLayout.addView(btnStart)

    btnStop = Button(this).apply {
      text = "停止引擎"
      isEnabled = false
    }
    controlLayout.addView(btnStop)

    layout.addView(controlLayout)

    btnRelease = Button(this).apply {
      text = "释放引擎"
      isEnabled = false
    }
    layout.addView(btnRelease)

    // 测试数据
    val testTitle = TextView(this).apply {
      text = "测试数据"
      textSize = 18f
      setPadding(0, 16, 0, 8)
    }
    layout.addView(testTitle)

    btnSendTestData = Button(this).apply {
      text = "发送模拟PCM数据"
      isEnabled = false
    }
    layout.addView(btnSendTestData)

    btnSendContinuousData = Button(this).apply {
      text = "连续发送测试数据"
      isEnabled = false
    }
    layout.addView(btnSendContinuousData)

    // 结果显示
    val resultTitle = TextView(this).apply {
      text = "转写结果"
      textSize = 18f
      setPadding(0, 16, 0, 8)
    }
    layout.addView(resultTitle)

    btnClearResults = Button(this).apply {
      text = "清空结果"
    }
    layout.addView(btnClearResults)

    tvTranscriptionResults = TextView(this).apply {
      text = "等待转写结果..."
      textSize = 12f
      setPadding(8, 8, 8, 8)
      setBackgroundColor(0xFFF5F5F5.toInt())
      minHeight = 200
      maxLines = 10
    }
    layout.addView(tvTranscriptionResults, LinearLayout.LayoutParams(
      LinearLayout.LayoutParams.MATCH_PARENT,
      LinearLayout.LayoutParams.WRAP_CONTENT
    ))

    // 日志显示
    val logTitle = TextView(this).apply {
      text = "调试日志"
      textSize = 18f
      setPadding(0, 16, 0, 8)
    }
    layout.addView(logTitle)

    btnClearLogs = Button(this).apply {
      text = "清空日志"
    }
    layout.addView(btnClearLogs)

    tvDebugLogs = TextView(this).apply {
      text = "等待日志输出..."
      textSize = 12f
      setPadding(8, 8, 8, 8)
      setBackgroundColor(0xFFF5F5F5.toInt())
      minHeight = 300
      maxLines = 15
    }
    layout.addView(tvDebugLogs, LinearLayout.LayoutParams(
      LinearLayout.LayoutParams.MATCH_PARENT,
      LinearLayout.LayoutParams.WRAP_CONTENT
    ))

    // 将布局添加到滚动视图中
    scrollView.addView(layout)
    setContentView(scrollView)
  }

  private fun initOfflineEngine() {
    offlineEngine = OfflineEngine()

    // 设置状态变化监听器
    offlineEngine.setStatusChangeListener { status ->
      runOnUiThread {
        appendLog("引擎状态变化: 状态=${status.engineState::class.simpleName}, 初始化=${status.isInitialized}, 初始化中=${status.isInitializing}, 运行中=${status.isRunning}")
        updateUI()

        // 如果初始化完成，显示成功消息
        if (status.isInitialized && !status.isInitializing) {
          appendLog("✅ 引擎初始化成功，现在可以启动引擎了")
        }
      }
    }
  }

  private fun setupClickListeners() {
    btnSetModelPath.setOnClickListener {
      val path = etModelPath.text.toString().trim()
      if (path.isNotEmpty()) {
        offlineEngine.setModelPath(path)
        appendLog("设置自定义模型路径: $path")
        updateUI()
      } else {
        appendLog("请输入有效的模型路径")
      }
    }

    btnInitialize.setOnClickListener {
      appendLog("开始初始化离线引擎...")

      // 如果用户设置了自定义路径，先设置路径
      val customPath = etModelPath.text.toString().trim()
      if (customPath.isNotEmpty()) {
        offlineEngine.setModelPath(customPath)
        appendLog("使用自定义模型路径: $customPath")
      } else {
        appendLog("使用默认模型路径")
      }

      val success = offlineEngine.initialize()
      if (success) {
        appendLog("初始化请求已发送，等待onInit回调...")
        btnInitialize.isEnabled = false
        btnInitialize.text = "初始化中..."
      } else {
        appendLog("初始化请求失败")
      }
      updateUI()
    }

    btnStart.setOnClickListener {
      appendLog("启动离线引擎...")
      val success = offlineEngine.start()
      if (success) {
        appendLog("离线引擎启动成功")
      } else {
        appendLog("离线引擎启动失败")
      }
      updateUI()
    }

    btnStop.setOnClickListener {
      appendLog("停止离线引擎...")
      offlineEngine.stop()
      appendLog("离线引擎已停止")
      updateUI()
    }

    btnRelease.setOnClickListener {
      appendLog("释放离线引擎...")
      offlineEngine.release()
      appendLog("离线引擎已释放")
      updateUI()
    }

    btnSendTestData.setOnClickListener {
      sendTestPcmData()
    }

    btnSendContinuousData.setOnClickListener {
      if (isContinuousDataSending) {
        stopContinuousDataSending()
      } else {
        startContinuousDataSending()
      }
    }

    btnClearResults.setOnClickListener {
      tvTranscriptionResults.text = "等待转写结果..."
    }

    btnClearLogs.setOnClickListener {
      tvDebugLogs.text = "等待日志输出..."
    }
  }

  private fun checkPermissions() {
    val missingPermissions = REQUIRED_PERMISSIONS.filter {
      ContextCompat.checkSelfPermission(this, it) != PackageManager.PERMISSION_GRANTED
    }

    if (missingPermissions.isNotEmpty()) {
      appendLog("请求权限: ${missingPermissions.joinToString(", ")}")
      ActivityCompat.requestPermissions(
        this,
        missingPermissions.toTypedArray(),
        PERMISSION_REQUEST_CODE
      )
    } else {
      appendLog("所有权限已授予")
    }
  }

  override fun onRequestPermissionsResult(
    requestCode: Int,
    permissions: Array<out String>,
    grantResults: IntArray
  ) {
    super.onRequestPermissionsResult(requestCode, permissions, grantResults)
    if (requestCode == PERMISSION_REQUEST_CODE) {
      val granted = grantResults.all { it == PackageManager.PERMISSION_GRANTED }
      if (granted) {
        appendLog("权限授予成功")
      } else {
        appendLog("权限授予失败，部分功能可能无法使用")
      }
    }
  }

  private fun updateUI() {
    val status = offlineEngine.getStatus()
    updateEngineStatus(status)
    updateButtonStates(status)
  }

  private fun updateEngineStatus(status: OfflineEngineStatus) {
    // 初始化状态
    val initStatusText = when {
      status.isInitializing -> "初始化中..."
      status.isInitialized -> "已初始化"
      else -> "未初始化"
    }
    tvInitStatus.text = "初始化状态: $initStatusText"

    val initColor = when {
      status.isInitializing -> android.R.color.holo_orange_dark
      status.isInitialized -> android.R.color.holo_green_dark
      else -> android.R.color.holo_red_dark
    }
    tvInitStatus.setTextColor(ContextCompat.getColor(this, initColor))

    // 运行状态
    tvRunningStatus.text = "运行状态: ${if (status.isRunning) "运行中" else "已停止"}"
    tvRunningStatus.setTextColor(
      ContextCompat.getColor(
        this,
        if (status.isRunning) android.R.color.holo_green_dark else android.R.color.holo_red_dark
      )
    )

    // 模型路径
    tvModelPath.text = "模型路径: ${if (status.modelPath.isNotEmpty()) status.modelPath else "未设置"}"

    // 缓冲区大小
    tvBufferSize.text = "缓冲区大小: ${status.bufferSize} bytes"
  }

  private fun updateButtonStates(status: OfflineEngineStatus) {
    // 初始化按钮状态
    when {
      status.isInitializing -> {
        btnInitialize.isEnabled = false
        btnInitialize.text = "初始化中..."
      }
      status.isInitialized -> {
        btnInitialize.isEnabled = false
        btnInitialize.text = "已初始化"
      }
      else -> {
        btnInitialize.isEnabled = true
        btnInitialize.text = "初始化引擎"
      }
    }

    // 其他按钮状态
    btnStart.isEnabled = status.isInitialized && !status.isRunning && !status.isInitializing
    btnStop.isEnabled = status.isRunning
    btnRelease.isEnabled = (status.isInitialized || status.isInitializing)
    btnSendTestData.isEnabled = status.isRunning
    btnSendContinuousData.isEnabled = status.isRunning

    // 更新连续发送按钮文本
    btnSendContinuousData.text = if (isContinuousDataSending) "停止连续发送" else "连续发送测试数据"
  }

  private fun sendTestPcmData() {
    // 生成模拟的PCM数据 (16kHz, 16bit, mono, 20ms = 640 bytes)
    val frameSize = 640
    val testData = ByteArray(frameSize)

    // 填充一些模拟的音频数据（简单的正弦波）
    for (i in testData.indices step 2) {
      val sample = (Short.MAX_VALUE * 0.1 * Math.sin(2.0 * Math.PI * 440.0 * i / 32000.0)).toInt().toShort()
      testData[i] = (sample.toInt() and 0xFF).toByte()
      testData[i + 1] = ((sample.toInt() shr 8) and 0xFF).toByte()
    }

    offlineEngine.processPcmData(testData)
    appendLog("发送测试PCM数据: ${testData.size} bytes")
    updateUI()
  }

  private fun startContinuousDataSending() {
    isContinuousDataSending = true
    appendLog("开始连续发送测试数据...")
    // TODO: 实现连续发送逻辑
    updateUI()
  }

  private fun stopContinuousDataSending() {
    isContinuousDataSending = false
    appendLog("停止连续发送测试数据")
    updateUI()
  }

  private fun appendLog(message: String) {
    val timestamp = dateFormat.format(Date())
    val logEntry = "[$timestamp] $message\n"

    runOnUiThread {
      val currentText = tvDebugLogs.text.toString()
      val newText = if (currentText == "等待日志输出...") {
        logEntry
      } else {
        currentText + logEntry
      }
      tvDebugLogs.text = newText
    }
  }

  override fun onDestroy() {
    super.onDestroy()
    stopContinuousDataSending()
    if (::offlineEngine.isInitialized) {
      offlineEngine.release()
    }
    appendLog("Activity销毁，引擎已释放")
  }
}
