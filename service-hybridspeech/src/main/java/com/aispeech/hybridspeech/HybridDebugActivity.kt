package com.aispeech.hybridspeech

import android.Manifest
import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.os.IBinder
import android.os.RemoteException
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.aispeech.hybridspeech.signing.HybridSpeechSignManager
import com.aispeech.tablet.core.network.config.NetWorkConfig
import com.aispeech.tablet.core.network.domain.config.ApiOptionsProviderUtils
import com.aispeech.tablet.core.network.service.interceptor.HeaderUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

class HybridDebugActivity : Activity(), CoroutineScope {

  companion object {
    private const val TAG = "HybridDebugActivity"
    private const val REQUEST_PERMISSIONS = 1001
    private val REQUIRED_PERMISSIONS = arrayOf(
      Manifest.permission.RECORD_AUDIO,
      Manifest.permission.WRITE_EXTERNAL_STORAGE,
      Manifest.permission.READ_EXTERNAL_STORAGE
    )
  }

  // 协程作用域
  override val coroutineContext = SupervisorJob() + Dispatchers.Main

  private var hybridSpeechService: IHybridSpeechService? = null
  private var serviceConnection: ServiceConnection? = null
  private var isServiceConnected = false
  private lateinit var signManager: HybridSpeechSignManager

  private lateinit var statusText: TextView
  private lateinit var connectButton: Button
  private lateinit var disconnectButton: Button
  private lateinit var recordingControlButton: Button // 改名为录音控制按钮，支持开始/暂停/继续
  private lateinit var stopRecordingButton: Button
  private lateinit var modeToggleButton: Button
  private lateinit var modeStatusText: TextView
  private lateinit var durationText: TextView
  private lateinit var logTextView: TextView
  private lateinit var scrollView: ScrollView

  // 模式状态
  private var isOnlineMode = true

  // 录音进度回调实现
  private val progressCallback = object : IRecordProgressCallback.Stub() {
    override fun onRecordingProgress(durationMs: Long) {
      runOnUiThread {
        durationText.text = "录音时长: ${formatDuration(durationMs)}"
      }
    }

    override fun onRecordingStarted() {
      runOnUiThread {
        durationText.text = "录音时长: 00:00.000"
        durationText.setTextColor(0xFF2196F3.toInt()) // 录音时变为蓝色
      }
    }

    override fun onRecordingStopped(totalDurationMs: Long) {
      runOnUiThread {
        durationText.text = "录音时长: ${formatDuration(totalDurationMs)} (已停止)"
        durationText.setTextColor(0xFF757575.toInt()) // 停止时变为灰色
      }
    }

    override fun onError(error: String?) {
      // 不处理错误，保持简单
    }
  }

  // 暂停录音回调实现
  private val pauseRecordingCallback = object : IPauseRecordingCallback.Stub() {
    override fun onPauseRecordingSuccess(result: com.aispeech.hybridspeech.PauseRecordingResult) {
      runOnUiThread {
        recordingControlButton.text = "继续录音"
        recordingControlButton.isEnabled = true
        stopRecordingButton.isEnabled = true
        modeToggleButton.isEnabled = false // 暂停时仍禁用模式切换

        // 更新时长显示颜色为橙色表示暂停状态
        durationText.setTextColor(0xFFFF9800.toInt())
      }
      appendLog("✅ 录音已暂停")
      val modeText = if (result.isOnlineMode) "在线模式" else "离线模式"
      appendLog("  模式: $modeText")
      appendLog("  录音时长: ${formatDuration(result.recordingDurationMs)}")
      if (result.isOnlineMode && result.finalTextReceived != null) {
        val finalTextStatus = if (result.finalTextReceived) "已收到" else "未收到"
        appendLog("  最终文本: $finalTextStatus")
      }
      appendSeparator()
    }

    override fun onPauseRecordingError(errorCode: Int, errorMessage: String?) {
      runOnUiThread {
        recordingControlButton.isEnabled = true
      }
      appendLog("❌ 暂停录音失败")
      appendLog("  错误代码: $errorCode")
      appendLog("  错误信息: ${errorMessage ?: "未知错误"}")
      appendSeparator()
    }
  }

  // 继续录音回调实现
  private val resumeRecordingCallback = object : IResumeRecordingCallback.Stub() {
    override fun onResumeRecordingSuccess() {
      runOnUiThread {
        recordingControlButton.text = "暂停录音"
        recordingControlButton.isEnabled = true
        stopRecordingButton.isEnabled = true
        modeToggleButton.isEnabled = false // 录音时禁用模式切换

        // 恢复录音时的蓝色
        durationText.setTextColor(0xFF2196F3.toInt())
      }
      appendLog("✅ 录音已继续")
      appendSeparator()
    }

    override fun onResumeRecordingError(errorCode: Int, errorMessage: String?) {
      runOnUiThread {
        recordingControlButton.isEnabled = true
      }
      appendLog("❌ 继续录音失败")
      appendLog("  错误代码: $errorCode")
      appendLog("  错误信息: ${errorMessage ?: "未知错误"}")
      appendSeparator()
    }
  }

  // AIDL回调实现
  private val transcriptionCallback = object : ITranscriptionCallback.Stub() {
    override fun onTranscriptionResult(result: TranscriptionResult?) {
      result?.let {
        runOnUiThread {
          val resultText = getResultDisplayText(it)
          val resultType = getResultTypeName(it)
          appendLog("[$resultType] $resultText")

          // 对于重要结果类型，添加额外的格式化显示
          when (it) {
            is TranscriptionResult.FinalTextResult -> {
              appendLog("  ✓ 最终确认: ${it.text}")
            }
            is TranscriptionResult.AgendaResult -> {
              appendLog("  📋 议程内容: ${it.text}")
              if (it.isLast) {
                appendLog("  🏁 议程结束")
              }
            }
            is TranscriptionResult.StartResult -> {
              appendLog("  🔗 连接建立成功")
            }
            else -> {
              // 其他类型不需要额外显示
            }
          }
        }
      }
    }

    override fun onError(errorMessage: String?) {
      errorMessage?.let {
        runOnUiThread {
          appendLog("❌ 转写错误: $it")
        }
      }
    }

    override fun onStatusChanged(status: Int) {
      runOnUiThread {
        updateServiceStatus(status)
        val statusText = getStatusText(status)
        val statusIcon = getStatusIcon(status)
        appendLog("$statusIcon 状态变化: $statusText")
      }
    }
  }

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    createUI()

    initializeSignManager()
    updateModeUI() // 初始化模式UI显示
    appendLog("Debug界面初始化完成")
    appendLog("默认模式: ${if (isOnlineMode) "在线模式" else "离线模式"}")

    // 检查和请求权限
    checkAndRequestPermissions()
  }

  private fun initializeSignManager() {
    signManager = HybridSpeechSignManager.getInstance()
    signManager.initialize(
      apiKey = "84fb45aa-4400-422a-b1c8-9c91f6b665af",
      apiSecret = "fde4f4e2-0fff-4de8-bcad-4aa463dadf39",
      signType = "HMAC-MD5",
      environment = "debug"
    )
    appendLog("签名管理器初始化完成")
  }

  private fun createUI() {
    val layout = LinearLayout(this).apply {
      orientation = LinearLayout.VERTICAL
      setPadding(32, 32, 32, 32)
    }

    // 标题
    val titleText = TextView(this).apply {
      text = "混合语音服务调试"
      textSize = 24f
      setPadding(0, 0, 0, 32)
    }
    layout.addView(titleText)

    // 服务连接状态
    statusText = TextView(this).apply {
      text = "服务状态: 未连接"
      textSize = 16f
      setPadding(0, 0, 0, 16)
    }
    layout.addView(statusText)

    // 模式状态显示
    modeStatusText = TextView(this).apply {
      text = "当前模式: 在线模式"
      textSize = 16f
      setPadding(0, 0, 0, 16)
      setTextColor(0xFF2196F3.toInt()) // 蓝色
    }
    layout.addView(modeStatusText)

    // 模式切换按钮
    modeToggleButton = Button(this).apply {
      text = "切换到离线模式"
      setOnClickListener { toggleMode() }
      setPadding(0, 8, 0, 16)
      isEnabled = false // 初始状态禁用，需要先连接服务
    }
    layout.addView(modeToggleButton)

    // 时长显示
    durationText = TextView(this).apply {
      text = "录音时长: 00:00.000"
      textSize = 18f
      setPadding(0, 16, 0, 16)
      setTextColor(0xFF757575.toInt()) // 初始灰色
      visibility = TextView.VISIBLE // 初始可见
    }
    layout.addView(durationText)

    // 连接按钮
    val buttonLayout = LinearLayout(this).apply {
      orientation = LinearLayout.HORIZONTAL
    }

    connectButton = Button(this).apply {
      text = "连接服务"
      setOnClickListener { connectToService() }
    }
    buttonLayout.addView(connectButton)

    disconnectButton = Button(this).apply {
      text = "断开连接"
      setOnClickListener { disconnectFromService() }
      isEnabled = false
    }
    buttonLayout.addView(disconnectButton)

    layout.addView(buttonLayout)

    // 录音控制按钮
    val recordingLayout = LinearLayout(this).apply {
      orientation = LinearLayout.HORIZONTAL
      setPadding(0, 16, 0, 16)
    }

    recordingControlButton = Button(this).apply {
      text = "开始录音"
      setOnClickListener { handleRecordingControl() }
      isEnabled = false
    }
    recordingLayout.addView(recordingControlButton)

    stopRecordingButton = Button(this).apply {
      text = "停止录音"
      setOnClickListener { stopRecordingAsync() }
      isEnabled = false
    }
    recordingLayout.addView(stopRecordingButton)

    layout.addView(recordingLayout)

    // 日志显示
    val logLabel = TextView(this).apply {
      text = "调试日志:"
      textSize = 16f
      setPadding(0, 16, 0, 8)
    }
    layout.addView(logLabel)

    scrollView = ScrollView(this)
    logTextView = TextView(this).apply {
      textSize = 12f
      setPadding(16, 16, 16, 16)
      setBackgroundColor(0xFFF5F5F5.toInt())
    }
    scrollView.addView(logTextView)
    layout.addView(scrollView, LinearLayout.LayoutParams(
      LinearLayout.LayoutParams.MATCH_PARENT,
      LinearLayout.LayoutParams.MATCH_PARENT
    ))

    setContentView(layout)
  }

  // 实现配置提供者
  private val configProvider = object : IHybridSpeechConfigProvider.Stub() {

    override fun requestNetworkConfig(
      request: NetworkConfigRequest,
      callback: INetworkConfigCallback
    ) {
      appendLog("🔧 服务端请求网络配置")
      appendLog("  请求参数: recordId=${request.recordId}, userId=${request.userId}")

      launch {
        try {
          // 使用配置生成器
          val networkConfig = signManager.generateNetworkConfig(
            request = request,
            baseUrl = NetWorkConfig.getBaseUrl(),
            customOptions = getApiOptions()
          )

          appendLog("✓ 网络配置生成成功")

          // 记录生成的配置信息
          networkConfig.websocketConfig?.let { ws ->
            appendLog("  WebSocket URL: ${ws.signedUrl.take(100)}...")
            appendLog("  API Key: ${ws.apiKey.take(10)}...")
          }

          callback.onConfigReady(networkConfig)
          appendLog("✅ 网络配置已返回给服务端")

        } catch (e: Exception) {
          appendLog("❌ 网络配置生成失败: ${e.message}")
          callback.onConfigError(
            ConfigErrorCode.SIGN_FAILED,
            "网络配置生成失败: ${e.message}"
          )
        }
      }
    }
  }

  private fun connectToService() {
    if (isServiceConnected) {
      appendLog("服务已连接")
      return
    }

    appendLog("开始连接服务...")

    // 添加详细的服务连接日志
    val intent = Intent(this, HybridSpeechService::class.java)
    appendLog("创建Intent: ${intent.component}")
    appendLog("目标服务类: ${HybridSpeechService::class.java.name}")

    // 尝试显式Intent
    val explicitIntent = Intent().apply {
      setClassName(packageName, "com.aispeech.hybridspeech.HybridSpeechService")
    }
    appendLog("显式Intent: ${explicitIntent.component}")

    // 尝试通过Action连接
    val actionIntent = Intent("com.aispeech.hybridspeech.ACTION_HYBRID_SPEECH").apply {
      setPackage(packageName)
    }
    appendLog("Action Intent: ${actionIntent.action}, package: ${actionIntent.`package`}")

    serviceConnection = object : ServiceConnection {
      override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
        appendLog("服务连接成功 - ComponentName: $name")
        appendLog("IBinder: ${service?.javaClass?.name}")

        try {
          hybridSpeechService = IHybridSpeechService.Stub.asInterface(service)
          appendLog("AIDL接口创建成功: ${hybridSpeechService?.javaClass?.name}")
          isServiceConnected = true

          runOnUiThread {
            connectButton.isEnabled = false
            disconnectButton.isEnabled = true
            recordingControlButton.isEnabled = true
            modeToggleButton.isEnabled = true // 连接后允许切换模式
            statusText.text = "服务状态: 已连接"
          }

          // 注册配置提供者
          hybridSpeechService?.registerConfigProvider(configProvider)
          appendLog("✅ 配置提供者已注册")

          // 注册回调
          registerCallback()

          // 获取当前状态
          getCurrentStatus()
        } catch (e: Exception) {
          appendLog("创建AIDL接口失败: ${e.message}")
          e.printStackTrace()
        }
      }

      override fun onServiceDisconnected(name: ComponentName?) {
        appendLog("服务连接断开 - ComponentName: $name")

        // 取消注册配置提供者
        hybridSpeechService?.unregisterConfigProvider()

        hybridSpeechService = null
        isServiceConnected = false
        runOnUiThread {
          connectButton.isEnabled = true
          disconnectButton.isEnabled = false
          recordingControlButton.isEnabled = false
          stopRecordingButton.isEnabled = false
          modeToggleButton.isEnabled = false // 断开连接后禁用模式切换
          statusText.text = "服务状态: 连接断开"
        }
      }
    }

    // 尝试多种连接方式
    var bound = false
    try {
      appendLog("尝试方式1: 直接类名绑定")
      bound = bindService(intent, serviceConnection!!, Context.BIND_AUTO_CREATE)
      appendLog("方式1结果: $bound")

      if (!bound) {
        appendLog("尝试方式2: 显式Intent绑定")
        bound = bindService(explicitIntent, serviceConnection!!, Context.BIND_AUTO_CREATE)
        appendLog("方式2结果: $bound")
      }

      if (!bound) {
        appendLog("尝试方式3: Action Intent绑定")
        bound = bindService(actionIntent, serviceConnection!!, Context.BIND_AUTO_CREATE)
        appendLog("方式3结果: $bound")
      }

      if (!bound) {
        appendLog("所有绑定方式都失败")
        // 检查服务是否存在
        checkServiceAvailability()
      } else {
        appendLog("服务绑定请求已发送，等待连接...")
      }
    } catch (e: Exception) {
      appendLog("连接服务异常: ${e.message}")
      appendLog("异常堆栈: ${Log.getStackTraceString(e)}")
    }
  }

  private fun disconnectFromService() {
    appendLog("断开服务连接")

    // 取消注册回调
    unregisterCallback()

    // 清理进度回调
    try {
      hybridSpeechService?.unregisterAllProgressCallbacks()
    } catch (e: RemoteException) {
      // 忽略异常
    }

    serviceConnection?.let {
      try {
        unbindService(it)
      } catch (e: Exception) {
        appendLog("解绑服务异常: ${e.message}")
      }
    }

    hybridSpeechService = null
    serviceConnection = null
    isServiceConnected = false

    runOnUiThread {
      connectButton.isEnabled = true
      disconnectButton.isEnabled = false
      recordingControlButton.isEnabled = false
      stopRecordingButton.isEnabled = false
      modeToggleButton.isEnabled = false // 未连接时禁用模式切换
      statusText.text = "服务状态: 未连接"
    }
  }

  /**
   * 检查和请求权限
   */
  private fun checkAndRequestPermissions() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
      val missingPermissions = REQUIRED_PERMISSIONS.filter { permission ->
        ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
      }

      if (missingPermissions.isNotEmpty()) {
        appendLog("需要请求权限: ${missingPermissions.joinToString(", ")}")
        ActivityCompat.requestPermissions(this, missingPermissions.toTypedArray(), REQUEST_PERMISSIONS)
      } else {
        appendLog("所有权限已授予")
      }
    } else {
      appendLog("Android版本低于6.0，无需动态权限检查")
    }
  }

  /**
   * 权限请求结果处理
   */
  override fun onRequestPermissionsResult(
    requestCode: Int,
    permissions: Array<out String>,
    grantResults: IntArray
  ) {
    super.onRequestPermissionsResult(requestCode, permissions, grantResults)

    if (requestCode == REQUEST_PERMISSIONS) {
      val deniedPermissions = mutableListOf<String>()
      for (i in permissions.indices) {
        if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
          deniedPermissions.add(permissions[i])
        }
      }

      if (deniedPermissions.isEmpty()) {
        appendLog("所有权限已授予")
      } else {
        appendLog("权限被拒绝: ${deniedPermissions.joinToString(", ")}")
        appendLog("警告: 录音功能可能无法正常工作")
      }
    }
  }

  /**
   * 检查是否有录音权限
   */
  private fun hasRecordAudioPermission(): Boolean {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
      ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) == PackageManager.PERMISSION_GRANTED
    } else {
      true
    }
  }

  /**
   * 检查是否有存储权限
   */
  private fun hasStoragePermission(): Boolean {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
      ContextCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
    } else {
      true
    }
  }

  private fun getApiOptions(): Map<String, String> {
    return HeaderUtils.makeCommonHeader() + ApiOptionsProviderUtils.getOptions().mapValues {
      it.value.toString()
    }
  }

  /**
   * 切换在线/离线模式
   */
  private fun toggleMode() {
    // 如果正在录音或暂停中，不允许切换模式
    try {
      val currentStatus = hybridSpeechService?.getCurrentStatus()
      if (currentStatus == ServiceStatus.RECORDING || currentStatus == ServiceStatus.PAUSED) {
        val statusText = if (currentStatus == ServiceStatus.RECORDING) "录音进行中" else "录音暂停中"
        appendLog("${statusText}，无法切换模式")
        return
      }
    } catch (e: Exception) {
      appendLog("检查录音状态时出错: ${e.message}")
    }

    val previousMode = isOnlineMode
    isOnlineMode = !isOnlineMode
    updateModeUI()

    appendLog("模式已切换: ${if (previousMode) "在线" else "离线"} -> ${if (isOnlineMode) "在线" else "离线"}")

    // 提示用户模式切换的影响
    if (isOnlineMode) {
      appendLog("提示: 在线模式需要网络连接，将使用WebSocket进行实时转写")
    } else {
      appendLog("提示: 离线模式使用本地模型，请确保离线模型文件存在")
      appendLog("离线模型路径: /sdcard/hybrid_speech_debug/offline_model")
    }
  }

  /**
   * 更新模式相关的UI显示
   */
  private fun updateModeUI() {
    runOnUiThread {
      if (isOnlineMode) {
        modeStatusText.text = "当前模式: 在线模式"
        modeStatusText.setTextColor(0xFF2196F3.toInt()) // 蓝色
        modeToggleButton.text = "切换到离线模式"
      } else {
        modeStatusText.text = "当前模式: 离线模式"
        modeStatusText.setTextColor(0xFF4CAF50.toInt()) // 绿色
        modeToggleButton.text = "切换到在线模式"
      }
    }
  }

  /**
   * 处理录音控制按钮点击事件
   * 根据当前状态执行开始录音、暂停录音或继续录音
   */
  private fun handleRecordingControl() {
    val service = hybridSpeechService
    if (service == null) {
      appendLog("服务未连接，无法操作录音")
      return
    }

    try {
      val currentStatus = service.getCurrentStatus()
      when (currentStatus) {
        ServiceStatus.IDLE -> {
          // 空闲状态：开始录音
          startRecording()
        }
        ServiceStatus.RECORDING -> {
          // 录音中：暂停录音
          pauseRecording()
        }
        ServiceStatus.PAUSED -> {
          // 暂停中：继续录音
          resumeRecording()
        }
        else -> {
          appendLog("当前状态不支持录音控制操作: ${getStatusText(currentStatus)}")
        }
      }
    } catch (e: Exception) {
      appendLog("获取服务状态失败: ${e.message}")
    }
  }

  /**
   * 开始录音
   */
  private fun startRecording() {
    val service = hybridSpeechService
    if (service == null) {
      appendLog("服务未连接，无法开始录音")
      return
    }

    // 检查权限
    if (!hasRecordAudioPermission()) {
      appendLog("缺少录音权限，请先授予权限")
      checkAndRequestPermissions()
      return
    }

    if (!hasStoragePermission()) {
      appendLog("缺少存储权限，请先授予权限")
      checkAndRequestPermissions()
      return
    }

    appendSeparator()
    appendLog("🎙️ 开始录音...")
    appendLog("✓ 权限检查通过")

    try {
      // 确保输出目录存在
      val outputDir = "/sdcard/hybrid_speech_debug/"
      val dir = File(outputDir)
      if (!dir.exists()) {
        val created = dir.mkdirs()
        appendLog("创建输出目录: $outputDir, 结果: $created")
      } else {
        appendLog("输出目录已存在: $outputDir")
      }

      // 根据当前模式创建配置
      if (isOnlineMode) {
        // 在线模式：使用配置提供者接口
        appendLog("🌐 使用配置提供者启动在线录音...")

        val recordId = System.currentTimeMillis()
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val pcmFilePath = File(outputDir, "online_recording_${timestamp}.pcm").absolutePath
        val mp3FilePath = File(outputDir, "online_recording_${timestamp}.mp3").absolutePath

        val config = RecordingConfig.createOnlineRecordingConfig(
          recordId = recordId,
          userId = "1000002643",
          pcmFilePath = pcmFilePath,
          mp3FilePath = mp3FilePath,
          serverUrl = "", // 将由配置提供者填充
          apiKey = "", // 将由配置提供者填充
          language = "cn",
          audioType = "mp3",
          translate = null, // 不使用翻译
          enableRealtimeAgenda = true,
          customOptions = emptyMap(),
        )

        appendLog("📋 在线录音配置:")
        appendLog("  recordId: ${config.onlineAsrConfig?.recordId}")
        appendLog("  userId: ${config.onlineAsrConfig?.userId}")
        appendLog("  PCM文件: ${config.pcmFilePath}")
        appendLog("  MP3文件: ${config.mp3FilePath}")

        // 先注册进度回调
        service.registerProgressCallback(progressCallback, 200)

        // 创建启动录音回调
        val startRecordingCallback = object : IStartRecordingCallback.Stub() {
          override fun onStartRecordingSuccess() {
            runOnUiThread {
              recordingControlButton.text = "暂停录音"
              recordingControlButton.isEnabled = true
              stopRecordingButton.isEnabled = true
              modeToggleButton.isEnabled = false
            }
            appendLog("✅ 在线录音启动成功")
            appendSeparator()
          }

          override fun onStartRecordingError(errorCode: Int, errorMessage: String?) {
            runOnUiThread {
              recordingControlButton.text = "开始录音"
              recordingControlButton.isEnabled = true
              stopRecordingButton.isEnabled = false
              modeToggleButton.isEnabled = true
            }
            appendLog("❌ 在线录音启动失败")
            appendLog("  错误代码: $errorCode")
            appendLog("  错误信息: ${errorMessage ?: "未知错误"}")
            appendSeparator()
          }
        }

        // 使用统一配置提供者接口
        service.startRecordingWithProvider(config, startRecordingCallback)
        appendLog("🚀 在线录音请求已发送（将通过配置提供者获取WebSocket配置）...")
        return
      } else {

        appendLog("💾 构建离线模式配置...")

        val offlineEngineConfig = OfflineEngineConfig(
          modelPath = "/sdcard/hybrid_speech_debug/offline_model/magnus_with_translate",
          enableVAD = true,
          vadThreshold = 0.5f
        )

        appendLog("✓ 离线引擎配置完成")
        appendLog("模型路径: ${offlineEngineConfig.modelPath}")

        // 生成文件路径
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val pcmFilePath = File(outputDir, "offline_recording_${timestamp}.pcm").absolutePath
        val mp3FilePath = File(outputDir, "offline_recording_${timestamp}.mp3").absolutePath

        // 离线模式：保持原有逻辑
        val config = RecordingConfig(
          useOnlineMode = false,
          pcmFilePath = pcmFilePath,
          mp3FilePath = mp3FilePath,
          offlineEngineConfig = offlineEngineConfig,
          offlineTranslationConfig = OfflineTranslationConfig.createEnabled(
            fromLanguage = "Chinese",
            toLanguage = "English"
          ),
          progressConfig = RecordingProgressConfig.createDefault()
        )

        appendLog("📋 录音配置总览:")
        appendLog("  模式: 离线模式")
        appendLog("  PCM文件: ${config.pcmFilePath}")
        appendLog("  MP3文件: ${config.mp3FilePath}")
        appendLog("  离线配置:")
        appendLog("    - VAD启用: ${config.offlineEngineConfig?.enableVAD}")
        appendLog("    - VAD阈值: ${config.offlineEngineConfig?.vadThreshold}")

        // 先注册进度回调，再启动录音
        service.registerProgressCallback(progressCallback, 200)

        // 创建异步启动录音回调
        val startRecordingCallback = object : IStartRecordingCallback.Stub() {
          override fun onStartRecordingSuccess() {
            runOnUiThread {
              recordingControlButton.text = "暂停录音"
              recordingControlButton.isEnabled = true
              stopRecordingButton.isEnabled = true
              modeToggleButton.isEnabled = false
            }
            appendLog("✅ 离线录音启动成功")
            appendSeparator()
          }

          override fun onStartRecordingError(errorCode: Int, errorMessage: String?) {
            runOnUiThread {
              recordingControlButton.text = "开始录音"
              recordingControlButton.isEnabled = true
              stopRecordingButton.isEnabled = false
              modeToggleButton.isEnabled = true
            }
            appendLog("❌ 离线录音启动失败")
            appendLog("  错误代码: $errorCode")
            appendLog("  错误信息: ${errorMessage ?: "未知错误"}")
            appendSeparator()
          }
        }

        // 调用原有的录音接口
        service.startRecordingWithConfigAsync(config, startRecordingCallback)
        appendLog("🚀 离线录音请求已发送...")
        return
      }
    } catch (e: Exception) {
      Log.d(TAG, "startRecording: ")
    }
  }



  /**
   * 暂停录音
   */
  private fun pauseRecording() {
    val service = hybridSpeechService
    if (service == null) {
      appendLog("服务未连接，无法暂停录音")
      return
    }

    appendSeparator()
    appendLog("⏸️ 暂停录音...")

    try {
      // 禁用按钮防止重复点击
      runOnUiThread {
        recordingControlButton.isEnabled = false
      }

      // 调用异步暂停录音
      service.pauseRecordingAsync(pauseRecordingCallback)
      appendLog("🚀 异步暂停录音请求已发送，等待回调...")

    } catch (e: RemoteException) {
      appendLog("暂停录音异常: ${e.message}")
      runOnUiThread {
        recordingControlButton.isEnabled = true
      }
    }
  }

  /**
   * 继续录音
   */
  private fun resumeRecording() {
    val service = hybridSpeechService
    if (service == null) {
      appendLog("服务未连接，无法继续录音")
      return
    }

    appendSeparator()
    appendLog("▶️ 继续录音...")

    try {
      // 禁用按钮防止重复点击
      runOnUiThread {
        recordingControlButton.isEnabled = false
      }

      // 调用异步继续录音
      service.resumeRecordingAsync(resumeRecordingCallback)
      appendLog("🚀 异步继续录音请求已发送，等待回调...")

    } catch (e: RemoteException) {
      appendLog("继续录音异常: ${e.message}")
      runOnUiThread {
        recordingControlButton.isEnabled = true
      }
    }
  }

  private fun stopRecordingAsync() {
    val service = hybridSpeechService
    if (service == null) {
      appendLog("服务未连接，无法停止录音")
      return
    }

    appendSeparator()
    appendLog("⏹️ 异步停止录音...")

    try {
      // 取消进度回调
      service.unregisterProgressCallback(progressCallback)

      // 创建异步停止录音回调
      val stopRecordingCallback = object : IStopRecordingCallback.Stub() {
        override fun onStopRecordingSuccess(result: RecordingResultInfo?) {
          runOnUiThread {
            recordingControlButton.text = "开始录音"
            recordingControlButton.isEnabled = true
            stopRecordingButton.isEnabled = false
            modeToggleButton.isEnabled = true // 停止录音后重新允许模式切换
          }

          if (result != null) {
            appendLog("✅ 异步录音停止成功")
            appendLog("📁 录音文件信息:")
            appendLog("  PCM文件: ${result.pcmFilePath}")
            appendLog("  MP3文件: ${result.mp3FilePath}")
            appendLog("  录音时长: ${formatDuration(result.durationMs)}")
            appendLog("  文件大小: ${formatFileSize(result.fileSizeBytes)}")
            appendSeparator()
          } else {
            appendLog("❌ 异步录音停止成功但结果为空")
          }
        }

        override fun onStopRecordingError(errorCode: Int, errorMessage: String?) {
          runOnUiThread {
            recordingControlButton.text = "开始录音"
            recordingControlButton.isEnabled = true
            stopRecordingButton.isEnabled = false
            modeToggleButton.isEnabled = true
          }

          appendLog("❌ 异步录音停止失败")
          appendLog("  错误代码: $errorCode")
          appendLog("  错误信息: ${errorMessage ?: "未知错误"}")
          appendSeparator()
        }
      }

      // 调用异步停止录音
      service.stopRecordingWithResultAsync(stopRecordingCallback)
      appendLog("🚀 异步停止录音请求已发送，等待回调...")

    } catch (e: RemoteException) {
      appendLog("异步停止录音异常: ${e.message}")
      runOnUiThread {
        recordingControlButton.text = "开始录音"
        recordingControlButton.isEnabled = true
        stopRecordingButton.isEnabled = false
        modeToggleButton.isEnabled = true
      }
    }
  }

  private fun getCurrentStatus() {
    val service = hybridSpeechService ?: return

    try {
      appendLog("正在获取服务状态...")
      val status = service.getCurrentStatus()
      updateServiceStatus(status)
      appendLog("获取状态成功: ${getStatusText(status)}")
    } catch (e: RemoteException) {
      appendLog("获取状态异常: ${e.message}")
      appendLog("RemoteException详情: ${Log.getStackTraceString(e)}")
    } catch (e: Exception) {
      appendLog("获取状态未知异常: ${e.message}")
      appendLog("异常详情: ${Log.getStackTraceString(e)}")
    }
  }

  private fun registerCallback() {
    val service = hybridSpeechService ?: return
    try {
      service.registerCallback(transcriptionCallback)
      appendLog("注册回调成功")
    } catch (e: RemoteException) {
      appendLog("注册回调异常: ${e.message}")
    }
  }

  private fun unregisterCallback() {
    val service = hybridSpeechService ?: return
    try {
      service.unregisterCallback(transcriptionCallback)
      appendLog("取消注册回调成功")
    } catch (e: RemoteException) {
      appendLog("取消注册回调异常: ${e.message}")
    }
  }



  private fun formatDuration(durationMs: Long): String {
    val totalSeconds = durationMs / 1000.0
    val minutes = (totalSeconds / 60).toInt()
    val seconds = totalSeconds % 60
    return String.format("%02d:%06.3f", minutes, seconds)
  }

  private fun formatFileSize(bytes: Long): String {
    if (bytes < 1024) return "${bytes}B"
    if (bytes < 1024 * 1024) return String.format("%.1fKB", bytes / 1024.0)
    if (bytes < 1024 * 1024 * 1024) return String.format("%.1fMB", bytes / (1024.0 * 1024))
    return String.format("%.1fGB", bytes / (1024.0 * 1024 * 1024))
  }

  private fun updateServiceStatus(status: Int) {
    runOnUiThread {
      statusText.text = "服务状态: ${getStatusText(status)}"
      when (status) {
        ServiceStatus.RECORDING -> {
          recordingControlButton.text = "暂停录音"
          recordingControlButton.isEnabled = true
          stopRecordingButton.isEnabled = true
          modeToggleButton.isEnabled = false // 录音时禁用模式切换
        }
        ServiceStatus.IDLE -> {
          recordingControlButton.text = "开始录音"
          recordingControlButton.isEnabled = true
          stopRecordingButton.isEnabled = false
          modeToggleButton.isEnabled = true // 空闲时允许模式切换

          // 状态变为空闲时重置时长显示
          if (durationText.text.toString().contains("已停止")) {
            durationText.text = "录音时长: 00:00.000"
            durationText.setTextColor(0xFF757575.toInt()) // 灰色
          }
        }
        ServiceStatus.INITIALIZING -> {
          recordingControlButton.text = "开始录音"
          recordingControlButton.isEnabled = false
          stopRecordingButton.isEnabled = false
          modeToggleButton.isEnabled = false
        }
        ServiceStatus.PROCESSING -> {
          recordingControlButton.text = "开始录音"
          recordingControlButton.isEnabled = false
          stopRecordingButton.isEnabled = true
          modeToggleButton.isEnabled = false
        }
        ServiceStatus.PAUSED -> {
          recordingControlButton.text = "继续录音"
          recordingControlButton.isEnabled = true
          stopRecordingButton.isEnabled = true
          modeToggleButton.isEnabled = false

          // 暂停状态时显示橙色
          durationText.setTextColor(0xFFFF9800.toInt())
        }
        ServiceStatus.ERROR -> {
          recordingControlButton.text = "开始录音"
          recordingControlButton.isEnabled = true
          stopRecordingButton.isEnabled = false
          modeToggleButton.isEnabled = true

          // 错误状态时重置时长显示
          durationText.text = "录音时长: 00:00.000"
          durationText.setTextColor(0xFF757575.toInt()) // 灰色
        }
      }
    }
  }

  private fun appendLog(message: String) {
    val timestamp = java.text.SimpleDateFormat("HH:mm:ss.SSS", java.util.Locale.getDefault())
      .format(java.util.Date())
    val logMessage = "[$timestamp] $message\n"

    runOnUiThread {
      logTextView.append(logMessage)
      scrollView.post {
        scrollView.fullScroll(View.FOCUS_DOWN)
      }
    }

    Log.i(TAG, message)
  }

  private fun appendSeparator() {
    appendLog("─".repeat(50))
  }

  private fun getResultDisplayText(result: TranscriptionResult): String {
    return when (result) {
      is TranscriptionResult.StartResult -> {
        val details = mutableListOf<String>()
        details.add("建立连接")
        result.sendAllSuccessCount?.let { details.add("成功发送数: $it") }
        result.sendAllSuccessLength?.let { details.add("发送长度: $it") }
        result.finalTimestamp?.let { details.add("时间戳: $it") }
        if (result.message != "success") {
          details.add("消息: ${result.message}")
        }
        details.joinToString(" | ")
      }

      is TranscriptionResult.IntermediateResult -> {
        val sourceIndicator = if (result.isOnline) "[在线]" else "[离线]"
        val timeInfo = "开始:${result.begin}ms"
        val roleInfo = if (result.role != 0) " 角色:${result.role}" else ""
        "$sourceIndicator 中间结果 ($timeInfo$roleInfo): ${result.`var`}"
      }

      is TranscriptionResult.ProcessingTextResult -> {
        val sourceIndicator = if (result.isOnline) "[在线]" else "[离线]"
        val timeInfo = "${result.begin}-${result.end}ms"
        val roleInfo = if (result.role != 0) " 角色:${result.role}" else ""
        "$sourceIndicator 处理中 ($timeInfo$roleInfo): ${result.text}"
      }

      is TranscriptionResult.FinalTextResult -> {
        val timeInfo = "${result.begin}-${result.end}ms"
        val roleInfo = if (result.role != 0) " 角色:${result.role}" else ""
        "[最终] 确认文本 ($timeInfo$roleInfo): ${result.text}"
      }

      is TranscriptionResult.AgendaResult -> {
        val timeInfo = "${result.begin}-${result.end}ms"
        val statusInfo = "状态:${result.status}"
        val seqInfo = "序号:${result.seq}"
        val lastInfo = if (result.isLast) " [最后]" else ""
        "[议程] ($timeInfo | $statusInfo | $seqInfo$lastInfo): ${result.text}"
      }

      is TranscriptionResult.InitializationResult -> ""
    }
  }

  private fun getResultTypeName(result: TranscriptionResult): String {
    return when (result) {
      is TranscriptionResult.StartResult -> "连接"
      is TranscriptionResult.IntermediateResult -> if (result.isOnline) "在线中间" else "离线中间"
      is TranscriptionResult.ProcessingTextResult -> if (result.isOnline) "在线处理" else "离线处理"
      is TranscriptionResult.FinalTextResult -> "最终文本"
      is TranscriptionResult.AgendaResult -> "议程"
      is TranscriptionResult.InitializationResult -> "初始化文本"
    }
  }

  private fun getStatusText(status: Int): String {
    return when (status) {
      ServiceStatus.IDLE -> "空闲"
      ServiceStatus.RECORDING -> "录音中"
      ServiceStatus.PROCESSING -> "处理中"
      ServiceStatus.ERROR -> "错误"
      ServiceStatus.INITIALIZING -> "初始化中"
      ServiceStatus.PAUSED -> "已暂停"
      else -> "未知($status)"
    }
  }

  private fun getStatusIcon(status: Int): String {
    return when (status) {
      ServiceStatus.IDLE -> "⏸️"
      ServiceStatus.RECORDING -> "🎙️"
      ServiceStatus.PROCESSING -> "⚙️"
      ServiceStatus.ERROR -> "❌"
      ServiceStatus.INITIALIZING -> "🔄"
      ServiceStatus.PAUSED -> "⏸️"
      else -> "❓"
    }
  }

  private fun checkServiceAvailability() {
    appendLog("=== 服务可用性检查 ===")

    // 检查包信息
    try {
      val packageInfo = packageManager.getPackageInfo(packageName, 0)
      appendLog("当前包名: $packageName")
      appendLog("包版本: ${packageInfo.versionName} (${packageInfo.versionCode})")
    } catch (e: Exception) {
      appendLog("获取包信息失败: ${e.message}")
    }

    // 检查服务是否在Manifest中声明
    try {
      val serviceIntent = Intent().apply {
        setClassName(packageName, "com.aispeech.hybridspeech.HybridSpeechService")
      }
      val resolveInfo = packageManager.resolveService(serviceIntent, 0)
      if (resolveInfo != null) {
        appendLog("✓ 服务在Manifest中已声明")
        appendLog("服务组件: ${resolveInfo.serviceInfo.name}")
        appendLog("服务包名: ${resolveInfo.serviceInfo.packageName}")
        appendLog("服务是否启用: ${resolveInfo.serviceInfo.enabled}")
        appendLog("服务是否导出: ${resolveInfo.serviceInfo.exported}")
      } else {
        appendLog("✗ 服务在Manifest中未找到")
      }
    } catch (e: Exception) {
      appendLog("检查服务声明失败: ${e.message}")
    }

    // 检查Action Intent
    try {
      val actionIntent = Intent("com.aispeech.hybridspeech.ACTION_HYBRID_SPEECH")
      val services = packageManager.queryIntentServices(actionIntent, 0)
      appendLog("通过Action找到的服务数量: ${services.size}")
      services.forEach { resolveInfo ->
        appendLog("Action服务: ${resolveInfo.serviceInfo.name}")
      }
    } catch (e: Exception) {
      appendLog("检查Action服务失败: ${e.message}")
    }

    // 尝试启动服务
    try {
      appendLog("尝试启动服务...")
      val startIntent = Intent(this, HybridSpeechService::class.java)
      val result = startService(startIntent)
      if (result != null) {
        appendLog("✓ 服务启动成功: ${result.className}")
        // 停止服务
        stopService(startIntent)
        appendLog("服务已停止")
      } else {
        appendLog("✗ 服务启动失败")
      }
    } catch (e: Exception) {
      appendLog("启动服务异常: ${e.message}")
      appendLog("异常详情: ${Log.getStackTraceString(e)}")
    }

    appendLog("=== 检查完成 ===")
  }

  // 辅助方法
  private fun getCustomHeaders(): Map<String, String> {
    return mapOf(
      "User-Agent" to "HybridSpeech-Debug/1.0",
      "X-Client-Type" to "Android",
      "X-Debug-Mode" to "true"
    )
  }

  override fun onDestroy() {
    super.onDestroy()
    disconnectFromService()
    appendLog("Debug界面已销毁")
  }
}

// 错误代码常量
object ConfigErrorCode {
  const val SIGN_FAILED = 3001
  const val NETWORK_ERROR = 3002
  const val INVALID_PARAMS = 3003
  const val TIMEOUT = 3004
}