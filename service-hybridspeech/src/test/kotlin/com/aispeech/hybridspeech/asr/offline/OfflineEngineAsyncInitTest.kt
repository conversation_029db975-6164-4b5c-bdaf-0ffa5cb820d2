package com.aispeech.hybridspeech.asr.offline

import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.Assert.*

/**
 * 测试 OfflineEngine 异步初始化和PCM数据缓存功能
 */
class OfflineEngineAsyncInitTest {

    @Test
    fun `test async initialization with PCM data caching using ConcurrentLinkedQueue`() = runBlocking {
        // 创建模拟的PCM数据流
        val pcmDataFlow = MutableSharedFlow<ByteArray>()

        // 创建OfflineOrchestrator
        val orchestrator = OfflineOrchestrator(mockContext())

        // 设置配置，这会启动异步初始化
        val configResult = orchestrator.setConfig("/mock/model/path")
        assertTrue("Config should be set successfully", configResult)

        // 立即启动orchestrator，即使引擎还在初始化
        val startResult = orchestrator.start(pcmDataFlow)
        assertTrue("Orchestrator should start even during initialization", startResult)

        // 发送一些PCM数据，这些数据应该被缓存
        val testPcmData1 = ByteArray(640) { it.toByte() }
        val testPcmData2 = ByteArray(640) { (it + 100).toByte() }

        pcmDataFlow.emit(testPcmData1)
        pcmDataFlow.emit(testPcmData2)

        // 验证状态
        val status = orchestrator.getStatus()
        assertTrue("Should be initializing", status.isInitializing)
        assertTrue("Should be running", status.isRunning)
        assertTrue("Should have cached data", status.engineStatus.cachedDataCount > 0)

        // 等待一段时间让初始化完成
        delay(2000)

        // 清理
        orchestrator.release()
    }
    
    @Test
    fun `test start during initialization vs after initialization`() = runBlocking {
        val pcmDataFlow = MutableSharedFlow<ByteArray>()
        val orchestrator = OfflineOrchestrator(mockContext())
        
        // 测试在初始化期间启动
        orchestrator.setConfig("/mock/model/path")
        val startDuringInit = orchestrator.start(pcmDataFlow)
        assertTrue("Should be able to start during initialization", startDuringInit)
        
        // 等待初始化完成
        delay(1000)
        
        // 测试在初始化完成后启动
        orchestrator.stop()
        val startAfterInit = orchestrator.start(pcmDataFlow)
        assertTrue("Should be able to start after initialization", startAfterInit)
        
        orchestrator.release()
    }
    
    @Test
    fun `test PCM data processing during initialization`() = runBlocking {
        val pcmDataFlow = MutableSharedFlow<ByteArray>()
        val orchestrator = OfflineOrchestrator(mockContext())
        
        // 启动配置和处理
        orchestrator.setConfig("/mock/model/path")
        orchestrator.start(pcmDataFlow)
        
        // 在初始化期间发送多个PCM数据包
        repeat(10) { index ->
            val pcmData = ByteArray(640) { (index * 10 + it).toByte() }
            pcmDataFlow.emit(pcmData)
            delay(50) // 模拟实时音频流
        }
        
        // 等待初始化完成
        delay(2000)
        
        // 继续发送数据，这些应该直接处理
        repeat(5) { index ->
            val pcmData = ByteArray(640) { (index * 20 + it).toByte() }
            pcmDataFlow.emit(pcmData)
            delay(50)
        }
        
        orchestrator.release()
    }

    @Test
    fun `test initialization timeout mechanism`() = runBlocking {
        val pcmDataFlow = MutableSharedFlow<ByteArray>()
        val orchestrator = OfflineOrchestrator(mockContext())

        // 设置一个无效的模型路径来模拟初始化超时
        orchestrator.setConfig("/invalid/model/path")
        orchestrator.start(pcmDataFlow)

        // 发送一些PCM数据，这些数据应该被缓存
        val testPcmData = ByteArray(640) { it.toByte() }
        pcmDataFlow.emit(testPcmData)

        // 验证初始状态
        val initialStatus = orchestrator.getStatus()
        assertTrue("Should be initializing", initialStatus.isInitializing)
        assertTrue("Should have cached data", initialStatus.engineStatus.cachedDataCount > 0)

        // 等待超过超时时间（10秒 + 缓冲时间）
        delay(12000)

        // 验证超时后的状态
        val timeoutStatus = orchestrator.getStatus()
        assertTrue("Should be in error state after timeout", timeoutStatus.isError)
        assertTrue("Should be initialization timeout error", timeoutStatus.engineStatus.isInitializationTimeout)
        assertFalse("Should not be initializing after timeout", timeoutStatus.isInitializing)
        assertEquals("Cached data should be cleared after timeout", 0, timeoutStatus.engineStatus.cachedDataCount)

        orchestrator.release()
    }

    @Test
    fun `test timeout error handling and recovery`() = runBlocking {
        val pcmDataFlow = MutableSharedFlow<ByteArray>()
        val orchestrator = OfflineOrchestrator(mockContext())

        // 第一次尝试：使用无效路径导致超时
        orchestrator.setConfig("/invalid/model/path")
        orchestrator.start(pcmDataFlow)

        // 等待超时
        delay(12000)

        val timeoutStatus = orchestrator.getStatus()
        assertTrue("Should be in error state", timeoutStatus.isError)
        assertTrue("Should be timeout error", timeoutStatus.engineStatus.isInitializationTimeout)

        // 停止并重新配置
        orchestrator.stop()

        // 第二次尝试：使用有效路径
        orchestrator.setConfig("/mock/valid/model/path")
        val retryResult = orchestrator.start(pcmDataFlow)
        assertTrue("Should be able to retry after timeout", retryResult)

        // 验证恢复状态
        val recoveryStatus = orchestrator.getStatus()
        assertTrue("Should be initializing again", recoveryStatus.isInitializing)

        orchestrator.release()
    }

    @Test
    fun `test status change listener receives timeout notification`() = runBlocking {
        val pcmDataFlow = MutableSharedFlow<ByteArray>()
        val orchestrator = OfflineOrchestrator(mockContext())

        var timeoutNotificationReceived = false
        var lastStatus: OfflineOrchestratorStatus? = null

        // 设置状态变化监听器
        orchestrator.setStatusChangeListener { status ->
            lastStatus = status
            if (status.isError && status.engineStatus.isInitializationTimeout) {
                timeoutNotificationReceived = true
            }
        }

        // 触发超时
        orchestrator.setConfig("/invalid/model/path")
        orchestrator.start(pcmDataFlow)

        // 等待超时
        delay(12000)

        // 验证通知
        assertTrue("Should receive timeout notification", timeoutNotificationReceived)
        assertNotNull("Should have last status", lastStatus)
        assertTrue("Last status should indicate timeout error", lastStatus!!.engineStatus.isInitializationTimeout)

        orchestrator.release()
    }

    private fun mockContext(): android.content.Context {
        // 在实际测试中，这里应该返回一个模拟的Context
        // 为了简化，这里返回null，实际使用时需要适当的模拟
        return org.mockito.Mockito.mock(android.content.Context::class.java)
    }
}
