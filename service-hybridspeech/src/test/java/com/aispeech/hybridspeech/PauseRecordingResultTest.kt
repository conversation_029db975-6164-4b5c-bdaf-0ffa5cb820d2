package com.aispeech.hybridspeech

import org.junit.Test
import org.junit.Assert.*

/**
 * 暂停录音结果测试
 */
class PauseRecordingResultTest {

    @Test
    fun testOnlineModeWithFinalTextReceived() {
        val result = PauseRecordingResult(
            isOnlineMode = true,
            recordingDurationMs = 30000L,
            finalTextReceived = true
        )
        
        assertTrue(result.isOnlineMode)
        assertEquals(30000L, result.recordingDurationMs)
        assertEquals(true, result.finalTextReceived)
    }

    @Test
    fun testOnlineModeWithoutFinalTextReceived() {
        val result = PauseRecordingResult(
            isOnlineMode = true,
            recordingDurationMs = 25000L,
            finalTextReceived = false
        )
        
        assertTrue(result.isOnlineMode)
        assertEquals(25000L, result.recordingDurationMs)
        assertEquals(false, result.finalTextReceived)
    }

    @Test
    fun testOfflineMode() {
        val result = PauseRecordingResult(
            isOnlineMode = false,
            recordingDurationMs = 45000L,
            finalTextReceived = null
        )
        
        assertFalse(result.isOnlineMode)
        assertEquals(45000L, result.recordingDurationMs)
        assertNull(result.finalTextReceived)
    }

    @Test
    fun testDefaultValues() {
        val result = PauseRecordingResult(
            isOnlineMode = false,
            recordingDurationMs = 10000L
        )
        
        assertFalse(result.isOnlineMode)
        assertEquals(10000L, result.recordingDurationMs)
        assertNull(result.finalTextReceived)
    }
}
