# AiTabletMDUtil 使用指南

## 问题背景

1. 原有的 `getAiMD()` 方法返回 `JSONObject`（fastjson），但外部模块可能没有 fastjson 依赖，无法直接使用返回的对象。
2. 原有的 `AiTaskDataMapper.mapFromJsonData()` 方法接受 `JSONObject` 参数，但外部模块希望直接传入 JSON 字符串。

## 解决方案

新增了多个便于外部使用的辅助方法，让外部模块可以：
1. 直接获取具体内容，无需依赖 fastjson
2. 直接传入 JSON 字符串进行处理，无需手动解析 JSON

## 新增方法说明

### 方案一：直接处理 JSON 字符串（推荐外部模块使用）

#### 1. 从 JSON 字符串获取完整结果（最推荐）

```java
/**
 * 直接从 JSON 字符串获取完整处理结果
 * 外部模块的一站式解决方案，无需依赖 fastjson
 */
public static AiMDResult getAiMDResultFromJson(String jsonString, String template)
```

**使用示例：**
```java
String jsonString = "{\"dialog_id\":\"123\",\"abstract\":\"会议摘要\",\"content\":[...]}";
String template = "您的模板内容";

AiTabletMDUtil.AiMDResult result = AiTabletMDUtil.getAiMDResultFromJson(jsonString, template);

// 获取各种信息
String content = result.getContent();
List<String> keyPhrases = result.getKeyPhrases();
List<String> todoList = result.getTodoList();
String meetingType = result.getMeetingType();
```

#### 2. 从 JSON 字符串获取 Markdown 内容

```java
/**
 * 直接从 JSON 字符串生成 Markdown 内容
 */
public static String getMarkdownContentFromJson(String jsonString, String template)
```

**使用示例：**
```java
String markdownContent = AiTabletMDUtil.getMarkdownContentFromJson(jsonString, template);
```

#### 3. 从 JSON 字符串获取关键词

```java
public static List<String> getKeyPhrasesFromJson(String jsonString, String template)
```

#### 4. 从 JSON 字符串获取待办事项

```java
public static List<String> getTodoListFromJson(String jsonString, String template)
```

#### 5. JSON 字符串解析

```java
/**
 * 将JSON字符串转换为AiTabletAITask对象
 * 外部模块可以直接传入JSON字符串，无需依赖fastjson
 */
public static AiTabletAITask mapFromJsonData(String jsonString)
```

### 方案二：先解析再处理

#### 1. 获取 Markdown 内容

```java
/**
 * 获取生成的 Markdown 内容（字符串格式）
 * 外部模块可以直接调用此方法获取 Markdown 内容，无需依赖 fastjson
 */
public static String getMarkdownContent(AiTabletAITask aiTask, String template)
```

**使用示例：**
```java
String markdownContent = AiTabletMDUtil.getMarkdownContent(aiTask, template);
System.out.println(markdownContent);
```

### 2. 获取关键词列表

```java
/**
 * 获取提取的关键词列表
 */
public static List<String> getKeyPhrases(AiTabletAITask aiTask, String template)
```

**使用示例：**
```java
List<String> keyPhrases = AiTabletMDUtil.getKeyPhrases(aiTask, template);
System.out.println("关键词: " + keyPhrases);
```

### 3. 获取待办事项列表

```java
/**
 * 获取待办事项列表
 */
public static List<String> getTodoList(AiTabletAITask aiTask, String template)
```

**使用示例：**
```java
List<String> todoList = AiTabletMDUtil.getTodoList(aiTask, template);
System.out.println("待办事项: " + todoList);
```

### 4. 获取会议类型

```java
/**
 * 获取会议类型
 */
public static String getMeetingType(AiTabletAITask aiTask, String template)
```

**使用示例：**
```java
String meetingType = AiTabletMDUtil.getMeetingType(aiTask, template);
System.out.println("会议类型: " + meetingType);
```

### 5. 获取完整结果（推荐）

```java
/**
 * 获取完整的处理结果（推荐使用）
 * 外部模块调用此方法可以获取所有信息，无需依赖 fastjson
 */
public static AiMDResult getAiMDResult(AiTabletAITask aiTask, String template)
```

**AiMDResult 类说明：**
```java
public static class AiMDResult {
    private String content;           // Markdown 内容
    private List<String> keyPhrases;  // 关键词列表
    private List<String> todoList;    // 待办事项列表
    private String meetingType;       // 会议类型
    
    // getter 方法
    public String getContent() { return content; }
    public List<String> getKeyPhrases() { return keyPhrases; }
    public List<String> getTodoList() { return todoList; }
    public String getMeetingType() { return meetingType; }
}
```

**使用示例：**
```java
AiTabletMDUtil.AiMDResult result = AiTabletMDUtil.getAiMDResult(aiTask, template);

// 获取各种信息
String content = result.getContent();
List<String> keyPhrases = result.getKeyPhrases();
List<String> todoList = result.getTodoList();
String meetingType = result.getMeetingType();

System.out.println("Markdown 内容: " + content);
System.out.println("关键词: " + keyPhrases);
System.out.println("待办事项: " + todoList);
System.out.println("会议类型: " + meetingType);
```

## 完整使用示例

### 示例1：外部模块直接使用 JSON 字符串（推荐）

```java
public class ExternalModuleExample {

    public void processAiTaskFromJson() {
        // 外部模块有 JSON 字符串数据
        String jsonString = getJsonDataFromSomewhere(); // 获取 JSON 字符串
        String template = createTemplate(); // 创建模板

        // 方式1: 一站式获取所有结果（最推荐）
        AiTabletMDUtil.AiMDResult result = AiTabletMDUtil.getAiMDResultFromJson(jsonString, template);

        // 使用结果
        displayMarkdown(result.getContent());
        processKeywords(result.getKeyPhrases());
        handleTodos(result.getTodoList());
        setMeetingType(result.getMeetingType());

        // 方式2: 只需要 Markdown 内容
        String markdownContent = AiTabletMDUtil.getMarkdownContentFromJson(jsonString, template);
        displayMarkdown(markdownContent);
    }

    public void processAiTaskStepByStep() {
        // 如果需要分步处理
        String jsonString = getJsonDataFromSomewhere();
        String template = createTemplate();

        // 步骤1: 解析 JSON 字符串
        AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonString);
        if (aiTask == null) {
            System.err.println("JSON 解析失败");
            return;
        }

        // 步骤2: 处理任务
        AiTabletMDUtil.AiMDResult result = AiTabletMDUtil.getAiMDResult(aiTask, template);

        // 使用结果
        displayMarkdown(result.getContent());
        processKeywords(result.getKeyPhrases());
        handleTodos(result.getTodoList());
        setMeetingType(result.getMeetingType());
    }

    // 无需依赖 fastjson 的方法
    private String getJsonDataFromSomewhere() {
        return "{\"dialog_id\":\"123\",\"abstract\":\"会议摘要\",\"content\":[...]}";
    }

    private String createTemplate() {
        return "<AiParagraphs><AiTitle>## {{AiTitle}}</AiTitle></AiParagraphs>";
    }

    private void displayMarkdown(String content) {
        // 显示 Markdown 内容
    }

    private void processKeywords(List<String> keywords) {
        // 处理关键词
    }

    private void handleTodos(List<String> todos) {
        // 处理待办事项
    }

    private void setMeetingType(String meetingType) {
        // 设置会议类型
    }
}
```

### 示例2：JSON 字符串格式

```json
{
  "dialog_id": "24ae12d7-c47d-4725-a820-e2c61bcaac45",
  "abstract": "会议首先讨论了测试流程的简化与执行，包括步骤顺序和匹配显示问题...",
  "content": [
    {
      "topic": "会议讨论测试流程和系统功能问题",
      "sub_topic_list": [
        {
          "sub_topic": "测试流程讨论",
          "points": [
            "说话人1和2讨论了测试流程的简化和执行，包括测试的步骤和顺序。",
            "说话人2提到测试流程的匹配和显示问题，以及测试的详细日志。"
          ]
        }
      ],
      "key_phrases": ["测试流程", "系统功能"],
      "conclusions": ["需要优化测试流程", "修复系统功能问题"]
    }
  ]
}
```

## 优势

1. **无依赖**: 外部模块无需引入 fastjson 依赖
2. **类型安全**: 返回具体的 Java 类型，避免类型转换错误
3. **易于使用**: 方法名清晰，直接返回所需内容
4. **向后兼容**: 原有的 `getAiMD()` 和 `mapFromJsonData(JSONObject)` 方法保持不变
5. **异常处理**: 内置异常处理，失败时返回默认值而不是抛出异常
6. **一站式处理**: 可以直接从 JSON 字符串获取结果，无需分步操作
7. **灵活性**: 提供多种使用方式，满足不同场景需求

## 推荐使用方式

### 外部模块（无 fastjson 依赖）

**最推荐**: 直接使用 JSON 字符串处理
```java
// 一行代码获取所有结果
AiTabletMDUtil.AiMDResult result = AiTabletMDUtil.getAiMDResultFromJson(jsonString, template);

// 或者只获取 Markdown 内容
String content = AiTabletMDUtil.getMarkdownContentFromJson(jsonString, template);
```

**备选方案**: 分步处理
```java
// 先解析
AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonString);
// 再处理
AiTabletMDUtil.AiMDResult result = AiTabletMDUtil.getAiMDResult(aiTask, template);
```

### 内部模块（有 fastjson 依赖）

可以继续使用原有方法：
```java
JSONObject result = AiTabletMDUtil.getAiMD(aiTask, template);
AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonObject);
```

## 注意事项

1. **异常处理**: 所有新方法都包含异常处理，失败时返回默认值（空字符串或空列表）
2. **JSON 格式**: 确保传入的 JSON 字符串格式正确，包含必要的字段（dialog_id、abstract、content）
3. **性能考虑**: 如果需要多次获取不同信息，推荐使用 `getAiMDResultFromJson()` 一次获取所有结果
4. **向后兼容**: 原有方法保持不变，可以逐步迁移到新方法
5. **错误处理**: JSON 解析失败时会在控制台输出错误信息，但不会抛出异常
