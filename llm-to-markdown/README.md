# LLM to Markdown Android AAR 模块

这个模块将原本的Java服务代码改造为Android AAR库，用于处理AI会议记录数据并生成Markdown格式的输出。

## 功能特性

- 支持将AI会议记录数据转换为Markdown格式
- 兼容Android平台，处理Java 8时间API兼容性问题
- 支持多种会议内容类型：摘要、主题、关键词、待办事项等
- 提供完整的数据映射和转换功能

## 主要类说明

### 核心类

1. **AiTabletMDUtil** - 主要的Markdown生成工具类
2. **AiTabletAITask** - AI任务数据实体类
3. **AiTaskDataMapper** - JSON数据到对象的映射器

### 数据模型类

- **AiTabletAbstractVO** - 摘要信息VO
- **AiTabletTodoVO** - 待办事项VO
- **FocusOnVo** - 重点关注信息VO
- **LLMQSResp** - 问答响应类
- **AiTabletMeetingTypeVO** - 会议类型VO

## 使用方法

### 1. 基本使用

```java
// 准备JSON数据
JSONObject jsonData = new JSONObject();
jsonData.put("dialog_id", "24ae12d7-c47d-4725-a820-e2c61bcaac45");
jsonData.put("abstract", "会议摘要内容...");
jsonData.put("content", contentArray);

// 创建AiTabletAITask对象
AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonData);

// 定义模板
String template = "<AiParagraphs>\n" +
    "<AiTitle>## {{AiTitle}} </AiTitle>\n" +
    "<AiAbstract> {{AiAbstract}} </AiAbstract>\n" +
    "</AiParagraphs>";

// 生成Markdown
JSONObject result = AiTabletMDUtil.getAiMD(aiTask, template);
String markdownContent = result.getString("content");
```

### 2. 使用示例数据

```java
// 使用提供的示例数据
AiTabletAITask aiTask = AiTaskExample.createTaskFromProvidedData();

// 生成Markdown
JSONObject result = AiTabletMDUtil.getAiMD(aiTask, template);
```

### 3. 自定义数据映射

```java
// 直接创建任务对象
AiTabletAITask aiTask = AiTaskDataMapper.createAiTask(
    "dialog-id-123",
    "会议摘要内容",
    contentArray
);
```

## JSON数据格式

输入的JSON数据应包含以下字段：

```json
{
  "dialog_id": "对话ID",
  "abstract": "会议摘要内容",
  "content": [
    {
      "topic": "主题",
      "sub_topic_list": [
        {
          "sub_topic": "子主题",
          "points": ["要点1", "要点2"]
        }
      ],
      "conclusions": ["结论1", "结论2"],
      "key_phrases": ["关键词1", "关键词2"]
    }
  ]
}
```

## 模板系统

支持以下模板标签：

- `{{AiTitle}}` - 标题
- `{{AiTimestamp}}` - 时间戳
- `{{AiAbstract}}` - 摘要内容
- `{{AiTopic}}` - 主题
- `{{AiPoint}}` - 要点
- `{{AiKeyPhrase}}` - 关键词

## Android要求

### 时间API要求

模块使用Java 8时间API，要求：
- Android API 26+ (Android 8.0+)

### 依赖要求

```gradle
dependencies {
    implementation 'com.alibaba:fastjson:1.2.83'
    implementation 'org.apache.commons:commons-lang3:3.12.0'
    implementation 'org.apache.commons:commons-collections4:4.4'
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
}
```

## 注意事项

1. **线程安全**：主要工具类是线程安全的，可以在多线程环境中使用
2. **内存使用**：处理大量数据时注意内存使用情况
3. **错误处理**：所有公共方法都包含适当的错误处理和日志记录
4. **Android版本**：要求Android API 26+ (Android 8.0+)

## 测试

运行示例代码：

```java
AiTaskExample.main(new String[]{});
```

这将使用提供的示例数据生成Markdown内容并输出到控制台。

## 文件结构

```
llm-to-markdown/
├── src/main/java/com/aispeech/llm2markdwon/
│   ├── AiTabletMDUtil.java           # 主要工具类
│   ├── AiTabletAITask.java           # AI任务实体类
│   ├── AiTaskDataMapper.java         # JSON数据映射器
│   ├── AiTaskExample.java            # 使用示例
│   ├── TestRunner.java               # 测试类
│   ├── AiTabletAbstractVO.java       # 摘要VO
│   ├── AiTabletTodoVO.java          # 待办VO
│   ├── FocusOnVo.java               # 重点关注VO
│   ├── LLMQSResp.java               # 问答响应类
│   ├── AiTabletMeetingTypeVO.java   # 会议类型VO
│   ├── AiMDLabelEnum.java           # 标签枚举
│   └── MarkdownUtil.java            # Markdown工具
├── build.gradle.kts                 # 构建脚本
└── README.md                        # 使用文档
```

## 版本历史

- v1.0 - 初始版本，支持基本的Markdown生成功能
