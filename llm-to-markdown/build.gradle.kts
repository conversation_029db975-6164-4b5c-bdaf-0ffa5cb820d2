/*
 * Copyright (C) 2023 AISPEECH. All rights reserved.
 *
 * This software is the confidential and proprietary information of AI Speech Co., Ltd..
 * Unauthorized copying, modification, publication, or use of this software, either
 * in part or in whole, is strictly prohibited without the prior written consent of <PERSON><PERSON><PERSON><PERSON>.
 *
 * For authorization inquiries, please contact AISPEECH at www.aispeech.com.
 */

plugins {
  id("com.android.library")
}

android {
  namespace = "com.aispeech.llm2markdwon"
  compileSdk = Configurations.compileSdk

  defaultConfig {
    minSdk = Configurations.minSdk
    targetSdk = Configurations.targetSdk
  }

  compileOptions {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
  }
}

dependencies {
  implementation("com.alibaba:fastjson:1.2.83")
  implementation("org.apache.commons:commons-lang3:3.12.0")
  implementation("org.apache.commons:commons-collections4:4.4")

  implementation("androidx.core:core-ktx:1.12.0")
  implementation("androidx.appcompat:appcompat:1.6.1")

  // 测试依赖
  testImplementation("junit:junit:4.13.2")
  testImplementation("org.hamcrest:hamcrest-core:1.3")
  testImplementation("org.mockito:mockito-core:4.6.1")

  androidTestImplementation("androidx.test.ext:junit:1.1.5")
  androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
}