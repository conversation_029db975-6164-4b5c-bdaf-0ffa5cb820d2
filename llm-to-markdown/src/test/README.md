# LLM to Markdown 测试文档

本目录包含了LLM to Markdown模块的完整测试套件，使用您提供的真实JSON数据进行测试。

## 测试文件结构

```
src/test/java/com/aispeech/llm2markdwon/
├── AiTabletMDUtilTest.java          # 主要工具类测试
├── AiTaskDataMapperTest.java        # 数据映射器测试
├── IntegrationTest.java             # 集成测试
├── TestSuite.java                   # JUnit测试套件
├── SimpleTestRunner.java            # 简单测试运行器
└── README.md                        # 本文档
```

## 测试数据

所有测试都使用您提供的真实JSON数据：

```json
{
  "dialog_id": "24ae12d7-c47d-4725-a820-e2c61bcaac45",
  "abstract": "会议首先讨论了测试流程的简化与执行，包括步骤顺序和匹配显示问题，同时涉及测试日志的详细记录；其次针对系统功能问题，涉及连接匹配、高亮显示及版本同步等保护修复措施；最后明确了后续工作需聚焦流程优化与功能修复。",
  "content": [
    {
      "topic": "会议讨论测试流程和系统功能问题",
      "sub_topic_list": [
        {
          "sub_topic": "测试流程讨论",
          "points": [
            "说话人1和2讨论了测试流程的简化和执行，包括测试的步骤和顺序。",
            "说话人2提到测试流程的匹配和显示问题，以及测试的详细日志。"
          ]
        },
        {
          "sub_topic": "系统功能问题讨论",
          "points": [
            "说话人2提到系统功能的连接和匹配问题，以及高亮位置的显示问题。",
            "说话人3讨论了系统功能的保护和修复，以及版本的保存和同步问题。"
          ]
        }
      ],
      "conclusions": [],
      "key_phrases": ["冒烟", "测试测试测试", "功能流程", "版本", "高亮位置"]
    }
  ]
}
```

## 测试类说明

### 1. AiTabletMDUtilTest.java
- **功能**: 测试主要的Markdown生成工具类
- **测试内容**:
  - JSON数据映射验证
  - Markdown生成功能
  - 完整工作流程测试
- **关键验证点**:
  - Dialog ID正确映射
  - 摘要内容完整性
  - 关键词提取准确性
  - 生成的Markdown格式正确性

### 2. AiTaskDataMapperTest.java
- **功能**: 专门测试JSON数据映射功能
- **测试内容**:
  - 真实数据映射测试
  - 空数据处理测试
  - 复杂数据结构处理
  - 便捷创建方法测试
- **关键验证点**:
  - 所有字段正确映射
  - 嵌套结构正确解析
  - 数组数据正确处理
  - 异常情况正确处理

### 3. IntegrationTest.java
- **功能**: 端到端集成测试
- **测试内容**:
  - 真实场景模拟
  - 性能测试（100次处理）
  - 完整工作流程验证
- **关键验证点**:
  - 整个处理链路正常
  - 性能满足要求（<100ms/次）
  - 结果数据完整性

### 4. SimpleTestRunner.java
- **功能**: 独立测试运行器，不依赖JUnit
- **特点**:
  - 可以直接运行，无需测试框架
  - 提供详细的测试报告
  - 包含性能统计
  - 友好的控制台输出

## 运行测试

### 方法1: 使用JUnit（推荐）

```bash
# 运行所有测试
./gradlew test

# 运行特定测试类
./gradlew test --tests AiTabletMDUtilTest

# 运行测试套件
./gradlew test --tests TestSuite
```

### 方法2: 使用简单测试运行器

```bash
# 编译项目
./gradlew build

# 直接运行SimpleTestRunner
java -cp "build/classes/java/test:build/classes/java/main:libs/*" \
  com.aispeech.llm2markdwon.SimpleTestRunner
```

### 方法3: 在IDE中运行

1. 打开任意测试类
2. 右键选择"Run"或"Debug"
3. 查看测试结果

## 测试覆盖的功能点

### ✅ 数据处理
- [x] JSON解析和验证
- [x] 对象映射和转换
- [x] 嵌套数据结构处理
- [x] 数组数据处理
- [x] 空值和异常处理

### ✅ Markdown生成
- [x] 模板解析
- [x] 内容替换
- [x] 格式化输出
- [x] 特殊字符处理
- [x] 多段落处理

### ✅ 关键信息提取
- [x] 关键词提取
- [x] 主题识别
- [x] 要点整理
- [x] 会议类型识别

### ✅ 性能和稳定性
- [x] 批量处理性能
- [x] 内存使用优化
- [x] 异常恢复能力
- [x] 边界条件处理

## 预期测试结果

运行所有测试后，您应该看到类似以下的输出：

```
🚀 开始运行LLM to Markdown模块测试
============================================================

📋 测试基本功能...
  ✅ AiTabletAITask对象创建
  ✅ VO对象创建

🔄 测试数据映射...
  ✅ JSON数据解析
  ✅ 数据映射到AiTask

📝 测试Markdown生成...
  ✅ Markdown生成

🌍 测试真实场景...
  ✅ 完整流程处理
    ✓ 生成内容长度: 1234
    ✓ 提取关键词: [冒烟, 测试测试测试, 功能流程, 版本, 高亮位置]

⚠️  测试错误处理...
  ✅ 空数据处理
  ✅ 空模板处理

============================================================
📊 测试结果总结
============================================================
总测试数: 8
通过测试: 8 ✅
失败测试: 0 ❌
成功率: 100.0%

🎉 所有测试通过！模块功能正常！
```

## 故障排除

如果测试失败，请检查：

1. **依赖问题**: 确保所有依赖库已正确添加
2. **Android版本**: 确保Android API 26+
3. **JSON格式**: 确保输入的JSON数据格式正确
4. **模板格式**: 确保Markdown模板格式正确

## 添加新测试

要添加新的测试用例：

1. 在相应的测试类中添加新的`@Test`方法
2. 使用真实数据进行测试
3. 添加适当的断言验证
4. 更新本文档说明新增的测试内容
