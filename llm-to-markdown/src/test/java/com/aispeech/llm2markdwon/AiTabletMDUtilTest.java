package com.aispeech.llm2markdwon;

import com.alibaba.fastjson.JSONObject;
import org.junit.Before;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * AiTabletMDUtil 测试类
 * 使用提供的真实数据进行测试
 */
public class AiTabletMDUtilTest {
    
    private JSONObject testJsonData;
    private String defaultTemplate;
    
    @Before
    public void setUp() {
        // 使用您提供的真实JSON数据
        String jsonString = "{\n" +
                "  \"dialog_id\": \"24ae12d7-c47d-4725-a820-e2c61bcaac45\",\n" +
                "  \"abstract\": \"会议首先讨论了测试流程的简化与执行，包括步骤顺序和匹配显示问题，同时涉及测试日志的详细记录；其次针对系统功能问题，涉及连接匹配、高亮显示及版本同步等保护修复措施；最后明确了后续工作需聚焦流程优化与功能修复。\",\n" +
                "  \"content\": [\n" +
                "    {\n" +
                "      \"topic\": \"会议讨论测试流程和系统功能问题\",\n" +
                "      \"sub_topic_list\": [\n" +
                "        {\n" +
                "          \"sub_topic\": \"测试流程讨论\",\n" +
                "          \"points\": [\n" +
                "            \"说话人1和2讨论了测试流程的简化和执行，包括测试的步骤和顺序。\",\n" +
                "            \"说话人2提到测试流程的匹配和显示问题，以及测试的详细日志。\"\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"sub_topic\": \"系统功能问题讨论\",\n" +
                "          \"points\": [\n" +
                "            \"说话人2提到系统功能的连接和匹配问题，以及高亮位置的显示问题。\",\n" +
                "            \"说话人3讨论了系统功能的保护和修复，以及版本的保存和同步问题。\"\n" +
                "          ]\n" +
                "        }\n" +
                "      ],\n" +
                "      \"conclusions\": [],\n" +
                "      \"key_phrases\": [\"冒烟\", \"测试测试测试\", \"功能流程\", \"版本\", \"高亮位置\"]\n" +
                "    },\n" +
                "    {\n" +
                "      \"topic\": \"\",\n" +
                "      \"sub_topic_list\": [],\n" +
                "      \"conclusions\": [],\n" +
                "      \"key_phrases\": []\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        
        testJsonData = JSONObject.parseObject(jsonString);
        
        // 设置默认模板
        defaultTemplate = "<AiParagraphs>\n" +
                "<AiCommon> <center> </AiCommon>\n" +
                "<AiTitle>## {{AiTitle}} </AiTitle>\n" +
                "<AiTimestamp> {{AiTimestamp}} </AiTimestamp>\n" +
                "<AiCommon> </center> </AiCommon>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiAbstractTitle>#### {{AiAbstractTitle}} </AiAbstractTitle>\n" +
                "<AiCommon> <rounded> </AiCommon>\n" +
                "<AiAbstract> {{AiAbstract}} </AiAbstract>\n" +
                "<AiCommon> </rounded> </AiCommon>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiKeyPhrase> {{AiKeyPhrase}}</AiKeyPhrase>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiTopicTitle>#### {{AiTopicTitle}} </AiTopicTitle>\n" +
                "<AiCommon> <details> </AiCommon>\n" +
                "<AiCommon> <rounded> </AiCommon>\n" +
                "<AiTopic>{{AiTopic}}</AiTopic>\n" +
                "<AiPoint>{{AiPoint}}</AiPoint>\n" +
                "<AiCommon> </rounded> </AiCommon>\n" +
                "<AiCommon> </details> </AiCommon>\n" +
                "</AiParagraphs>";
    }
    
    @Test
    public void testJsonDataMapping() {
        System.out.println("=== 测试JSON数据映射 ===");
        
        // 执行映射
        AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(testJsonData);
        
        // 验证基本信息
        assertNotNull("AiTask不应为null", aiTask);
        assertEquals("dialog_id应该正确映射", "24ae12d7-c47d-4725-a820-e2c61bcaac45", aiTask.getObjectId());
        assertNotNull("摘要信息不应为null", aiTask.getAbstractInfo());
        assertEquals("摘要内容应该正确", testJsonData.getString("abstract"), aiTask.getAbstractInfo().getContent());
        
        // 验证详细内容
        assertNotNull("详细内容不应为null", aiTask.getAbstractInfo().getDetail());
        assertTrue("应该有详细内容", aiTask.getAbstractInfo().getDetail().size() > 0);
        
        AiTabletAbstractVO.AbstractContent firstDetail = aiTask.getAbstractInfo().getDetail().get(0);
        assertEquals("第一个主题应该正确", "会议讨论测试流程和系统功能问题", firstDetail.getTopic());
        assertNotNull("要点不应为null", firstDetail.getPoints());
        assertTrue("应该有要点", firstDetail.getPoints().size() > 0);
        assertNotNull("关键词不应为null", firstDetail.getKeyPhrases());
        assertTrue("应该有关键词", firstDetail.getKeyPhrases().size() > 0);
        
        System.out.println("✓ JSON数据映射测试通过");
        System.out.println("  - Dialog ID: " + aiTask.getObjectId());
        System.out.println("  - 摘要长度: " + aiTask.getAbstractInfo().getContent().length());
        System.out.println("  - 详细内容数量: " + aiTask.getAbstractInfo().getDetail().size());
        System.out.println("  - 第一个主题: " + firstDetail.getTopic());
        System.out.println("  - 要点数量: " + firstDetail.getPoints().size());
        System.out.println("  - 关键词数量: " + firstDetail.getKeyPhrases().size());
    }
    
    @Test
    public void testMarkdownGeneration() {
        System.out.println("\n=== 测试Markdown生成 ===");
        
        // 创建AiTask对象
        AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(testJsonData);
        
        // 生成Markdown
        JSONObject result = AiTabletMDUtil.getAiMD(aiTask, defaultTemplate);
        
        // 验证结果
        assertNotNull("结果不应为null", result);
        assertTrue("应该包含content字段", result.containsKey("content"));
        assertNotNull("content不应为null", result.getString("content"));
        assertFalse("content不应为空", result.getString("content").trim().isEmpty());
        
        // 验证关键词提取
        if (result.containsKey("key")) {
            assertNotNull("关键词不应为null", result.get("key"));
            System.out.println("✓ 关键词提取成功: " + result.get("key"));
        }
        
        // 验证会议类型
        if (result.containsKey("meetingType")) {
            assertNotNull("会议类型不应为null", result.getString("meetingType"));
            System.out.println("✓ 会议类型: " + result.getString("meetingType"));
        }
        
        String content = result.getString("content");
        System.out.println("✓ Markdown生成测试通过");
        System.out.println("  - 生成内容长度: " + content.length());
        System.out.println("  - 包含摘要: " + content.contains("内容概括"));
        System.out.println("  - 包含主要内容: " + content.contains("主要内容"));
        
        // 输出生成的Markdown内容（前200个字符）
        System.out.println("  - 内容预览: " + content.substring(0, Math.min(200, content.length())) + "...");
    }
    
    @Test
    public void testCompleteWorkflow() {
        System.out.println("\n=== 测试完整工作流程 ===");
        
        try {
            // 步骤1: 数据映射
            AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(testJsonData);
            assertNotNull("步骤1失败: AiTask创建失败", aiTask);
            System.out.println("✓ 步骤1: 数据映射成功");
            
            // 步骤2: Markdown生成
            JSONObject result = AiTabletMDUtil.getAiMD(aiTask, defaultTemplate);
            assertNotNull("步骤2失败: Markdown生成失败", result);
            System.out.println("✓ 步骤2: Markdown生成成功");
            
            // 步骤3: 结果验证
            String content = result.getString("content");
            assertNotNull("步骤3失败: 内容为null", content);
            assertFalse("步骤3失败: 内容为空", content.trim().isEmpty());
            System.out.println("✓ 步骤3: 结果验证成功");
            
            System.out.println("✅ 完整工作流程测试通过!");
            
        } catch (Exception e) {
            fail("完整工作流程测试失败: " + e.getMessage());
        }
    }
}
