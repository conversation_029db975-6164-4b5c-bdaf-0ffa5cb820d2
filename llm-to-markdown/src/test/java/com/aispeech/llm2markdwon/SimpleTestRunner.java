package com.aispeech.llm2markdwon;

import com.alibaba.fastjson.JSONObject;

/**
 * 简单测试运行器
 * 不依赖JUnit框架，可以直接运行测试
 */
public class SimpleTestRunner {
    
    private static int totalTests = 0;
    private static int passedTests = 0;
    private static int failedTests = 0;
    
    public static void main(String[] args) {
        System.out.println("🚀 开始运行LLM to Markdown模块测试");
        System.out.println("=" .repeat(60));
        
        try {
            // 运行各项测试
            testBasicFunctionality();
            testDataMapping();
            testMarkdownGeneration();
            testRealWorldScenario();
            testErrorHandling();
            
            // 输出测试结果
            printTestSummary();
            
        } catch (Exception e) {
            System.err.println("❌ 测试运行过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试基本功能
     */
    private static void testBasicFunctionality() {
        System.out.println("\n📋 测试基本功能...");
        
        try {
            // 测试对象创建
            runTest("AiTabletAITask对象创建", () -> {
                AiTabletAITask task = new AiTabletAITask();
                task.setTitle("测试标题");
                task.setObjectId("test-123");
                return task.getTitle().equals("测试标题") && task.getObjectId().equals("test-123");
            });
            
            // 测试VO对象创建
            runTest("VO对象创建", () -> {
                AiTabletAbstractVO abstractVO = new AiTabletAbstractVO();
                abstractVO.setContent("测试内容");
                return abstractVO.getContent().equals("测试内容");
            });
            
        } catch (Exception e) {
            System.err.println("基本功能测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试数据映射
     */
    private static void testDataMapping() {
        System.out.println("\n🔄 测试数据映射...");
        
        try {
            // 使用您提供的真实数据
            String jsonString = "{\n" +
                    "  \"dialog_id\": \"24ae12d7-c47d-4725-a820-e2c61bcaac45\",\n" +
                    "  \"abstract\": \"会议首先讨论了测试流程的简化与执行，包括步骤顺序和匹配显示问题，同时涉及测试日志的详细记录；其次针对系统功能问题，涉及连接匹配、高亮显示及版本同步等保护修复措施；最后明确了后续工作需聚焦流程优化与功能修复。\",\n" +
                    "  \"content\": [\n" +
                    "    {\n" +
                    "      \"topic\": \"会议讨论测试流程和系统功能问题\",\n" +
                    "      \"sub_topic_list\": [\n" +
                    "        {\n" +
                    "          \"sub_topic\": \"测试流程讨论\",\n" +
                    "          \"points\": [\n" +
                    "            \"说话人1和2讨论了测试流程的简化和执行，包括测试的步骤和顺序。\",\n" +
                    "            \"说话人2提到测试流程的匹配和显示问题，以及测试的详细日志。\"\n" +
                    "          ]\n" +
                    "        }\n" +
                    "      ],\n" +
                    "      \"key_phrases\": [\"冒烟\", \"测试测试测试\", \"功能流程\", \"版本\", \"高亮位置\"]\n" +
                    "    }\n" +
                    "  ]\n" +
                    "}";
            
            JSONObject jsonData = JSONObject.parseObject(jsonString);
            
            runTest("JSON数据解析", () -> {
                return jsonData != null && jsonData.containsKey("dialog_id");
            });
            
            runTest("数据映射到AiTask", () -> {
                AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonData);
                return aiTask != null && 
                       aiTask.getObjectId().equals("24ae12d7-c47d-4725-a820-e2c61bcaac45") &&
                       aiTask.getAbstractInfo() != null &&
                       aiTask.getAbstractInfo().getDetail().size() > 0;
            });
            
        } catch (Exception e) {
            System.err.println("数据映射测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试Markdown生成
     */
    private static void testMarkdownGeneration() {
        System.out.println("\n📝 测试Markdown生成...");
        
        try {
            // 准备测试数据
            String jsonString = "{\n" +
                    "  \"dialog_id\": \"test-md-gen\",\n" +
                    "  \"abstract\": \"测试Markdown生成功能\",\n" +
                    "  \"content\": [\n" +
                    "    {\n" +
                    "      \"topic\": \"测试主题\",\n" +
                    "      \"key_phrases\": [\"测试\", \"Markdown\"]\n" +
                    "    }\n" +
                    "  ]\n" +
                    "}";
            
            JSONObject jsonData = JSONObject.parseObject(jsonString);
            AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonData);
            
            String template = "<AiParagraphs>\n" +
                    "<AiTitle>## {{AiTitle}} </AiTitle>\n" +
                    "<AiAbstract> {{AiAbstract}} </AiAbstract>\n" +
                    "</AiParagraphs>";
            
            runTest("Markdown生成", () -> {
                JSONObject result = AiTabletMDUtil.getAiMD(aiTask, template);
                return result != null && 
                       result.containsKey("content") && 
                       result.getString("content").length() > 0;
            });
            
        } catch (Exception e) {
            System.err.println("Markdown生成测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试真实场景
     */
    private static void testRealWorldScenario() {
        System.out.println("\n🌍 测试真实场景...");
        
        try {
            // 使用您提供的完整真实数据
            String realJsonData = "{\n" +
                    "  \"dialog_id\": \"24ae12d7-c47d-4725-a820-e2c61bcaac45\",\n" +
                    "  \"abstract\": \"会议首先讨论了测试流程的简化与执行，包括步骤顺序和匹配显示问题，同时涉及测试日志的详细记录；其次针对系统功能问题，涉及连接匹配、高亮显示及版本同步等保护修复措施；最后明确了后续工作需聚焦流程优化与功能修复。\",\n" +
                    "  \"content\": [\n" +
                    "    {\n" +
                    "      \"topic\": \"会议讨论测试流程和系统功能问题\",\n" +
                    "      \"sub_topic_list\": [\n" +
                    "        {\n" +
                    "          \"sub_topic\": \"测试流程讨论\",\n" +
                    "          \"points\": [\n" +
                    "            \"说话人1和2讨论了测试流程的简化和执行，包括测试的步骤和顺序。\",\n" +
                    "            \"说话人2提到测试流程的匹配和显示问题，以及测试的详细日志。\"\n" +
                    "          ]\n" +
                    "        },\n" +
                    "        {\n" +
                    "          \"sub_topic\": \"系统功能问题讨论\",\n" +
                    "          \"points\": [\n" +
                    "            \"说话人2提到系统功能的连接和匹配问题，以及高亮位置的显示问题。\",\n" +
                    "            \"说话人3讨论了系统功能的保护和修复，以及版本的保存和同步问题。\"\n" +
                    "          ]\n" +
                    "        }\n" +
                    "      ],\n" +
                    "      \"conclusions\": [],\n" +
                    "      \"key_phrases\": [\"冒烟\", \"测试测试测试\", \"功能流程\", \"版本\", \"高亮位置\"]\n" +
                    "    }\n" +
                    "  ]\n" +
                    "}";
            
            runTest("完整流程处理", () -> {
                JSONObject jsonData = JSONObject.parseObject(realJsonData);
                AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonData);
                
                String template = "<AiParagraphs>\n" +
                        "<AiTitle>## {{AiTitle}} </AiTitle>\n" +
                        "<AiAbstract> {{AiAbstract}} </AiAbstract>\n" +
                        "<AiTopic>{{AiTopic}}</AiTopic>\n" +
                        "</AiParagraphs>";
                
                JSONObject result = AiTabletMDUtil.getAiMD(aiTask, template);
                
                boolean success = result != null && 
                                result.containsKey("content") &&
                                result.getString("content").contains("测试流程") &&
                                result.containsKey("key");
                
                if (success) {
                    System.out.println("    ✓ 生成内容长度: " + result.getString("content").length());
                    System.out.println("    ✓ 提取关键词: " + result.get("key"));
                }
                
                return success;
            });
            
        } catch (Exception e) {
            System.err.println("真实场景测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试错误处理
     */
    private static void testErrorHandling() {
        System.out.println("\n⚠️  测试错误处理...");
        
        try {
            runTest("空数据处理", () -> {
                AiTabletAITask result = AiTaskDataMapper.mapFromJsonData(null);
                return result == null; // 应该返回null
            });
            
            runTest("空模板处理", () -> {
                AiTabletAITask aiTask = new AiTabletAITask();
                aiTask.setTitle("测试");
                try {
                    JSONObject result = AiTabletMDUtil.getAiMD(aiTask, "");
                    return result != null; // 应该能处理空模板
                } catch (Exception e) {
                    return true; // 或者抛出异常也是可以接受的
                }
            });
            
        } catch (Exception e) {
            System.err.println("错误处理测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 运行单个测试
     */
    private static void runTest(String testName, TestCase testCase) {
        totalTests++;
        try {
            boolean result = testCase.run();
            if (result) {
                System.out.println("  ✅ " + testName);
                passedTests++;
            } else {
                System.out.println("  ❌ " + testName + " - 测试失败");
                failedTests++;
            }
        } catch (Exception e) {
            System.out.println("  ❌ " + testName + " - 异常: " + e.getMessage());
            failedTests++;
        }
    }
    
    /**
     * 打印测试总结
     */
    private static void printTestSummary() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("📊 测试结果总结");
        System.out.println("=".repeat(60));
        System.out.println("总测试数: " + totalTests);
        System.out.println("通过测试: " + passedTests + " ✅");
        System.out.println("失败测试: " + failedTests + " ❌");
        System.out.println("成功率: " + String.format("%.1f", (double) passedTests / totalTests * 100) + "%");
        
        if (failedTests == 0) {
            System.out.println("\n🎉 所有测试通过！模块功能正常！");
        } else {
            System.out.println("\n⚠️  有 " + failedTests + " 个测试失败，请检查相关功能。");
        }
    }
    
    /**
     * 测试用例接口
     */
    @FunctionalInterface
    private interface TestCase {
        boolean run() throws Exception;
    }
}
