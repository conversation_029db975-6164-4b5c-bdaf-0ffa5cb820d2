package com.aispeech.llm2markdwon;

import org.junit.Test;
import java.util.List;
import static org.junit.Assert.*;

/**
 * 测试直接处理 JSON 字符串的新功能
 * 验证外部模块无需依赖 fastjson 即可使用
 */
public class JsonStringProcessingTest {
    
    private static final String TEST_JSON_STRING = "{\n" +
            "  \"dialog_id\": \"test-dialog-123\",\n" +
            "  \"abstract\": \"这是一个测试会议的摘要内容，讨论了项目进展和下一步计划。\",\n" +
            "  \"content\": [\n" +
            "    {\n" +
            "      \"topic\": \"项目进展讨论\",\n" +
            "      \"sub_topic_list\": [\n" +
            "        {\n" +
            "          \"sub_topic\": \"开发进度\",\n" +
            "          \"points\": [\n" +
            "            \"前端开发已完成80%\",\n" +
            "            \"后端API开发进度良好\"\n" +
            "          ]\n" +
            "        }\n" +
            "      ],\n" +
            "      \"key_phrases\": [\"项目进展\", \"开发进度\", \"API开发\"],\n" +
            "      \"conclusions\": [\"需要加快前端开发\", \"后端开发按计划进行\"]\n" +
            "    }\n" +
            "  ]\n" +
            "}";
    
    private static final String TEST_TEMPLATE = "<AiParagraphs>\n" +
            "<AiTitle>## {{AiTitle}}</AiTitle>\n" +
            "<AiAbstractTitle>#### {{AiAbstractTitle}}</AiAbstractTitle>\n" +
            "<AiAbstract>{{AiAbstract}}</AiAbstract>\n" +
            "<AiTopicTitle>#### {{AiTopicTitle}}</AiTopicTitle>\n" +
            "<AiTopic>{{AiTopic}}</AiTopic>\n" +
            "</AiParagraphs>";
    
    @Test
    public void testJsonStringParsing() {
        System.out.println("=== 测试 JSON 字符串解析 ===");
        
        // 测试 AiTaskDataMapper.mapFromJsonData(String)
        AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(TEST_JSON_STRING);
        
        assertNotNull("AI任务对象不应为null", aiTask);
        assertEquals("对话ID应该正确", "test-dialog-123", aiTask.getObjectId());
        assertNotNull("摘要信息不应为null", aiTask.getAbstractInfo());
        assertEquals("摘要内容应该正确", "这是一个测试会议的摘要内容，讨论了项目进展和下一步计划。", 
                     aiTask.getAbstractInfo().getContent());
        
        System.out.println("✓ JSON 字符串解析成功");
    }
    
    @Test
    public void testGetMarkdownContentFromJson() {
        System.out.println("=== 测试从 JSON 字符串获取 Markdown 内容 ===");
        
        String markdownContent = AiTabletMDUtil.getMarkdownContentFromJson(TEST_JSON_STRING, TEST_TEMPLATE);
        
        assertNotNull("Markdown 内容不应为null", markdownContent);
        assertFalse("Markdown 内容不应为空", markdownContent.trim().isEmpty());
        
        System.out.println("生成的 Markdown 内容长度: " + markdownContent.length());
        System.out.println("✓ 从 JSON 字符串获取 Markdown 内容成功");
    }
    
    @Test
    public void testGetKeyPhrasesFromJson() {
        System.out.println("=== 测试从 JSON 字符串获取关键词 ===");
        
        List<String> keyPhrases = AiTabletMDUtil.getKeyPhrasesFromJson(TEST_JSON_STRING, TEST_TEMPLATE);
        
        assertNotNull("关键词列表不应为null", keyPhrases);
        // 注意：关键词可能为空，因为模板中没有包含 AiKeyPhrase 标签
        
        System.out.println("提取的关键词: " + keyPhrases);
        System.out.println("✓ 从 JSON 字符串获取关键词成功");
    }
    
    @Test
    public void testGetTodoListFromJson() {
        System.out.println("=== 测试从 JSON 字符串获取待办事项 ===");
        
        List<String> todoList = AiTabletMDUtil.getTodoListFromJson(TEST_JSON_STRING, TEST_TEMPLATE);
        
        assertNotNull("待办事项列表不应为null", todoList);
        // 注意：待办事项可能为空，因为测试数据中没有 todoInfo
        
        System.out.println("提取的待办事项: " + todoList);
        System.out.println("✓ 从 JSON 字符串获取待办事项成功");
    }
    
    @Test
    public void testGetAiMDResultFromJson() {
        System.out.println("=== 测试从 JSON 字符串获取完整结果 ===");
        
        AiTabletMDUtil.AiMDResult result = AiTabletMDUtil.getAiMDResultFromJson(TEST_JSON_STRING, TEST_TEMPLATE);
        
        assertNotNull("结果对象不应为null", result);
        assertNotNull("Markdown 内容不应为null", result.getContent());
        assertNotNull("关键词列表不应为null", result.getKeyPhrases());
        assertNotNull("待办事项列表不应为null", result.getTodoList());
        assertNotNull("会议类型不应为null", result.getMeetingType());
        
        assertFalse("Markdown 内容不应为空", result.getContent().trim().isEmpty());
        
        System.out.println("完整结果:");
        System.out.println("- Markdown 内容长度: " + result.getContent().length());
        System.out.println("- 关键词数量: " + result.getKeyPhrases().size());
        System.out.println("- 待办事项数量: " + result.getTodoList().size());
        System.out.println("- 会议类型: " + result.getMeetingType());
        System.out.println("✓ 从 JSON 字符串获取完整结果成功");
    }
    
    @Test
    public void testInvalidJsonString() {
        System.out.println("=== 测试无效 JSON 字符串处理 ===");
        
        String invalidJson = "{ invalid json }";
        
        // 测试解析无效 JSON
        AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(invalidJson);
        assertNull("无效 JSON 应该返回 null", aiTask);
        
        // 测试从无效 JSON 获取结果
        AiTabletMDUtil.AiMDResult result = AiTabletMDUtil.getAiMDResultFromJson(invalidJson, TEST_TEMPLATE);
        assertNotNull("结果对象不应为null", result);
        assertEquals("内容应为空字符串", "", result.getContent());
        assertTrue("关键词列表应为空", result.getKeyPhrases().isEmpty());
        assertTrue("待办事项列表应为空", result.getTodoList().isEmpty());
        assertEquals("会议类型应为空字符串", "", result.getMeetingType());
        
        System.out.println("✓ 无效 JSON 字符串处理正确");
    }
    
    @Test
    public void testNullAndEmptyJsonString() {
        System.out.println("=== 测试 null 和空 JSON 字符串处理 ===");
        
        // 测试 null
        AiTabletAITask nullTask = AiTaskDataMapper.mapFromJsonData((String) null);
        assertNull("null 应该返回 null", nullTask);
        
        // 测试空字符串
        AiTabletAITask emptyTask = AiTaskDataMapper.mapFromJsonData("");
        assertNull("空字符串应该返回 null", emptyTask);
        
        // 测试从 null 获取结果
        AiTabletMDUtil.AiMDResult nullResult = AiTabletMDUtil.getAiMDResultFromJson(null, TEST_TEMPLATE);
        assertNotNull("结果对象不应为null", nullResult);
        assertEquals("内容应为空字符串", "", nullResult.getContent());
        
        System.out.println("✓ null 和空 JSON 字符串处理正确");
    }
    
    @Test
    public void testCompleteWorkflow() {
        System.out.println("=== 测试完整工作流程（外部模块使用场景）===");
        
        try {
            // 模拟外部模块的使用场景
            // 步骤1: 外部模块有 JSON 字符串
            String jsonData = TEST_JSON_STRING;
            String template = TEST_TEMPLATE;
            
            // 步骤2: 一行代码获取所有结果（推荐方式）
            AiTabletMDUtil.AiMDResult result = AiTabletMDUtil.getAiMDResultFromJson(jsonData, template);
            
            // 步骤3: 验证结果
            assertNotNull("结果不应为null", result);
            assertFalse("内容不应为空", result.getContent().trim().isEmpty());
            
            // 步骤4: 外部模块使用结果（无需依赖 fastjson）
            String content = result.getContent();
            List<String> keywords = result.getKeyPhrases();
            List<String> todos = result.getTodoList();
            String meetingType = result.getMeetingType();
            
            // 验证可以正常使用
            assertNotNull("内容不应为null", content);
            assertNotNull("关键词不应为null", keywords);
            assertNotNull("待办事项不应为null", todos);
            assertNotNull("会议类型不应为null", meetingType);
            
            System.out.println("✅ 完整工作流程测试通过!");
            System.out.println("外部模块可以无依赖地使用所有功能");
            
        } catch (Exception e) {
            fail("完整工作流程测试失败: " + e.getMessage());
        }
    }
}
