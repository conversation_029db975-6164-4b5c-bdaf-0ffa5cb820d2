package com.aispeech.llm2markdwon;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import static org.junit.Assert.*;

/**
 * AiTaskDataMapper 测试类
 * 专门测试JSON数据映射功能
 */
public class AiTaskDataMapperTest {
    
    @Test
    public void testMapFromJsonDataWithProvidedData() {
        System.out.println("=== 测试使用提供的真实数据进行映射 ===");
        
        // 您提供的真实JSON数据
        String jsonString = "{\n" +
                "  \"dialog_id\": \"24ae12d7-c47d-4725-a820-e2c61bcaac45\",\n" +
                "  \"abstract\": \"会议首先讨论了测试流程的简化与执行，包括步骤顺序和匹配显示问题，同时涉及测试日志的详细记录；其次针对系统功能问题，涉及连接匹配、高亮显示及版本同步等保护修复措施；最后明确了后续工作需聚焦流程优化与功能修复。\",\n" +
                "  \"content\": [\n" +
                "    {\n" +
                "      \"topic\": \"会议讨论测试流程和系统功能问题\",\n" +
                "      \"sub_topic_list\": [\n" +
                "        {\n" +
                "          \"sub_topic\": \"测试流程讨论\",\n" +
                "          \"points\": [\n" +
                "            \"说话人1和2讨论了测试流程的简化和执行，包括测试的步骤和顺序。\",\n" +
                "            \"说话人2提到测试流程的匹配和显示问题，以及测试的详细日志。\"\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"sub_topic\": \"系统功能问题讨论\",\n" +
                "          \"points\": [\n" +
                "            \"说话人2提到系统功能的连接和匹配问题，以及高亮位置的显示问题。\",\n" +
                "            \"说话人3讨论了系统功能的保护和修复，以及版本的保存和同步问题。\"\n" +
                "          ]\n" +
                "        }\n" +
                "      ],\n" +
                "      \"conclusions\": [],\n" +
                "      \"key_phrases\": [\"冒烟\", \"测试测试测试\", \"功能流程\", \"版本\", \"高亮位置\"]\n" +
                "    },\n" +
                "    {\n" +
                "      \"topic\": \"\",\n" +
                "      \"sub_topic_list\": [],\n" +
                "      \"conclusions\": [],\n" +
                "      \"key_phrases\": []\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        
        JSONObject jsonData = JSONObject.parseObject(jsonString);
        
        // 执行映射
        AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonData);
        
        // 验证基本字段
        assertNotNull("AiTask不应为null", aiTask);
        assertEquals("ObjectId应该正确映射", "24ae12d7-c47d-4725-a820-e2c61bcaac45", aiTask.getObjectId());
        assertEquals("NoteUid应该正确映射", "24ae12d7-c47d-4725-a820-e2c61bcaac45", aiTask.getNoteUid());
        assertEquals("Title应该有默认值", "AI会议记录", aiTask.getTitle());
        assertNotNull("Timestamp不应为null", aiTask.getTimestamp());
        
        // 验证状态字段
        assertEquals("AbstractStatus应该为2", Integer.valueOf(2), aiTask.getAbstractStatus());
        assertEquals("TodoStatus应该为2", Integer.valueOf(2), aiTask.getTodoStatus());
        assertEquals("AiStatus应该为2", Integer.valueOf(2), aiTask.getAiStatus());
        
        // 验证摘要信息
        assertNotNull("AbstractInfo不应为null", aiTask.getAbstractInfo());
        assertEquals("摘要内容应该正确", jsonData.getString("abstract"), aiTask.getAbstractInfo().getContent());
        
        // 验证详细内容
        assertNotNull("Detail不应为null", aiTask.getAbstractInfo().getDetail());
        assertEquals("应该有1个有效的详细内容", 1, aiTask.getAbstractInfo().getDetail().size());
        
        AiTabletAbstractVO.AbstractContent detail = aiTask.getAbstractInfo().getDetail().get(0);
        assertEquals("主题应该正确", "会议讨论测试流程和系统功能问题", detail.getTopic());
        
        // 验证要点
        assertNotNull("Points不应为null", detail.getPoints());
        assertEquals("应该有4个要点", 4, detail.getPoints().size());
        assertTrue("应该包含第一个要点", detail.getPoints().contains("说话人1和2讨论了测试流程的简化和执行，包括测试的步骤和顺序。"));
        
        // 验证关键词
        assertNotNull("KeyPhrases不应为null", detail.getKeyPhrases());
        assertEquals("应该有5个关键词", 5, detail.getKeyPhrases().size());
        assertTrue("应该包含'冒烟'关键词", detail.getKeyPhrases().contains("冒烟"));
        assertTrue("应该包含'测试测试测试'关键词", detail.getKeyPhrases().contains("测试测试测试"));
        assertTrue("应该包含'功能流程'关键词", detail.getKeyPhrases().contains("功能流程"));
        assertTrue("应该包含'版本'关键词", detail.getKeyPhrases().contains("版本"));
        assertTrue("应该包含'高亮位置'关键词", detail.getKeyPhrases().contains("高亮位置"));
        
        // 验证会议类型
        assertNotNull("MeetingType不应为null", aiTask.getMeetingType());
        assertEquals("会议类型应该正确", "AI会议记录", aiTask.getMeetingType().getMeetingType());
        
        System.out.println("✓ 真实数据映射测试通过");
        System.out.println("  - Dialog ID: " + aiTask.getObjectId());
        System.out.println("  - 摘要长度: " + aiTask.getAbstractInfo().getContent().length());
        System.out.println("  - 主题: " + detail.getTopic());
        System.out.println("  - 要点数量: " + detail.getPoints().size());
        System.out.println("  - 关键词: " + detail.getKeyPhrases());
    }
    
    @Test
    public void testMapFromJsonDataWithNullData() {
        System.out.println("\n=== 测试空数据处理 ===");
        
        // 测试null数据
        AiTabletAITask result = AiTaskDataMapper.mapFromJsonData(null);
        assertNull("null数据应该返回null", result);
        
        System.out.println("✓ 空数据处理测试通过");
    }
    
    @Test
    public void testCreateAiTaskMethod() {
        System.out.println("\n=== 测试便捷创建方法 ===");
        
        // 准备测试数据
        String dialogId = "test-dialog-123";
        String abstractContent = "这是测试摘要内容";
        
        JSONArray contentArray = new JSONArray();
        JSONObject contentItem = new JSONObject();
        contentItem.put("topic", "测试主题");
        contentItem.put("key_phrases", new String[]{"关键词1", "关键词2"});
        contentArray.add(contentItem);
        
        // 使用便捷方法创建
        AiTabletAITask aiTask = AiTaskDataMapper.createAiTask(dialogId, abstractContent, contentArray);
        
        // 验证结果
        assertNotNull("AiTask不应为null", aiTask);
        assertEquals("ObjectId应该正确", dialogId, aiTask.getObjectId());
        assertEquals("摘要内容应该正确", abstractContent, aiTask.getAbstractInfo().getContent());
        
        System.out.println("✓ 便捷创建方法测试通过");
        System.out.println("  - Dialog ID: " + aiTask.getObjectId());
        System.out.println("  - 摘要内容: " + aiTask.getAbstractInfo().getContent());
    }
    
    @Test
    public void testComplexDataStructure() {
        System.out.println("\n=== 测试复杂数据结构处理 ===");
        
        // 创建复杂的测试数据
        JSONObject jsonData = new JSONObject();
        jsonData.put("dialog_id", "complex-test-456");
        jsonData.put("abstract", "复杂数据结构测试摘要");
        
        JSONArray contentArray = new JSONArray();
        
        // 第一个内容项 - 包含完整数据
        JSONObject content1 = new JSONObject();
        content1.put("topic", "完整数据主题");
        
        JSONArray subTopicList1 = new JSONArray();
        JSONObject subTopic1 = new JSONObject();
        subTopic1.put("sub_topic", "子主题1");
        subTopic1.put("points", new String[]{"要点1", "要点2", "要点3"});
        subTopicList1.add(subTopic1);
        
        content1.put("sub_topic_list", subTopicList1);
        content1.put("key_phrases", new String[]{"关键词A", "关键词B"});
        content1.put("conclusions", new String[]{"结论1", "结论2"});
        contentArray.add(content1);
        
        // 第二个内容项 - 部分数据缺失
        JSONObject content2 = new JSONObject();
        content2.put("topic", "部分数据主题");
        content2.put("sub_topic_list", new JSONArray()); // 空数组
        content2.put("key_phrases", new String[]{"关键词C"});
        contentArray.add(content2);
        
        jsonData.put("content", contentArray);
        
        // 执行映射
        AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonData);
        
        // 验证结果
        assertNotNull("AiTask不应为null", aiTask);
        assertEquals("应该有2个详细内容", 2, aiTask.getAbstractInfo().getDetail().size());
        
        // 验证第一个内容项
        AiTabletAbstractVO.AbstractContent detail1 = aiTask.getAbstractInfo().getDetail().get(0);
        assertEquals("第一个主题应该正确", "完整数据主题", detail1.getTopic());
        assertEquals("第一个内容应该有3个要点", 3, detail1.getPoints().size());
        assertEquals("第一个内容应该有2个关键词", 2, detail1.getKeyPhrases().size());
        
        // 验证第二个内容项
        AiTabletAbstractVO.AbstractContent detail2 = aiTask.getAbstractInfo().getDetail().get(1);
        assertEquals("第二个主题应该正确", "部分数据主题", detail2.getTopic());
        assertEquals("第二个内容应该有1个关键词", 1, detail2.getKeyPhrases().size());
        
        System.out.println("✓ 复杂数据结构处理测试通过");
        System.out.println("  - 详细内容数量: " + aiTask.getAbstractInfo().getDetail().size());
        System.out.println("  - 第一个内容要点数: " + detail1.getPoints().size());
        System.out.println("  - 第二个内容关键词: " + detail2.getKeyPhrases());
    }
}
