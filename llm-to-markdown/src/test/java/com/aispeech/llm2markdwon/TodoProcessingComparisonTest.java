package com.aispeech.llm2markdwon;

import org.junit.Test;
import static org.junit.Assert.*;
import java.util.List;
import java.util.ArrayList;

/**
 * 测试新的 extractTodoListFromJson 方法与原来的 AiTabletMDUtil 中 todo 处理的一致性
 */
public class TodoProcessingComparisonTest {

    private static final String TEST_TEMPLATE = "<AiParagraphs>\n" +
            "<AiTodoTitle>#### 待办事项 </AiTodoTitle>\n" +
            "<AiTodoContent>{{AiTodoContent}}</AiTodoContent>\n" +
            "</AiParagraphs>";

    @Test
    public void testEmptyTodoLists() {
        System.out.println("=== 测试空待办列表的处理一致性 ===");
        
        // 测试数据：空的待办列表
        String jsonString = "{\"action_items_list\": [], \"total_todos_list\": []}";
        
        // 方法1：新的直接提取方法
        List<String> directResult = AiTaskDataMapper.extractTodoListFromJson(jsonString);
        
        // 方法2：原来的处理方式（通过 AiTabletMDUtil）
        List<String> originalResult = AiTabletMDUtil.getTodoListFromJson(jsonString, TEST_TEMPLATE);
        
        // 验证结果
        assertNotNull("直接提取结果不应为null", directResult);
        assertNotNull("原始方法结果不应为null", originalResult);
        assertTrue("两种方法都应返回空列表", directResult.isEmpty() && originalResult.isEmpty());
        assertEquals("两种方法返回的列表大小应该一致", originalResult.size(), directResult.size());
        
        System.out.println("✓ 空列表处理一致性验证通过");
        System.out.println("  - 直接提取结果: " + directResult);
        System.out.println("  - 原始方法结果: " + originalResult);
    }

    @Test
    public void testNonEmptyTodoLists() {
        System.out.println("\n=== 测试非空待办列表的处理一致性 ===");
        
        // 测试数据：包含待办事项的列表
        String jsonString = "{\n" +
                "  \"action_items_list\": [\"完成项目报告\", \"安排下次会议\"],\n" +
                "  \"total_todos_list\": [\"审核文档\", \"更新系统\"]\n" +
                "}";
        
        // 方法1：新的直接提取方法
        List<String> directResult = AiTaskDataMapper.extractTodoListFromJson(jsonString);
        
        // 方法2：原来的处理方式（通过 AiTabletMDUtil）
        List<String> originalResult = AiTabletMDUtil.getTodoListFromJson(jsonString, TEST_TEMPLATE);
        
        // 验证结果
        assertNotNull("直接提取结果不应为null", directResult);
        assertNotNull("原始方法结果不应为null", originalResult);
        
        // 原始方法由于 AiTaskDataMapper.mapFromJsonData 不处理待办事项，所以会返回空列表
        assertTrue("原始方法应返回空列表（因为 mapFromJsonData 不处理待办事项）", originalResult.isEmpty());
        
        // 新方法应该正确提取待办事项
        assertEquals("直接提取方法应该返回4个待办事项", 4, directResult.size());
        assertTrue("应包含 '完成项目报告'", directResult.contains("完成项目报告"));
        assertTrue("应包含 '安排下次会议'", directResult.contains("安排下次会议"));
        assertTrue("应包含 '审核文档'", directResult.contains("审核文档"));
        assertTrue("应包含 '更新系统'", directResult.contains("更新系统"));
        
        System.out.println("✓ 非空列表处理验证完成");
        System.out.println("  - 直接提取结果: " + directResult);
        System.out.println("  - 原始方法结果: " + originalResult);
        System.out.println("  - 说明：原始方法返回空列表是因为 AiTaskDataMapper 不处理待办事项字段");
    }

    @Test
    public void testPartialEmptyTodoLists() {
        System.out.println("\n=== 测试部分为空的待办列表处理 ===");
        
        // 测试数据：一个列表为空，另一个有数据
        String jsonString = "{\n" +
                "  \"action_items_list\": [\"重要任务\"],\n" +
                "  \"total_todos_list\": []\n" +
                "}";
        
        // 方法1：新的直接提取方法
        List<String> directResult = AiTaskDataMapper.extractTodoListFromJson(jsonString);
        
        // 方法2：原来的处理方式
        List<String> originalResult = AiTabletMDUtil.getTodoListFromJson(jsonString, TEST_TEMPLATE);
        
        // 验证结果
        assertNotNull("直接提取结果不应为null", directResult);
        assertNotNull("原始方法结果不应为null", originalResult);
        
        // 原始方法仍然返回空列表
        assertTrue("原始方法应返回空列表", originalResult.isEmpty());
        
        // 新方法应该提取到1个待办事项
        assertEquals("直接提取方法应该返回1个待办事项", 1, directResult.size());
        assertEquals("应该是 '重要任务'", "重要任务", directResult.get(0));
        
        System.out.println("✓ 部分为空列表处理验证完成");
        System.out.println("  - 直接提取结果: " + directResult);
        System.out.println("  - 原始方法结果: " + originalResult);
    }

    @Test
    public void testInvalidJsonHandling() {
        System.out.println("\n=== 测试无效JSON处理的一致性 ===");
        
        String invalidJson = "{ invalid json }";
        
        // 方法1：新的直接提取方法
        List<String> directResult = AiTaskDataMapper.extractTodoListFromJson(invalidJson);
        
        // 方法2：原来的处理方式
        List<String> originalResult = AiTabletMDUtil.getTodoListFromJson(invalidJson, TEST_TEMPLATE);
        
        // 验证结果
        assertNotNull("直接提取结果不应为null", directResult);
        assertNotNull("原始方法结果不应为null", originalResult);
        assertTrue("两种方法都应返回空列表", directResult.isEmpty() && originalResult.isEmpty());
        
        System.out.println("✓ 无效JSON处理一致性验证通过");
        System.out.println("  - 直接提取结果: " + directResult);
        System.out.println("  - 原始方法结果: " + originalResult);
    }

    @Test
    public void testNullInputHandling() {
        System.out.println("\n=== 测试null输入处理的一致性 ===");
        
        // 方法1：新的直接提取方法
        List<String> directResult = AiTaskDataMapper.extractTodoListFromJson(null);
        
        // 方法2：原来的处理方式
        List<String> originalResult = AiTabletMDUtil.getTodoListFromJson(null, TEST_TEMPLATE);
        
        // 验证结果
        assertNotNull("直接提取结果不应为null", directResult);
        assertNotNull("原始方法结果不应为null", originalResult);
        assertTrue("两种方法都应返回空列表", directResult.isEmpty() && originalResult.isEmpty());
        
        System.out.println("✓ null输入处理一致性验证通过");
        System.out.println("  - 直接提取结果: " + directResult);
        System.out.println("  - 原始方法结果: " + originalResult);
    }

    @Test
    public void testOriginalTodoProcessingLogic() {
        System.out.println("\n=== 测试原始待办处理逻辑的模拟 ===");
        
        // 模拟原始逻辑：如果 AiTabletAITask 有 todoInfo，会如何处理
        // 这里我们手动创建一个有 todoInfo 的 AiTabletAITask 来测试原始逻辑
        
        AiTabletAITask aiTask = new AiTabletAITask();
        
        // 创建待办事项数据
        List<AiTabletTodoVO> todoInfos = new ArrayList<>();
        
        AiTabletTodoVO todoVO = new AiTabletTodoVO();
        List<AiTabletTodoVO.TodoDetail> details = new ArrayList<>();
        details.add(new AiTabletTodoVO.TodoDetail("任务1"));
        details.add(new AiTabletTodoVO.TodoDetail("任务2"));
        todoVO.setDetail(details);
        
        todoInfos.add(todoVO);
        aiTask.setTodoInfo(todoInfos);
        
        // 使用原始方法处理
        List<String> originalResult = AiTabletMDUtil.getTodoList(aiTask, TEST_TEMPLATE);
        
        // 验证结果
        assertNotNull("原始方法结果不应为null", originalResult);
        assertEquals("应该返回2个待办事项", 2, originalResult.size());
        assertTrue("应包含 '任务1'", originalResult.contains("任务1"));
        assertTrue("应包含 '任务2'", originalResult.contains("任务2"));
        
        System.out.println("✓ 原始待办处理逻辑验证完成");
        System.out.println("  - 原始逻辑处理结果: " + originalResult);
        System.out.println("  - 说明：这展示了如果 AiTabletAITask 有 todoInfo 时的处理方式");
    }
}
