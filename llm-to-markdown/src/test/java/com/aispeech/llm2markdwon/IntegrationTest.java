package com.aispeech.llm2markdwon;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import static org.junit.Assert.*;
import java.util.Set;

/**
 * 集成测试类
 * 模拟真实使用场景，测试完整的工作流程
 */
public class IntegrationTest {
    
    @Test
    public void testRealWorldScenario() {
        System.out.println("=== 真实场景集成测试 ===");
        
        // 模拟从外部系统接收到的JSON数据（您提供的真实数据）
        String receivedJsonData = "{\n" +
                "  \"dialog_id\": \"24ae12d7-c47d-4725-a820-e2c61bcaac45\",\n" +
                "  \"abstract\": \"会议首先讨论了测试流程的简化与执行，包括步骤顺序和匹配显示问题，同时涉及测试日志的详细记录；其次针对系统功能问题，涉及连接匹配、高亮显示及版本同步等保护修复措施；最后明确了后续工作需聚焦流程优化与功能修复。\",\n" +
                "  \"content\": [\n" +
                "    {\n" +
                "      \"topic\": \"会议讨论测试流程和系统功能问题\",\n" +
                "      \"sub_topic_list\": [\n" +
                "        {\n" +
                "          \"sub_topic\": \"测试流程讨论\",\n" +
                "          \"points\": [\n" +
                "            \"说话人1和2讨论了测试流程的简化和执行，包括测试的步骤和顺序。\",\n" +
                "            \"说话人2提到测试流程的匹配和显示问题，以及测试的详细日志。\"\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"sub_topic\": \"系统功能问题讨论\",\n" +
                "          \"points\": [\n" +
                "            \"说话人2提到系统功能的连接和匹配问题，以及高亮位置的显示问题。\",\n" +
                "            \"说话人3讨论了系统功能的保护和修复，以及版本的保存和同步问题。\"\n" +
                "          ]\n" +
                "        }\n" +
                "      ],\n" +
                "      \"conclusions\": [],\n" +
                "      \"key_phrases\": [\"冒烟\", \"测试测试测试\", \"功能流程\", \"版本\", \"高亮位置\"]\n" +
                "    },\n" +
                "    {\n" +
                "      \"topic\": \"\",\n" +
                "      \"sub_topic_list\": [],\n" +
                "      \"conclusions\": [],\n" +
                "      \"key_phrases\": []\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        
        try {
            // 步骤1: 解析接收到的JSON数据
            System.out.println("步骤1: 解析JSON数据...");
            JSONObject jsonData = JSONObject.parseObject(receivedJsonData);
            assertNotNull("JSON解析不应失败", jsonData);
            System.out.println("✓ JSON解析成功");
            
            // 步骤2: 将JSON数据映射为AiTabletAITask对象
            System.out.println("步骤2: 数据映射...");
            AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonData);
            assertNotNull("数据映射不应失败", aiTask);
            assertEquals("Dialog ID应该正确映射", "24ae12d7-c47d-4725-a820-e2c61bcaac45", aiTask.getObjectId());
            System.out.println("✓ 数据映射成功");
            
            // 步骤3: 准备Markdown模板
            System.out.println("步骤3: 准备模板...");
            String template = createProductionTemplate();
            assertNotNull("模板不应为null", template);
            assertFalse("模板不应为空", template.trim().isEmpty());
            System.out.println("✓ 模板准备成功");
            
            // 步骤4: 生成Markdown内容
            System.out.println("步骤4: 生成Markdown...");
            JSONObject result = AiTabletMDUtil.getAiMD(aiTask, template);
            assertNotNull("Markdown生成不应失败", result);
            assertTrue("结果应包含content字段", result.containsKey("content"));
            System.out.println("✓ Markdown生成成功");
            
            // 步骤5: 验证生成的内容
            System.out.println("步骤5: 验证生成内容...");
            String markdownContent = result.getString("content");
            assertNotNull("Markdown内容不应为null", markdownContent);
            assertFalse("Markdown内容不应为空", markdownContent.trim().isEmpty());
            
            // 验证内容包含预期的元素
            assertTrue("应包含标题", markdownContent.contains("AI会议记录"));
            assertTrue("应包含摘要标题", markdownContent.contains("内容概括"));
            assertTrue("应包含主要内容标题", markdownContent.contains("主要内容"));
            assertTrue("应包含摘要内容", markdownContent.contains("测试流程的简化与执行"));
            
            System.out.println("✓ 内容验证成功");
            
            // 步骤6: 提取关键信息
            System.out.println("步骤6: 提取关键信息...");
            if (result.containsKey("key")) {
                Set<String> keywords = (Set<String>) result.get("key");
                assertNotNull("关键词不应为null", keywords);
                assertFalse("关键词不应为空", keywords.isEmpty());
                assertTrue("应包含'冒烟'关键词", keywords.contains("冒烟"));
                assertTrue("应包含'版本'关键词", keywords.contains("版本"));
                System.out.println("✓ 关键词提取成功: " + keywords);
            }
            
            if (result.containsKey("meetingType")) {
                String meetingType = result.getString("meetingType");
                assertNotNull("会议类型不应为null", meetingType);
                assertEquals("会议类型应该正确", "AI会议记录", meetingType);
                System.out.println("✓ 会议类型提取成功: " + meetingType);
            }
            
            // 步骤7: 输出最终结果统计
            System.out.println("步骤7: 结果统计...");
            System.out.println("  - 原始JSON长度: " + receivedJsonData.length());
            System.out.println("  - 生成Markdown长度: " + markdownContent.length());
            System.out.println("  - 包含字段数: " + result.size());
            System.out.println("  - 处理耗时: < 1秒");
            
            System.out.println("\n🎉 真实场景集成测试完全通过!");
            
            // 输出部分生成的Markdown内容作为示例
            System.out.println("\n--- 生成的Markdown内容示例 ---");
            String preview = markdownContent.length() > 500 ? 
                markdownContent.substring(0, 500) + "\n...(内容已截断)" : 
                markdownContent;
            System.out.println(preview);
            System.out.println("--- 示例结束 ---");
            
        } catch (Exception e) {
            fail("集成测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    @Test
    public void testPerformance() {
        System.out.println("\n=== 性能测试 ===");
        
        // 准备测试数据
        String jsonString = "{\n" +
                "  \"dialog_id\": \"perf-test-789\",\n" +
                "  \"abstract\": \"性能测试摘要内容，用于测试大量数据处理的性能表现。\",\n" +
                "  \"content\": [\n" +
                "    {\n" +
                "      \"topic\": \"性能测试主题\",\n" +
                "      \"sub_topic_list\": [\n" +
                "        {\n" +
                "          \"sub_topic\": \"性能子主题\",\n" +
                "          \"points\": [\"性能要点1\", \"性能要点2\", \"性能要点3\"]\n" +
                "        }\n" +
                "      ],\n" +
                "      \"key_phrases\": [\"性能\", \"测试\", \"优化\"]\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        
        JSONObject jsonData = JSONObject.parseObject(jsonString);
        String template = createProductionTemplate();
        
        // 执行多次测试
        int testCount = 100;
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < testCount; i++) {
            AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonData);
            JSONObject result = AiTabletMDUtil.getAiMD(aiTask, template);
            assertNotNull("第" + (i+1) + "次测试结果不应为null", result);
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        double avgTime = (double) totalTime / testCount;
        
        System.out.println("✓ 性能测试完成");
        System.out.println("  - 测试次数: " + testCount);
        System.out.println("  - 总耗时: " + totalTime + "ms");
        System.out.println("  - 平均耗时: " + String.format("%.2f", avgTime) + "ms");
        System.out.println("  - 每秒处理: " + String.format("%.0f", 1000.0 / avgTime) + "次");
        
        // 性能断言（平均处理时间应该小于100ms）
        assertTrue("平均处理时间应该小于100ms，实际: " + avgTime + "ms", avgTime < 100);
    }
    
    /**
     * 创建生产环境使用的模板
     */
    private String createProductionTemplate() {
        return "<AiParagraphs>\n" +
                "<AiCommon> <center> </AiCommon>\n" +
                "<AiTitle>## {{AiTitle}} </AiTitle>\n" +
                "<AiTimestamp> {{AiTimestamp}} </AiTimestamp>\n" +
                "<AiCommon> </center> </AiCommon>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiAbstractTitle>#### {{AiAbstractTitle}} </AiAbstractTitle>\n" +
                "<AiCommon> <rounded> </AiCommon>\n" +
                "<AiAbstract> {{AiAbstract}} </AiAbstract>\n" +
                "<AiCommon> </rounded> </AiCommon>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiKeyPhrase> {{AiKeyPhrase}}</AiKeyPhrase>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiTopicTitle>#### {{AiTopicTitle}} </AiTopicTitle>\n" +
                "<AiCommon> <details> </AiCommon>\n" +
                "<AiCommon> <rounded> </AiCommon>\n" +
                "<AiTopic>{{AiTopic}}</AiTopic>\n" +
                "<AiPoint>{{AiPoint}}</AiPoint>\n" +
                "<AiCommon> </rounded> </AiCommon>\n" +
                "<AiCommon> </details> </AiCommon>\n" +
                "</AiParagraphs>";
    }
}
