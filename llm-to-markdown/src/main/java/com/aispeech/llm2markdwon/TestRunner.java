package com.aispeech.llm2markdwon;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * 测试运行器
 * 用于验证模块功能是否正常
 */
public class TestRunner {
    
    public static void main(String[] args) {
        System.out.println("=== LLM to Markdown 模块测试 ===\n");
        
        try {
            // 测试1: 基本对象创建
            testBasicObjectCreation();
            
            // 测试2: JSON数据映射
            testJsonDataMapping();
            
            // 测试3: Markdown生成（简化版，不依赖Android环境）
            testMarkdownGeneration();
            
            System.out.println("✅ 所有测试通过！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 测试基本对象创建
     */
    private static void testBasicObjectCreation() {
        System.out.println("1. 测试基本对象创建...");
        
        // 创建AiTabletAITask对象
        AiTabletAITask aiTask = new AiTabletAITask();
        aiTask.setTitle("测试会议");
        aiTask.setObjectId("test-id-123");
        aiTask.setTimestamp(System.currentTimeMillis());
        
        // 创建摘要信息
        AiTabletAbstractVO abstractVO = new AiTabletAbstractVO();
        abstractVO.setContent("这是一个测试摘要");
        aiTask.setAbstractInfo(abstractVO);
        
        // 验证
        assert aiTask.getTitle().equals("测试会议");
        assert aiTask.getObjectId().equals("test-id-123");
        assert aiTask.getAbstractInfo().getContent().equals("这是一个测试摘要");
        
        System.out.println("   ✓ 基本对象创建成功");
    }
    
    /**
     * 测试JSON数据映射
     */
    private static void testJsonDataMapping() {
        System.out.println("2. 测试JSON数据映射...");
        
        // 创建测试JSON数据
        JSONObject jsonData = new JSONObject();
        jsonData.put("dialog_id", "test-dialog-123");
        jsonData.put("abstract", "测试会议摘要内容");
        
        // 创建内容数组
        JSONArray contentArray = new JSONArray();
        JSONObject contentItem = new JSONObject();
        contentItem.put("topic", "测试主题");
        
        JSONArray subTopicList = new JSONArray();
        JSONObject subTopic = new JSONObject();
        subTopic.put("sub_topic", "测试子主题");
        
        JSONArray points = new JSONArray();
        points.add("测试要点1");
        points.add("测试要点2");
        subTopic.put("points", points);
        
        subTopicList.add(subTopic);
        contentItem.put("sub_topic_list", subTopicList);
        
        JSONArray keyPhrases = new JSONArray();
        keyPhrases.add("关键词1");
        keyPhrases.add("关键词2");
        contentItem.put("key_phrases", keyPhrases);
        
        contentArray.add(contentItem);
        jsonData.put("content", contentArray);
        
        // 执行映射
        AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonData);
        
        // 验证结果
        assert aiTask != null;
        assert aiTask.getObjectId().equals("test-dialog-123");
        assert aiTask.getAbstractInfo().getContent().equals("测试会议摘要内容");
        assert aiTask.getAbstractInfo().getDetail().size() > 0;
        assert aiTask.getAbstractInfo().getDetail().get(0).getTopic().equals("测试主题");
        
        System.out.println("   ✓ JSON数据映射成功");
    }
    
    /**
     * 测试Markdown生成（简化版）
     */
    private static void testMarkdownGeneration() {
        System.out.println("3. 测试Markdown生成...");
        
        // 创建测试任务
        AiTabletAITask aiTask = new AiTabletAITask();
        aiTask.setTitle("测试会议");
        aiTask.setTimestamp(System.currentTimeMillis());
        
        // 创建摘要信息
        AiTabletAbstractVO abstractVO = new AiTabletAbstractVO();
        abstractVO.setContent("这是测试摘要内容");
        aiTask.setAbstractInfo(abstractVO);
        
        // 创建会议类型
        AiTabletMeetingTypeVO meetingType = new AiTabletMeetingTypeVO("测试会议类型");
        aiTask.setMeetingType(meetingType);
        
        // 简单模板
        String template = "<AiParagraphs>\n" +
                "<AiTitle>## {{AiTitle}} </AiTitle>\n" +
                "<AiAbstract> {{AiAbstract}} </AiAbstract>\n" +
                "</AiParagraphs>";
        
        try {
            // 生成Markdown（这里可能会因为Android依赖而失败，但我们可以测试基本逻辑）
            JSONObject result = AiTabletMDUtil.getAiMD(aiTask, template);
            
            // 验证结果
            assert result != null;
            assert result.containsKey("content");
            assert result.containsKey("meetingType");
            assert result.getString("meetingType").equals("测试会议类型");
            
            System.out.println("   ✓ Markdown生成成功");
            System.out.println("   生成的内容长度: " + result.getString("content").length());
            
        } catch (Exception e) {
            // 如果因为其他依赖失败
            System.out.println("   ⚠ Markdown生成测试异常: " + e.getMessage());
        }
    }
}
