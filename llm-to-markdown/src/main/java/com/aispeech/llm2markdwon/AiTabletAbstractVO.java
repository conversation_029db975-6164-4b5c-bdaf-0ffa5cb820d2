package com.aispeech.llm2markdwon;

import java.util.List;

/**
 * AI平板摘要信息VO类
 * 用于处理会议内容的摘要和详细信息
 */
public class AiTabletAbstractVO {
    
    private String content;
    private List<AbstractContent> detail;
    
    // 构造函数
    public AiTabletAbstractVO() {}
    
    public AiTabletAbstractVO(String content, List<AbstractContent> detail) {
        this.content = content;
        this.detail = detail;
    }
    
    // Getter和Setter方法
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public List<AbstractContent> getDetail() {
        return detail;
    }
    
    public void setDetail(List<AbstractContent> detail) {
        this.detail = detail;
    }
    
    /**
     * 摘要内容详情类
     */
    public static class AbstractContent {
        private String topic;
        private List<String> points;
        private List<String> keyPhrases;
        private List<String> conclusions;
        private Integer startParagraph;
        private Integer endParagraph;
        
        // 构造函数
        public AbstractContent() {}
        
        public AbstractContent(String topic, List<String> points, List<String> keyPhrases, 
                             List<String> conclusions, Integer startParagraph, Integer endParagraph) {
            this.topic = topic;
            this.points = points;
            this.keyPhrases = keyPhrases;
            this.conclusions = conclusions;
            this.startParagraph = startParagraph;
            this.endParagraph = endParagraph;
        }
        
        // Getter和Setter方法
        public String getTopic() {
            return topic;
        }
        
        public void setTopic(String topic) {
            this.topic = topic;
        }
        
        public List<String> getPoints() {
            return points;
        }
        
        public void setPoints(List<String> points) {
            this.points = points;
        }
        
        public List<String> getKeyPhrases() {
            return keyPhrases;
        }
        
        public void setKeyPhrases(List<String> keyPhrases) {
            this.keyPhrases = keyPhrases;
        }
        
        public List<String> getConclusions() {
            return conclusions;
        }
        
        public void setConclusions(List<String> conclusions) {
            this.conclusions = conclusions;
        }
        
        public Integer getStartParagraph() {
            return startParagraph;
        }
        
        public void setStartParagraph(Integer startParagraph) {
            this.startParagraph = startParagraph;
        }
        
        public Integer getEndParagraph() {
            return endParagraph;
        }
        
        public void setEndParagraph(Integer endParagraph) {
            this.endParagraph = endParagraph;
        }
    }
}
