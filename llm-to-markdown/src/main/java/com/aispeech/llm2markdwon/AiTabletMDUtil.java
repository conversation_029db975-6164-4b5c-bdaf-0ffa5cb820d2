package com.aispeech.llm2markdwon;


import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import android.util.Log;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class AiTabletMDUtil {

    private static final String TAG = "AiTabletMDUtil";
    private static final Pattern AI_LABEL_PATTERN = Pattern.compile("<Ai(\\w+)>.*?</Ai\\1>");
    private static final String IGNORE_SPEAKER_SUMMARY = "无有效内容";

    public static JSONObject getAiMD(AiTabletAITask aiTask, String template) {
        JSONObject resultObject = new JSONObject();
        String[] cutTemplate = cutWithAiParagraphs(template);
        StringBuilder resultBuilder = new StringBuilder();
        for (String templateLines : cutTemplate) {
            String[] lines = templateLines.split("\n");
            List<String> titleList = AiTabletMDUtil.getTemplateTitleList(templateLines);
            MarkdownUtil.SectionBuilder bd = MarkdownUtil.of();

            int comIndex = 0;
            for (String title : titleList) {
                AiMDLabelEnum label = AiMDLabelEnum.getLabel(title);
                if (label == null) {
                    continue;
                }
                if (Objects.isNull(bd)) {
                    break;
                }
                switch (label) {
                    case TITLE:
                        String titleStr = Optional.ofNullable(aiTask).map(AiTabletAITask::getTitle).orElse(null);
                        if (StringUtils.isNotBlank(titleStr)) {
                            appendSimpleText(bd, label, titleStr,template);
                            bd.br();
                        }
                        break;
                    case TIMESTAMP:
                        Long timestamp = Optional.ofNullable(aiTask).map(AiTabletAITask::getTimestamp).orElse(null);
                        if (Objects.nonNull(timestamp)) {
                            LocalDateTime dateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneId.systemDefault());
                            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                            appendSimpleText(bd, label, dateTime.format(formatter), template);
                            bd.br();
                        }
                        break;
                    case FOCUS_ON_TITLE:
                        appendSimpleText(bd, label, "重点关注", template);
                        bd.br();
                        break;
                    case FOCUS_ON_SUB_TITLE:
                        FocusOnVo focusOnVo = Optional.ofNullable(aiTask).map(AiTabletAITask::getFocusOn).orElse(null);
                        if (Objects.nonNull(focusOnVo) && CollectionUtils.isNotEmpty(focusOnVo.getFocusOnList())) {
                            processFocusOn(bd, focusOnVo, template);
                            bd.br();
                        } else {
                            bd = null;
                        }
                        break;
                    case ABSTRACT_TITLE:
                        appendSimpleText(bd, label, "内容概括", template);
                        bd.br();
                        break;
                    case ABSTRACT:
                        String contentStr = Optional.ofNullable(aiTask).map(AiTabletAITask::getAbstractInfo).map(AiTabletAbstractVO::getContent).orElse(null);
                        if (StringUtils.isNotBlank(contentStr)) {
                            appendSimpleText(bd, label, aiTask.getAbstractInfo().getContent(), template);
                            bd.br();
                        }else {
                            bd = null;
                        }

                        break;
                    case TOPIC_TITLE:
                        appendSimpleText(bd, label, "主要内容", template);
                        bd.br();
                        break;
                    case TOPIC:
                        List<AiTabletAbstractVO.AbstractContent> details = Optional.ofNullable(aiTask).map(AiTabletAITask::getAbstractInfo).map(AiTabletAbstractVO::getDetail).orElse(null);
                        if (CollectionUtils.isNotEmpty(details)) {
                            processTopic(bd, details, template);
                            bd.br();
                        }else {
                            bd = null;
                        }

                        break;
                    case KEY_PHRASE_TITLE:
                        //客户端自行处理，不需要
                        break;
                    case KEY_PHRASE:
                        List<AiTabletAbstractVO.AbstractContent> keyPhrases = Optional.ofNullable(aiTask).map(AiTabletAITask::getAbstractInfo).map(AiTabletAbstractVO::getDetail).orElse(null);
                        if (Objects.nonNull(keyPhrases)) {
                            Set<String> phraseList = new HashSet<>();
                            keyPhrases.stream()
                                    .flatMap(detail -> Optional.ofNullable(detail.getKeyPhrases())
                                            .orElseGet(Collections::emptyList)
                                            .stream())
                                    .forEach(phraseList::add);
                            resultObject.put("key", phraseList);
                        }
                        break;
                    case CONCLUSIONS_TOPIC:
                        List<AiTabletAbstractVO.AbstractContent> conclusions = Optional.ofNullable(aiTask).map(AiTabletAITask::getAbstractInfo).map(AiTabletAbstractVO::getDetail).orElse(null);
                        if (CollectionUtils.isNotEmpty(conclusions)) {
                            processConclusions(bd, conclusions, template);
                            bd.br();
                        }else {
                            bd = null;
                        }
                        break;
                    case TODO_TITLE:
                        break;
                    case TODO_CONTENT:
                        List<AiTabletTodoVO> todoInfos = Optional.ofNullable(aiTask).map(AiTabletAITask::getTodoInfo).orElse(null);
                        if (Objects.nonNull(todoInfos)) {
                            List<String> todoList = new ArrayList<>();
                            aiTask.getTodoInfo().forEach(
                                    todoInfo -> {
                                        todoInfo.getDetail().forEach(
                                                detail -> todoList.add(detail.getTodo())
                                        );
                                    }
                            );
                            resultObject.put("todo", todoList);
                        }
                        break;
                    case QA_TITLE:
                        appendSimpleText(bd, label, "问答回顾", template);
                        bd.br();
                        break;
                    case QA_QUESTION:
                        List<LLMQSResp.QAPair>  pairs = Optional.ofNullable(aiTask).map(AiTabletAITask::getQaInfo).map(LLMQSResp.QSInfo::getQaRecord).orElse(null);
                        if (CollectionUtils.isNotEmpty(pairs)) {
                            processQaContent(bd, aiTask.getQaInfo().getQaRecord(), template);
                            bd.br();
                        }else {
                            bd = null;
                        }
                        break;
                    case COMMON:
                        comIndex = processCommon(bd, lines, comIndex);
                        //客户端特殊处理，特殊标签需要添加两个 br
                        bd.br();
                        bd.br();
                        break;
                    case SPEAKER_SUMMARY_TITLE:
                        appendSimpleText(bd, label, "发言总结", template);
                        bd.br();
                        break;
                    case SPEAKER_NAME:
                        JSONObject speakerSummary = Optional.ofNullable(aiTask).map(AiTabletAITask::getSpeakerSummary).orElse(new JSONObject());
                        processSpeakerSummary(bd, speakerSummary, template);
                        bd.br();
                        break;
                    default:
                        break;
                }
            }
            if (Objects.nonNull(bd)) {
                String res =  bd.build().replace("<br>", "\n");
                resultBuilder.append(res);
            }

        }
        resultObject.put("content", resultBuilder.toString());
        if(aiTask!=null && aiTask.getMeetingType()!=null ) {
            resultObject.put("meetingType", aiTask.getMeetingType().getMeetingType());
        }
        return resultObject;
    }

    // ========== 便于外部使用的辅助方法（无需依赖 fastjson）==========

    /**
     * 获取生成的 Markdown 内容（字符串格式）
     * 外部模块可以直接调用此方法获取 Markdown 内容，无需依赖 fastjson
     *
     * @param aiTask AI任务对象
     * @param template 模板字符串
     * @return Markdown 内容字符串，如果生成失败返回空字符串
     */
    public static String getMarkdownContent(AiTabletAITask aiTask, String template) {
        try {
            JSONObject result = getAiMD(aiTask, template);
            return result.getString("content");
        } catch (Exception e) {
            Log.e(TAG, "获取 Markdown 内容失败", e);
            return "";
        }
    }

    /**
     * 获取提取的关键词列表
     *
     * @param aiTask AI任务对象
     * @param template 模板字符串
     * @return 关键词列表，如果没有关键词返回空列表
     */
    @SuppressWarnings("unchecked")
    public static List<String> getKeyPhrases(AiTabletAITask aiTask, String template) {
        try {
            JSONObject result = getAiMD(aiTask, template);
            Object keyObj = result.get("key");
            if (keyObj instanceof Set) {
                return new ArrayList<>((Set<String>) keyObj);
            }
            return new ArrayList<>();
        } catch (Exception e) {
            Log.e(TAG, "获取关键词失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取待办事项列表
     *
     * @param aiTask AI任务对象
     * @param template 模板字符串
     * @return 待办事项列表，如果没有待办事项返回空列表
     */
    @SuppressWarnings("unchecked")
    public static List<String> getTodoList(AiTabletAITask aiTask, String template) {
        try {
            JSONObject result = getAiMD(aiTask, template);
            Object todoObj = result.get("todo");
            if (todoObj instanceof List) {
                return (List<String>) todoObj;
            }
            return new ArrayList<>();
        } catch (Exception e) {
            Log.e(TAG, "获取待办事项失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取会议类型
     *
     * @param aiTask AI任务对象
     * @param template 模板字符串
     * @return 会议类型字符串，如果没有会议类型返回空字符串
     */
    public static String getMeetingType(AiTabletAITask aiTask, String template) {
        try {
            JSONObject result = getAiMD(aiTask, template);
            return result.getString("meetingType");
        } catch (Exception e) {
            Log.e(TAG, "获取会议类型失败", e);
            return "";
        }
    }

    /**
     * 获取完整的处理结果（简化的数据结构）
     * 返回一个包含所有信息的简单对象，外部模块无需依赖 fastjson
     */
    public static class AiMDResult {
        private String content;
        private List<String> keyPhrases;
        private List<String> todoList;
        private String meetingType;

        public AiMDResult(String content, List<String> keyPhrases, List<String> todoList, String meetingType) {
            this.content = content != null ? content : "";
            this.keyPhrases = keyPhrases != null ? keyPhrases : new ArrayList<>();
            this.todoList = todoList != null ? todoList : new ArrayList<>();
            this.meetingType = meetingType != null ? meetingType : "";
        }

        public String getContent() { return content; }
        public List<String> getKeyPhrases() { return keyPhrases; }
        public List<String> getTodoList() { return todoList; }
        public String getMeetingType() { return meetingType; }

        @Override
        public String toString() {
            return "AiMDResult{" +
                    "content='" + content + '\'' +
                    ", keyPhrases=" + keyPhrases +
                    ", todoList=" + todoList +
                    ", meetingType='" + meetingType + '\'' +
                    '}';
        }
    }

    /**
     * 获取完整的处理结果（推荐使用）
     * 外部模块调用此方法可以获取所有信息，无需依赖 fastjson
     *
     * @param aiTask AI任务对象
     * @param template 模板字符串
     * @return AiMDResult 对象，包含所有处理结果
     */
    public static AiMDResult getAiMDResult(AiTabletAITask aiTask, String template) {
        try {
            JSONObject result = getAiMD(aiTask, template);

            String content = result.getString("content");

            List<String> keyPhrases = new ArrayList<>();
            Object keyObj = result.get("key");
            if (keyObj instanceof Set) {
                keyPhrases.addAll((Set<String>) keyObj);
            }

            List<String> todoList = new ArrayList<>();
            Object todoObj = result.get("todo");
            if (todoObj instanceof List) {
                todoList.addAll((List<String>) todoObj);
            }

            String meetingType = result.getString("meetingType");

            return new AiMDResult(content, keyPhrases, todoList, meetingType);
        } catch (Exception e) {
            Log.e(TAG, "获取 AI Markdown 结果失败", e);
            return new AiMDResult("", new ArrayList<>(), new ArrayList<>(), "");
        }
    }

    // ========== 直接处理 JSON 字符串的便捷方法（推荐外部模块使用）==========

    /**
     * 直接从 JSON 字符串生成 Markdown 内容
     * 外部模块的一站式解决方案，无需依赖 fastjson
     *
     * @param jsonString 包含 AI 任务数据的 JSON 字符串
     * @param template 模板字符串
     * @return Markdown 内容字符串，如果处理失败返回空字符串
     */
    public static String getMarkdownContentFromJson(String jsonString, String template) {
        try {
            AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonString);
            if (aiTask == null) {
                Log.e(TAG, "JSON 数据解析失败");
                return "";
            }
            return getMarkdownContent(aiTask, template);
        } catch (Exception e) {
            Log.e(TAG, "从 JSON 字符串生成 Markdown 内容失败", e);
            return "";
        }
    }

    /**
     * 直接从 JSON 字符串获取完整处理结果
     * 外部模块的一站式解决方案，无需依赖 fastjson
     *
     * @param jsonString 包含 AI 任务数据的 JSON 字符串
     * @param template 模板字符串
     * @return AiMDResult 对象，包含所有处理结果
     */
    public static AiMDResult getAiMDResultFromJson(String jsonString, String template) {
        try {
            AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonString);
            if (aiTask == null) {
                Log.e(TAG, "JSON 数据解析失败");
                return new AiMDResult("", new ArrayList<>(), new ArrayList<>(), "");
            }
            return getAiMDResult(aiTask, template);
        } catch (Exception e) {
            Log.e(TAG, "从 JSON 字符串获取 AI Markdown 结果失败", e);
            return new AiMDResult("", new ArrayList<>(), new ArrayList<>(), "");
        }
    }

    /**
     * 直接从 JSON 字符串获取关键词列表
     *
     * @param jsonString 包含 AI 任务数据的 JSON 字符串
     * @param template 模板字符串
     * @return 关键词列表，如果处理失败返回空列表
     */
    public static List<String> getKeyPhrasesFromJson(String jsonString, String template) {
        try {
            AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonString);
            if (aiTask == null) {
                return new ArrayList<>();
            }
            return getKeyPhrases(aiTask, template);
        } catch (Exception e) {
            Log.e(TAG, "从 JSON 字符串获取关键词失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 直接从 JSON 字符串获取待办事项列表
     *
     * @param jsonString 包含 AI 任务数据的 JSON 字符串
     * @param template 模板字符串
     * @return 待办事项列表，如果处理失败返回空列表
     */
    public static List<String> getTodoListFromJson(String jsonString, String template) {
        try {
            AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonString);
            if (aiTask == null) {
                return new ArrayList<>();
            }
            return getTodoList(aiTask, template);
        } catch (Exception e) {
            Log.e(TAG, "从 JSON 字符串获取待办事项失败", e);
            return new ArrayList<>();
        }
    }

    private static void appendSimpleText(MarkdownUtil.SectionBuilder bd, AiMDLabelEnum label, String content, String template) {
        bd.text(AiTabletMDUtil.getFinalMDString(label, content, template));
    }

    private static void processFocusOn(MarkdownUtil.SectionBuilder bd, FocusOnVo focusOn, String template) {
        for (FocusOnVo.FocusOnList item : focusOn.getFocusOnList()) {
            String subTitleFormat = AiTabletMDUtil.getMDTemplate(AiMDLabelEnum.FOCUS_ON_SUB_TITLE, template);
            bd.text(subTitleFormat.replace(String.format("{{%s}}", AiMDLabelEnum.FOCUS_ON_SUB_TITLE.getLabel()), item.getSubTitle()));

            String contentFormat = AiTabletMDUtil.getMDTemplate(AiMDLabelEnum.FOCUS_ON_CONTENT, template);
            for (String content : item.getContentList()) {
                bd.text(contentFormat.replace(String.format("{{%s}}", AiMDLabelEnum.FOCUS_ON_CONTENT.getLabel()), content));
            }
        }
    }

    private static void processTopic(MarkdownUtil.SectionBuilder bd, List<AiTabletAbstractVO.AbstractContent> details, String template) {
        for (int i = 0; i < details.size(); i++) {
            AiTabletAbstractVO.AbstractContent detail = details.get(i);
            String topicFormat = AiTabletMDUtil.getMDTemplate(AiMDLabelEnum.TOPIC, template);
            bd.text((i + 1) + ". " + topicFormat.replace(String.format("{{%s}}", AiMDLabelEnum.TOPIC.getLabel()), detail.getTopic()));

            String pointFormat = AiTabletMDUtil.getMDTemplate(AiMDLabelEnum.TOPIC_POINT, template);
            for (int j = 0; j < detail.getPoints().size(); j++) {
                bd.text(("    " + (j + 1) + ". " + pointFormat.replace(String.format("{{%s}}", AiMDLabelEnum.TOPIC_POINT.getLabel()), detail.getPoints().get(j))));
            }
        }
    }

    private static void processConclusions(MarkdownUtil.SectionBuilder bd, List<AiTabletAbstractVO.AbstractContent> details, String template) {
        for (int i = 0; i < details.size(); i++) {
            AiTabletAbstractVO.AbstractContent detail = details.get(i);
            String topicFormat = AiTabletMDUtil.getMDTemplate(AiMDLabelEnum.CONCLUSIONS_TOPIC, template);
            bd.text((i + 1) + ". " + topicFormat.replace(String.format("{{%s}}", AiMDLabelEnum.CONCLUSIONS_TOPIC.getLabel()), detail.getTopic()));

            String conclusionsFormat = AiTabletMDUtil.getMDTemplate(AiMDLabelEnum.CONCLUSIONS, template);
            for (int j = 0; j < detail.getConclusions().size(); j++) {
                bd.text(("    " + (j + 1) + ". " + conclusionsFormat.replace(String.format("{{%s}}", AiMDLabelEnum.CONCLUSIONS.getLabel()), detail.getConclusions().get(j))));
            }
        }
    }

    private static void processQaContent(MarkdownUtil.SectionBuilder bd, List<LLMQSResp.QAPair> qaRecords, String template) {
        for (LLMQSResp.QAPair record : qaRecords) {
            bd.text(AiTabletMDUtil.getFinalMDString(AiMDLabelEnum.QA_QUESTION, record.getQuestion(), template));
            bd.text(AiTabletMDUtil.getFinalMDString(AiMDLabelEnum.QA_ANSWER, record.getAnswer(), template));
        }
    }

    private static void processSpeakerSummary(MarkdownUtil.SectionBuilder bd, JSONObject speakerSummary, String template) {
        String speakerNameTemplate = AiTabletMDUtil.getMDTemplate(AiMDLabelEnum.SPEAKER_NAME, template);
        String speakerSummaryTemplate = AiTabletMDUtil.getMDTemplate(AiMDLabelEnum.SPEAKER_SUMMARY, template);
        String speakerNameFormat = String.format("{{%s}}", AiMDLabelEnum.SPEAKER_NAME.getLabel());
        String speakerSummaryFormat = String.format("{{%s}}", AiMDLabelEnum.SPEAKER_SUMMARY.getLabel());

        AtomicBoolean empty = new AtomicBoolean(true);
        speakerSummary.entrySet().stream()
                .filter(entry -> {
                    String content = (String) entry.getValue();
                    if (StringUtils.isBlank(content) || StringUtils.equals(content, IGNORE_SPEAKER_SUMMARY)) {
                        Log.i(TAG, "跳过处理无效说话人总结内容 " + entry.getKey() + " " + content);
                        return false;
                    }
                    return true;
                })
                .sorted((e1, e2) -> {
                    String c1 = (String)e1.getValue();
                    String c2 = (String)e2.getValue();
                    int compareRes = Integer.compare(c2.length(), c1.length());
                    if (compareRes != 0) {
                        return compareRes;
                    }
                    return e1.getKey().compareTo(e2.getKey());
                })
                .forEachOrdered(entry -> {
                    String content = (String) entry.getValue();
                    bd.text(speakerNameTemplate.replace(speakerNameFormat, entry.getKey()) + speakerSummaryTemplate.replace(speakerSummaryFormat, content));
                    empty.set(false);
                });
        // 内容为空时，需要展示默认文案
        if (empty.get()) {
            bd.text("AI未获取到有效内容");
        }
    }

    private static int processCommon(MarkdownUtil.SectionBuilder bd, String[] lines, int comIndex) {
        for (int i = comIndex; i < lines.length; i++) {
            if (lines[i].contains("<AiCommon>")) {
                String s = AiTabletMDUtil.getMDTemplate(AiMDLabelEnum.COMMON, lines[i]);
                bd.text(s);
                return i + 1;
            }
        }
        return comIndex;
    }

    public static String getFinalMDString(AiMDLabelEnum labelEnum, String contentStr, String template) {
        String formatStr = String.format("{{%s}}", labelEnum.getLabel());
        return AiTabletMDUtil.getMDTemplate(labelEnum, template).replace(formatStr, contentStr);

    }
    private static String getMDTemplate(AiMDLabelEnum label, String content) {
        String patternStr = String.format("<%s>(.*?)</%s>", label.getLabel(), label.getLabel());
        Pattern pattern = Pattern.compile(patternStr, Pattern.DOTALL);
        // 创建 Matcher 对象
        Matcher matcher = pattern.matcher(content);
        // 如果找到匹配项
        if (matcher.find()) {
            // 提取并打印匹配的内容
            return matcher.group(1).trim();
        } else {
            System.out.println("No match found.");
        }
        return "";
    }

    private static String[] cutWithAiParagraphs(String template) {
        // 正则表达式匹配 <AiCheck> 标签及其内容
        String regex = "<AiParagraphs>(.*?)</AiParagraphs>";
        Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(template);

        // 存储匹配到的内容块
        List<String> aiCheckBlocks = new ArrayList<>();

        // 遍历匹配结果并存入列表
        while (matcher.find()) {
            String aiCheckBlock = matcher.group(1).trim();
            aiCheckBlocks.add(aiCheckBlock);
        }

        // 将 List 转换为数组
        String[] aiCheckArray = aiCheckBlocks.toArray(new String[0]);
        return aiCheckArray;
    }

    private static List<String> getTemplateTitleList(String template) {
        // 创建 Matcher 对象
        Matcher matcher = AI_LABEL_PATTERN.matcher(template);
        // 创建一个列表来存储匹配的标签名
        List<String> tagNames = new ArrayList<>();
        // 查找并添加所有匹配的标签名到列表中
        while (matcher.find()) {
            tagNames.add("Ai" + matcher.group(1));
        }
        return tagNames;
    }

    public static void main(String[] args) {
        String temp = "{\n" +
                "  \"_id\": {\n" +
                "    \"$oid\": \"66f0df4e8c1845e8732ffb80\"\n" +
                "  },\n" +
                "  \"_class\": \"com.aispeech.aiwork.business.Public.entity.AiTabletAITask\",\n" +
                "  \"noteUid\": \"uid231\",\n" +
                "  \"objectId\": \"uid231\",\n" +
                "  \"sn\": \"\",\n" +
                "  \"paragraph\": [\n" +
                "    {\n" +
                "      \"paragraphId\": \"1\",\n" +
                "      \"startTimeStr\": \"00:00:00\",\n" +
                "      \"content\": \"南京因为也没有确定时间，我们先按南京的通关和厦门的都要去做，前提来来排的话，美霞和雪儿和米燕你们3个人负责一个班就一个班就行\",\n" +
                "      \"speaker\": 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"paragraphId\": \"2\",\n" +
                "      \"startTimeStr\": \"00:00:30\",\n" +
                "      \"content\": \"负责二班，2班它是明天到周日中午展开，所以你们可以各负责半天的。\",\n" +
                "      \"speaker\": 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"paragraphId\": \"3\",\n" +
                "      \"startTimeStr\": \"00:01:00\",\n" +
                "      \"content\": \"接下来你就比如说你明天上午那个半天，那五个人，因为现在15个人没出来，他可能也不是那么集中的\",\n" +
                "      \"speaker\": 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"paragraphId\": \"4\",\n" +
                "      \"startTimeStr\": \"00:01:30\",\n" +
                "      \"content\": \"这五个人我是按半天来，他也可能说我整个上午我就只贡献出来了一个名额以一个一个种子选手也有可能，\",\n" +
                "      \"speaker\": 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"paragraphId\": \"5\",\n" +
                "      \"startTimeStr\": \"00:02:00\",\n" +
                "      \"content\": \"所以他就不太好排，你们3个就自行来讨论一下。\",\n" +
                "      \"speaker\": 2\n" +
                "    },\n" +
                "    {\n" +
                "      \"paragraphId\": \"test-EngLish\",\n" +
                "      \"startTimeStr\": \"00:03:00\",\n" +
                "      \"content\": \"Tomorrow, I will study English with Xiao Ming.\",\n" +
                "      \"speaker\": 0,\n" +
                "      \"language\": \"en\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"speakerMap\": {\n" +
                "    \"0\": \"我\",\n" +
                "    \"1\": \"说话人1\",\n" +
                "    \"2\": \"说话人2\"\n" +
                "  },\n" +
                "  \"ocrText\": \"今天下午需要测试一下手写文本的待办\",\n" +
                "  \"abstractStatus\": 2,\n" +
                "  \"todoStatus\": 2,\n" +
                "  \"realtimeNoteStatus\": 0,\n" +
                "  \"realtimeNoteBatchStatus\": 0,\n" +
                "  \"aiStatus\": 2,\n" +
                "  \"meetingType\": {\n" +
                "    \"_class\": \"com.aispeech.aiwork.business.Public.entity.vo.AiTabletMeetingTypeVO\",\n" +
                "    \"meetingType\": \"企业会议记录\"\n" +
                "  },\n" +
                "  \"abstractInfo\": {\n" +
                "    \"content\": \"会议首先讨论了工作分配和时间安排，明确了美霞、雪儿和米燕负责的任务和时间范围，同时指出了人员分配的困难并建议自行解决。然后，会议提出了今天下午需要进行手写文本测试的待办事项。\",\n" +
                "    \"detail\": [\n" +
                "      {\n" +
                "        \"topic\": \"会议讨论工作分配和时间安排\",\n" +
                "        \"points\": [\n" +
                "          \"说话人1指示美霞、雪儿和米燕负责一个班的工作，时间为明天上午到周日中午。\",\n" +
                "          \"说话人2指出人员分配存在困难，建议三人自行讨论解决。\"\n" +
                "        ],\n" +
                "        \"keyPhrases\": [\n" +
                "          \"雪儿\",\n" +
                "          \"米燕\",\n" +
                "          \"美霞\"\n" +
                "        ],\n" +
                "        \"startParagraph\": 0,\n" +
                "        \"endParagraph\": 5\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  \"todoInfo\": [\n" +
                "    {\n" +
                "      \"todos\": \"明天，【美霞】、【雪儿】和【米燕】将各自负责二班半天的工作，同时【说话人1】需要在明天上午安排五个人的工作，可能只贡献一个名额，并在今天下午测试手写文本的待办\",\n" +
                "      \"startParagraph\": 0,\n" +
                "      \"endParagraph\": 5,\n" +
                "      \"detail\": [\n" +
                "        {\n" +
                "          \"todo\": \"明天，【美霞】、【雪儿】和【米燕】将各自负责二班半天的工作，同时【说话人1】需要在明天上午安排五个人的工作，可能只贡献一个名额，并在今天下午测试手写文本的待办\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ],\n" +
                "  \"title\": \"测试会议\",\n" +
                "  \"timestamp\": 1727163044000,\n" +
                "  \"focusOn\": {\n" +
                "    \"title\": \"重点关注\",\n" +
                "    \"focusOnList\": [\n" +
                "      {\n" +
                "        \"subTitle\": \"\",\n" +
                "        \"contentList\": [\n" +
                "          \"#### 马云与投资者的关系#012- 马云坚持公司决策权，确保阿里巴巴按其意志运作。#012- 1999年，孙正义提出3500万美元投资，马云因担心失去控制权，最终接受2000万美元。#012#012\"\n" +
                "        ]\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "}";

        AiTabletAITask aiTask = JSONObject.parseObject(temp, AiTabletAITask.class);

        String template = "<AiParagraphs>\n" +
                "<AiCommon> <center> </AiCommon>\n" +
                "<AiTitle>## {{AiTitle}} </AiTitle>\n" +
                "<AiTimestamp> {{AiTimestamp}} </AiTimestamp>\n" +
                "<AiCommon> </center> </AiCommon>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiAbstractTitle>#### {{AiAbstractTitle}} </AiAbstractTitle>\n" +
                "<AiCommon> <rounded> </AiCommon>\n" +
                "<AiAbstract> {{AiAbstract}} </AiAbstract>\n" +
                "<AiCommon> </rounded> </AiCommon>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiKeyPhrase> {{AiKeyPhrase}}</AiKeyPhrase>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiFocusOnTitle>#### {{AiFocusOnTitle}} </AiFocusOnTitle>\n" +
                "<AiCommon> <details> </AiCommon>\n" +
                "<AiCommon> <rounded> </AiCommon>\n" +
                "<AiFocusOnSubTitle> {{AiFocusOnSubTitle}} </AiFocusOnSubTitle>\n" +
                "<AiFocusOnContent> {{AiFocusOnContent}} </AiFocusOnContent>\n" +
                "<AiCommon> </rounded> </AiCommon>\n" +
                "<AiCommon> </details> </AiCommon>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiTopicTitle>#### {{AiTopicTitle}} </AiTopicTitle>\n" +
                "<AiCommon> <details> </AiCommon>\n" +
                "<AiCommon> <rounded> </AiCommon>\n" +
                "<AiConclusionsTopic>#### {{AiConclusionsTopic}}</AiConclusionsTopic>\n" +
                "<AiConclusions> {{AiConclusions}}</AiConclusions>\n" +
                "<AiCommon> </rounded> </AiCommon>\n" +
                "<AiCommon> </details> </AiCommon>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiSpeakerSummaryTitle>#### {{AiSpeakerSummaryTitle}} </AiSpeakerSummaryTitle>\n" +
                "<AiCommon> <details> </AiCommon>\n" +
                "<AiCommon> <rounded> </AiCommon>\n" +
                "<AiSpeakerName><b>{{AiSpeakerName}}：</b></AiSpeakerName>\n" +
                "<AiSpeakerSummary> {{AiSpeakerSummary}}</AiSpeakerSummary>\n" +
                "<AiCommon> </rounded> </AiCommon>\n" +
                "<AiCommon> </details> </AiCommon>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiTodoTitle> {{AiTodoTitle}} </AiTodoTitle>\n" +
                "<AiTodoContent> {{AiTodoContent}}</AiTodoContent>\n" +
                "</AiParagraphs>";

        // 原有的方法（返回 JSONObject）
        System.out.println("=== 原有方法（返回 JSONObject）===");
        System.out.println(getAiMD(aiTask, template));

        System.out.println("\n=== 新增的便于外部使用的方法 ===");

        // 方法1: 只获取 Markdown 内容
        System.out.println("1. 获取 Markdown 内容:");
        String markdownContent = getMarkdownContent(aiTask, template);
        System.out.println(markdownContent);

        // 方法2: 获取关键词
        System.out.println("\n2. 获取关键词:");
        List<String> keyPhrases = getKeyPhrases(aiTask, template);
        System.out.println("关键词: " + keyPhrases);

        // 方法3: 获取待办事项
        System.out.println("\n3. 获取待办事项:");
        List<String> todoList = getTodoList(aiTask, template);
        System.out.println("待办事项: " + todoList);

        // 方法4: 获取会议类型
        System.out.println("\n4. 获取会议类型:");
        String meetingType = getMeetingType(aiTask, template);
        System.out.println("会议类型: " + meetingType);

        // 方法5: 获取完整结果（推荐使用）
        System.out.println("\n5. 获取完整结果（推荐使用）:");
        AiMDResult result = getAiMDResult(aiTask, template);
        System.out.println("完整结果: " + result);

        System.out.println("\n=== 外部模块使用示例（无需依赖 fastjson）===");

        // 方式1: 直接使用 JSON 字符串（推荐）
        System.out.println("6. 直接从 JSON 字符串生成结果（推荐外部模块使用）:");
        AiMDResult resultFromJson = getAiMDResultFromJson(temp, template);
        System.out.println("从 JSON 字符串生成的结果: " + resultFromJson);

        // 方式2: 只获取 Markdown 内容
        System.out.println("\n7. 直接从 JSON 字符串获取 Markdown 内容:");
        String contentFromJson = getMarkdownContentFromJson(temp, template);
        System.out.println("从 JSON 字符串生成的 Markdown 内容长度: " + contentFromJson.length());

        System.out.println("\n=== 外部模块代码示例 ===");
        System.out.println("// 外部模块无需依赖 fastjson，可以直接使用:");
        System.out.println("// 方式1: 从 JSON 字符串直接获取结果（推荐）");
        System.out.println("// AiTabletMDUtil.AiMDResult result = AiTabletMDUtil.getAiMDResultFromJson(jsonString, template);");
        System.out.println("// String content = AiTabletMDUtil.getMarkdownContentFromJson(jsonString, template);");
        System.out.println("//");
        System.out.println("// 方式2: 先解析再处理");
        System.out.println("// AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonString);");
        System.out.println("// String content = AiTabletMDUtil.getMarkdownContent(aiTask, template);");
        System.out.println("// AiTabletMDUtil.AiMDResult result = AiTabletMDUtil.getAiMDResult(aiTask, template);");
    }

}

