package com.aispeech.llm2markdwon;

public enum AiMDLabelEnum {

    TITLE("AiTitle"),
    TIMESTAMP("AiTimestamp"),

    ABSTRACT_TITLE("AiAbstractTitle"),
    ABSTRACT("AiAbstract"),

    TOPIC_TITLE("AiTopicTitle"),
    TOPIC("AiTopic"),
    TOPIC_POINT("AiPoint"),

    CONCLUSIONS_TOPIC("AiConclusionsTopic"),
    CONCLUSIONS("AiConclusions"),

    KEY_PHRASE_TITLE("AiKeyPhraseTitle"),

    KEY_PHRASE("AiKeyPhrase"),

    FOCUS_ON_TITLE("AiFocusOnTitle"),
    FOCUS_ON_SUB_TITLE("AiFocusOnSubTitle"),
    FOCUS_ON_CONTENT("AiFocusOnContent"),


    TODO_TITLE("AiTodoTitle"),
    TODO_CONTENT("AiTodoContent"),

    QA_TITLE("AiQATitle"),
    QA_QUESTION("AiQAQuestion"),
    QA_ANSWER("AiQAAnswer"),

    SPEAKER_SUMMARY_TITLE("AiSpeakerSummaryTitle"),
    SPEAKER_NAME("AiSpeakerName"),
    SPEAKER_SUMMARY("AiSpeakerSummary"),

    COMMON("AiCommon"),

    PARAGRAPHS("AiParagraphs");


    private String label;
    AiMDLabelEnum(String label)
    {
        this.label = label;
    }
    public String getLabel()
    {
        return label;
    }
    public static AiMDLabelEnum getLabel(String label)
    {
        for (AiMDLabelEnum item : AiMDLabelEnum.values())
        {
            if (item.getLabel().equals(label))
            {
                return item;
            }
        }
        return null;
    }

}
