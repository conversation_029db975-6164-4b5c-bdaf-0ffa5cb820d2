package com.aispeech.llm2markdwon;

import java.util.List;

/**
 * 重点关注信息VO类
 * 用于处理会议中的重点关注内容
 */
public class FocusOnVo {
    
    private String title;
    private List<FocusOnList> focusOnList;
    
    // 构造函数
    public FocusOnVo() {}
    
    public FocusOnVo(String title, List<FocusOnList> focusOnList) {
        this.title = title;
        this.focusOnList = focusOnList;
    }
    
    // Getter和Setter方法
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public List<FocusOnList> getFocusOnList() {
        return focusOnList;
    }
    
    public void setFocusOnList(List<FocusOnList> focusOnList) {
        this.focusOnList = focusOnList;
    }
    
    /**
     * 重点关注列表项类
     */
    public static class FocusOnList {
        private String subTitle;
        private List<String> contentList;
        
        // 构造函数
        public FocusOnList() {}
        
        public FocusOnList(String subTitle, List<String> contentList) {
            this.subTitle = subTitle;
            this.contentList = contentList;
        }
        
        // Getter和Setter方法
        public String getSubTitle() {
            return subTitle;
        }
        
        public void setSubTitle(String subTitle) {
            this.subTitle = subTitle;
        }
        
        public List<String> getContentList() {
            return contentList;
        }
        
        public void setContentList(List<String> contentList) {
            this.contentList = contentList;
        }
    }
}
