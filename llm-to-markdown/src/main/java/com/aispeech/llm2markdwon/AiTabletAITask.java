package com.aispeech.llm2markdwon;

import com.alibaba.fastjson.JSONObject;
import java.util.List;

/**
 * AI平板任务实体类
 * 用于处理会议记录的AI分析结果
 */
public class AiTabletAITask {
    
    private String noteUid;
    private String objectId;
    private String sn;
    private String title;
    private Long timestamp;
    private String ocrText;
    
    // 状态字段
    private Integer abstractStatus;
    private Integer todoStatus;
    private Integer realtimeNoteStatus;
    private Integer realtimeNoteBatchStatus;
    private Integer aiStatus;
    
    // 核心数据字段
    private AiTabletAbstractVO abstractInfo;
    private List<AiTabletTodoVO> todoInfo;
    private FocusOnVo focusOn;
    private LLMQSResp.QSInfo qaInfo;
    private JSONObject speakerSummary;
    private AiTabletMeetingTypeVO meetingType;
    
    // 段落数据
    private List<ParagraphInfo> paragraph;
    private JSONObject speakerMap;
    
    // 构造函数
    public AiTabletAITask() {}
    
    // Getter和Setter方法
    public String getNoteUid() {
        return noteUid;
    }
    
    public void setNoteUid(String noteUid) {
        this.noteUid = noteUid;
    }
    
    public String getObjectId() {
        return objectId;
    }
    
    public void setObjectId(String objectId) {
        this.objectId = objectId;
    }
    
    public String getSn() {
        return sn;
    }
    
    public void setSn(String sn) {
        this.sn = sn;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public String getOcrText() {
        return ocrText;
    }
    
    public void setOcrText(String ocrText) {
        this.ocrText = ocrText;
    }
    
    public Integer getAbstractStatus() {
        return abstractStatus;
    }
    
    public void setAbstractStatus(Integer abstractStatus) {
        this.abstractStatus = abstractStatus;
    }
    
    public Integer getTodoStatus() {
        return todoStatus;
    }
    
    public void setTodoStatus(Integer todoStatus) {
        this.todoStatus = todoStatus;
    }
    
    public Integer getRealtimeNoteStatus() {
        return realtimeNoteStatus;
    }
    
    public void setRealtimeNoteStatus(Integer realtimeNoteStatus) {
        this.realtimeNoteStatus = realtimeNoteStatus;
    }
    
    public Integer getRealtimeNoteBatchStatus() {
        return realtimeNoteBatchStatus;
    }
    
    public void setRealtimeNoteBatchStatus(Integer realtimeNoteBatchStatus) {
        this.realtimeNoteBatchStatus = realtimeNoteBatchStatus;
    }
    
    public Integer getAiStatus() {
        return aiStatus;
    }
    
    public void setAiStatus(Integer aiStatus) {
        this.aiStatus = aiStatus;
    }
    
    public AiTabletAbstractVO getAbstractInfo() {
        return abstractInfo;
    }
    
    public void setAbstractInfo(AiTabletAbstractVO abstractInfo) {
        this.abstractInfo = abstractInfo;
    }
    
    public List<AiTabletTodoVO> getTodoInfo() {
        return todoInfo;
    }
    
    public void setTodoInfo(List<AiTabletTodoVO> todoInfo) {
        this.todoInfo = todoInfo;
    }
    
    public FocusOnVo getFocusOn() {
        return focusOn;
    }
    
    public void setFocusOn(FocusOnVo focusOn) {
        this.focusOn = focusOn;
    }
    
    public LLMQSResp.QSInfo getQaInfo() {
        return qaInfo;
    }
    
    public void setQaInfo(LLMQSResp.QSInfo qaInfo) {
        this.qaInfo = qaInfo;
    }
    
    public JSONObject getSpeakerSummary() {
        return speakerSummary;
    }
    
    public void setSpeakerSummary(JSONObject speakerSummary) {
        this.speakerSummary = speakerSummary;
    }
    
    public AiTabletMeetingTypeVO getMeetingType() {
        return meetingType;
    }
    
    public void setMeetingType(AiTabletMeetingTypeVO meetingType) {
        this.meetingType = meetingType;
    }
    
    public List<ParagraphInfo> getParagraph() {
        return paragraph;
    }
    
    public void setParagraph(List<ParagraphInfo> paragraph) {
        this.paragraph = paragraph;
    }
    
    public JSONObject getSpeakerMap() {
        return speakerMap;
    }
    
    public void setSpeakerMap(JSONObject speakerMap) {
        this.speakerMap = speakerMap;
    }
    
    /**
     * 段落信息内部类
     */
    public static class ParagraphInfo {
        private String paragraphId;
        private String startTimeStr;
        private String content;
        private Integer speaker;
        private String language;
        
        // Getter和Setter方法
        public String getParagraphId() {
            return paragraphId;
        }
        
        public void setParagraphId(String paragraphId) {
            this.paragraphId = paragraphId;
        }
        
        public String getStartTimeStr() {
            return startTimeStr;
        }
        
        public void setStartTimeStr(String startTimeStr) {
            this.startTimeStr = startTimeStr;
        }
        
        public String getContent() {
            return content;
        }
        
        public void setContent(String content) {
            this.content = content;
        }
        
        public Integer getSpeaker() {
            return speaker;
        }
        
        public void setSpeaker(Integer speaker) {
            this.speaker = speaker;
        }
        
        public String getLanguage() {
            return language;
        }
        
        public void setLanguage(String language) {
            this.language = language;
        }
    }
}
