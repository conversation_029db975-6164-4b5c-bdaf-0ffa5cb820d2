package com.aispeech.llm2markdwon;

import java.util.List;

/**
 * LLM问答响应类
 * 用于处理会议中的问答信息
 */
public class LLMQSResp {
    
    /**
     * 问答信息类
     */
    public static class QSInfo {
        private List<QAPair> qaRecord;
        
        // 构造函数
        public QSInfo() {}
        
        public QSInfo(List<QAPair> qaRecord) {
            this.qaRecord = qaRecord;
        }
        
        // Getter和Setter方法
        public List<QAPair> getQaRecord() {
            return qaRecord;
        }
        
        public void setQaRecord(List<QAPair> qaRecord) {
            this.qaRecord = qaRecord;
        }
    }
    
    /**
     * 问答对类
     */
    public static class QAPair {
        private String question;
        private String answer;
        
        // 构造函数
        public QAPair() {}
        
        public QAPair(String question, String answer) {
            this.question = question;
            this.answer = answer;
        }
        
        // Getter和Setter方法
        public String getQuestion() {
            return question;
        }
        
        public void setQuestion(String question) {
            this.question = question;
        }
        
        public String getAnswer() {
            return answer;
        }
        
        public void setAnswer(String answer) {
            this.answer = answer;
        }
    }
}
