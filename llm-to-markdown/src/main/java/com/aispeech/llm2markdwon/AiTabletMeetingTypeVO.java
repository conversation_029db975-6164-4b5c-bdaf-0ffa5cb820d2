package com.aispeech.llm2markdwon;

/**
 * AI平板会议类型VO类
 * 用于处理会议类型信息
 */
public class AiTabletMeetingTypeVO {
    
    private String meetingType;
    
    // 构造函数
    public AiTabletMeetingTypeVO() {}
    
    public AiTabletMeetingTypeVO(String meetingType) {
        this.meetingType = meetingType;
    }
    
    // Getter和Setter方法
    public String getMeetingType() {
        return meetingType;
    }
    
    public void setMeetingType(String meetingType) {
        this.meetingType = meetingType;
    }
}
