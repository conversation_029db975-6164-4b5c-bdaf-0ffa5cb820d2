package com.aispeech.llm2markdwon;

import java.util.List;

/**
 * AI平板待办事项VO类
 * 用于处理会议中的待办事项信息
 */
public class AiTabletTodoVO {
    
    private String todos;
    private Integer startParagraph;
    private Integer endParagraph;
    private List<TodoDetail> detail;
    
    // 构造函数
    public AiTabletTodoVO() {}
    
    public AiTabletTodoVO(String todos, Integer startParagraph, Integer endParagraph, List<TodoDetail> detail) {
        this.todos = todos;
        this.startParagraph = startParagraph;
        this.endParagraph = endParagraph;
        this.detail = detail;
    }
    
    // Getter和Setter方法
    public String getTodos() {
        return todos;
    }
    
    public void setTodos(String todos) {
        this.todos = todos;
    }
    
    public Integer getStartParagraph() {
        return startParagraph;
    }
    
    public void setStartParagraph(Integer startParagraph) {
        this.startParagraph = startParagraph;
    }
    
    public Integer getEndParagraph() {
        return endParagraph;
    }
    
    public void setEndParagraph(Integer endParagraph) {
        this.endParagraph = endParagraph;
    }
    
    public List<TodoDetail> getDetail() {
        return detail;
    }
    
    public void setDetail(List<TodoDetail> detail) {
        this.detail = detail;
    }
    
    /**
     * 待办事项详情类
     */
    public static class TodoDetail {
        private String todo;
        
        // 构造函数
        public TodoDetail() {}
        
        public TodoDetail(String todo) {
            this.todo = todo;
        }
        
        // Getter和Setter方法
        public String getTodo() {
            return todo;
        }
        
        public void setTodo(String todo) {
            this.todo = todo;
        }
    }
}
