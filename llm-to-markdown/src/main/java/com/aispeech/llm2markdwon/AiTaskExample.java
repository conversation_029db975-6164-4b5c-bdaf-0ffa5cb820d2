package com.aispeech.llm2markdwon;

import com.alibaba.fastjson.JSONObject;

/**
 * AI任务示例类
 * 演示如何使用提供的JSON数据创建AiTabletAITask对象
 */
public class AiTaskExample {
    
    /**
     * 使用您提供的JSON数据创建AiTabletAITask对象
     */
    public static AiTabletAITask createTaskFromProvidedData() {
        // 您提供的JSON数据
        String jsonString = "{\n" +
                "  \"dialog_id\": \"24ae12d7-c47d-4725-a820-e2c61bcaac45\",\n" +
                "  \"abstract\": \"会议首先讨论了测试流程的简化与执行，包括步骤顺序和匹配显示问题，同时涉及测试日志的详细记录；其次针对系统功能问题，涉及连接匹配、高亮显示及版本同步等保护修复措施；最后明确了后续工作需聚焦流程优化与功能修复。\",\n" +
                "  \"content\": [\n" +
                "    {\n" +
                "      \"topic\": \"会议讨论测试流程和系统功能问题\",\n" +
                "      \"sub_topic_list\": [\n" +
                "        {\n" +
                "          \"sub_topic\": \"测试流程讨论\",\n" +
                "          \"points\": [\n" +
                "            \"说话人1和2讨论了测试流程的简化和执行，包括测试的步骤和顺序。\",\n" +
                "            \"说话人2提到测试流程的匹配和显示问题，以及测试的详细日志。\"\n" +
                "          ]\n" +
                "        },\n" +
                "        {\n" +
                "          \"sub_topic\": \"系统功能问题讨论\",\n" +
                "          \"points\": [\n" +
                "            \"说话人2提到系统功能的连接和匹配问题，以及高亮位置的显示问题。\",\n" +
                "            \"说话人3讨论了系统功能的保护和修复，以及版本的保存和同步问题。\"\n" +
                "          ]\n" +
                "        }\n" +
                "      ],\n" +
                "      \"conclusions\": [],\n" +
                "      \"key_phrases\": [\"冒烟\", \"测试测试测试\", \"功能流程\", \"版本\", \"高亮位置\"]\n" +
                "    },\n" +
                "    {\n" +
                "      \"topic\": \"\",\n" +
                "      \"sub_topic_list\": [],\n" +
                "      \"conclusions\": [],\n" +
                "      \"key_phrases\": []\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        
        // 解析JSON数据
        JSONObject jsonData = JSONObject.parseObject(jsonString);
        
        // 使用映射器创建AiTabletAITask对象
        AiTabletAITask aiTask = AiTaskDataMapper.mapFromJsonData(jsonData);
        
        return aiTask;
    }
    
    /**
     * 测试方法 - 创建任务并生成Markdown
     */
    public static void testCreateTaskAndGenerateMarkdown() {
        // 创建AiTabletAITask对象
        AiTabletAITask aiTask = createTaskFromProvidedData();
        
        // 使用默认模板
        String template = "<AiParagraphs>\n" +
                "<AiCommon> <center> </AiCommon>\n" +
                "<AiTitle>## {{AiTitle}} </AiTitle>\n" +
                "<AiTimestamp> {{AiTimestamp}} </AiTimestamp>\n" +
                "<AiCommon> </center> </AiCommon>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiAbstractTitle>#### {{AiAbstractTitle}} </AiAbstractTitle>\n" +
                "<AiCommon> <rounded> </AiCommon>\n" +
                "<AiAbstract> {{AiAbstract}} </AiAbstract>\n" +
                "<AiCommon> </rounded> </AiCommon>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiKeyPhrase> {{AiKeyPhrase}}</AiKeyPhrase>\n" +
                "</AiParagraphs>\n" +
                "<AiParagraphs>\n" +
                "<AiTopicTitle>#### {{AiTopicTitle}} </AiTopicTitle>\n" +
                "<AiCommon> <details> </AiCommon>\n" +
                "<AiCommon> <rounded> </AiCommon>\n" +
                "<AiTopic>{{AiTopic}}</AiTopic>\n" +
                "<AiPoint>{{AiPoint}}</AiPoint>\n" +
                "<AiCommon> </rounded> </AiCommon>\n" +
                "<AiCommon> </details> </AiCommon>\n" +
                "</AiParagraphs>";
        
        // 生成Markdown
        JSONObject result = AiTabletMDUtil.getAiMD(aiTask, template);
        
        // 输出结果
        System.out.println("生成的Markdown内容：");
        System.out.println(result.getString("content"));
        
        if (result.containsKey("key")) {
            System.out.println("\n关键词：");
            System.out.println(result.get("key"));
        }
        
        if (result.containsKey("meetingType")) {
            System.out.println("\n会议类型：");
            System.out.println(result.getString("meetingType"));
        }
    }
    
    /**
     * 主方法 - 用于测试
     */
    public static void main(String[] args) {
        testCreateTaskAndGenerateMarkdown();
    }
}
