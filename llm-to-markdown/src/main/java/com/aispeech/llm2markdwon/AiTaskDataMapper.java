package com.aispeech.llm2markdwon;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.util.ArrayList;
import java.util.List;

/**
 * AI任务数据映射器
 * 用于将JSON数据转换为AiTabletAITask对象
 */
public class AiTaskDataMapper {
    
    /**
     * 将JSON字符串转换为AiTabletAITask对象
     * 外部模块可以直接传入JSON字符串，无需依赖fastjson
     *
     * @param jsonString 包含dialog_id、abstract、content等字段的JSON字符串
     * @return AiTabletAITask对象，解析失败时返回null
     */
    public static AiTabletAITask mapFromJsonData(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }

        try {
            JSONObject jsonData = JSONObject.parseObject(jsonString);
            return mapFromJsonData(jsonData);
        } catch (Exception e) {
            // 解析失败时返回null，避免抛出异常影响外部模块
            System.err.println("JSON解析失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 将JSON数据转换为AiTabletAITask对象
     *
     * @param jsonData 包含dialog_id、abstract、content等字段的JSON数据
     * @return AiTabletAITask对象
     */
    public static AiTabletAITask mapFromJsonData(JSONObject jsonData) {
        if (jsonData == null) {
            return null;
        }
        
        AiTabletAITask aiTask = new AiTabletAITask();
        
        // 设置基本信息
        aiTask.setObjectId(jsonData.getString("dialog_id"));
        aiTask.setNoteUid(jsonData.getString("dialog_id"));
        aiTask.setTitle("AI会议记录"); // 默认标题
        aiTask.setTimestamp(System.currentTimeMillis()); // 当前时间戳
        
        // 设置状态
        aiTask.setAbstractStatus(2);
        aiTask.setTodoStatus(2);
        aiTask.setAiStatus(2);
        
        // 处理摘要信息
        String abstractContent = jsonData.getString("abstract");
        if (abstractContent != null) {
            AiTabletAbstractVO abstractVO = createAbstractVO(abstractContent, jsonData.getJSONArray("content"));
            aiTask.setAbstractInfo(abstractVO);
        }
        
        // 设置会议类型
        AiTabletMeetingTypeVO meetingType = new AiTabletMeetingTypeVO("AI会议记录");
        aiTask.setMeetingType(meetingType);
        
        return aiTask;
    }
    
    /**
     * 创建摘要VO对象
     */
    private static AiTabletAbstractVO createAbstractVO(String abstractContent, JSONArray contentArray) {
        AiTabletAbstractVO abstractVO = new AiTabletAbstractVO();
        abstractVO.setContent(abstractContent);
        
        if (contentArray != null && contentArray.size() > 0) {
            List<AiTabletAbstractVO.AbstractContent> details = new ArrayList<>();
            
            for (int i = 0; i < contentArray.size(); i++) {
                JSONObject contentItem = contentArray.getJSONObject(i);
                if (contentItem == null) continue;
                
                String topic = contentItem.getString("topic");
                if (topic == null || topic.trim().isEmpty()) continue;
                
                AiTabletAbstractVO.AbstractContent detail = new AiTabletAbstractVO.AbstractContent();
                detail.setTopic(topic);
                
                // 处理子主题列表
                JSONArray subTopicList = contentItem.getJSONArray("sub_topic_list");
                if (subTopicList != null && subTopicList.size() > 0) {
                    List<String> points = new ArrayList<>();
                    List<String> keyPhrases = new ArrayList<>();
                    
                    for (int j = 0; j < subTopicList.size(); j++) {
                        JSONObject subTopic = subTopicList.getJSONObject(j);
                        if (subTopic == null) continue;
                        
                        // 添加要点
                        JSONArray pointsArray = subTopic.getJSONArray("points");
                        if (pointsArray != null) {
                            for (int k = 0; k < pointsArray.size(); k++) {
                                String point = pointsArray.getString(k);
                                if (point != null && !point.trim().isEmpty()) {
                                    points.add(point);
                                }
                            }
                        }
                    }
                    
                    detail.setPoints(points);
                }
                
                // 处理关键词
                JSONArray keyPhrasesArray = contentItem.getJSONArray("key_phrases");
                if (keyPhrasesArray != null) {
                    List<String> keyPhrases = new ArrayList<>();
                    for (int k = 0; k < keyPhrasesArray.size(); k++) {
                        String keyPhrase = keyPhrasesArray.getString(k);
                        if (keyPhrase != null && !keyPhrase.trim().isEmpty()) {
                            keyPhrases.add(keyPhrase);
                        }
                    }
                    detail.setKeyPhrases(keyPhrases);
                }
                
                // 处理结论
                JSONArray conclusionsArray = contentItem.getJSONArray("conclusions");
                if (conclusionsArray != null) {
                    List<String> conclusions = new ArrayList<>();
                    for (int k = 0; k < conclusionsArray.size(); k++) {
                        String conclusion = conclusionsArray.getString(k);
                        if (conclusion != null && !conclusion.trim().isEmpty()) {
                            conclusions.add(conclusion);
                        }
                    }
                    detail.setConclusions(conclusions);
                }
                
                details.add(detail);
            }
            
            abstractVO.setDetail(details);
        }
        
        return abstractVO;
    }
    
    /**
     * 创建AiTabletAITask对象的便捷方法
     *
     * @param dialogId 对话ID
     * @param abstractContent 摘要内容
     * @param contentArray 内容数组
     * @return AiTabletAITask对象
     */
    public static AiTabletAITask createAiTask(String dialogId, String abstractContent, JSONArray contentArray) {
        JSONObject jsonData = new JSONObject();
        jsonData.put("dialog_id", dialogId);
        jsonData.put("abstract", abstractContent);
        jsonData.put("content", contentArray);

        return mapFromJsonData(jsonData);
    }

    /**
     * 直接从JSON字符串提取待办事项列表
     * 简单、直接、高效的处理方式
     *
     * @param jsonString 包含 action_items_list 和 total_todos_list 的JSON字符串
     * @return 待办事项列表，如果解析失败或无数据返回空列表
     */
    public static List<String> extractTodoListFromJson(String jsonString) {
        List<String> todoList = new ArrayList<>();

        if (jsonString == null || jsonString.trim().isEmpty()) {
            return todoList; // 返回空列表
        }

        try {
            JSONObject jsonData = JSONObject.parseObject(jsonString);

            // 处理 action_items_list
            JSONArray actionItems = jsonData.getJSONArray("action_items_list");
            if (actionItems != null) {
                for (int i = 0; i < actionItems.size(); i++) {
                    String item = actionItems.getString(i);
                    if (item != null && !item.trim().isEmpty()) {
                        todoList.add(item.trim());
                    }
                }
            }

            // 处理 total_todos_list
            JSONArray totalTodos = jsonData.getJSONArray("total_todos_list");
            if (totalTodos != null) {
                for (int i = 0; i < totalTodos.size(); i++) {
                    String item = totalTodos.getString(i);
                    if (item != null && !item.trim().isEmpty()) {
                        todoList.add(item.trim());
                    }
                }
            }

        } catch (Exception e) {
            // 解析失败时返回空列表，保持一致性
            System.err.println("解析待办事项失败: " + e.getMessage());
        }

        return todoList;
    }
}
