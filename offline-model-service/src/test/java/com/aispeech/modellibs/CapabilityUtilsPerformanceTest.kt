package com.aispeech.modellibs

import com.aispeech.modellibs.CapabilityUtils
import kotlinx.coroutines.test.runTest
import org.junit.Test
import kotlin.system.measureTimeMillis

/**
 * CapabilityUtils 性能测试
 * 验证缓存机制的性能改进效果
 */
class CapabilityUtilsPerformanceTest {

    @Test
    fun testApuSysSupportedCachePerformance() = runTest {
        println("=== APU 支持检查性能测试 ===")
        
        // 测试首次调用（包含文件系统检查）
        val firstCallTime = measureTimeMillis {
            val result = CapabilityUtils.isApuSysSupported()
            println("首次调用结果: $result")
        }
        println("首次调用耗时: ${firstCallTime}ms")
        
        // 测试后续调用（使用缓存）
        val cachedCallTimes = mutableListOf<Long>()
        repeat(100) {
            val time = measureTimeMillis {
                CapabilityUtils.isApuSysSupported()
            }
            cachedCallTimes.add(time)
        }
        
        val avgCachedTime = cachedCallTimes.average()
        val maxCachedTime = cachedCallTimes.maxOrNull() ?: 0
        val minCachedTime = cachedCallTimes.minOrNull() ?: 0
        
        println("缓存调用平均耗时: ${String.format("%.2f", avgCachedTime)}ms")
        println("缓存调用最大耗时: ${maxCachedTime}ms")
        println("缓存调用最小耗时: ${minCachedTime}ms")
        
        // 性能改进计算
        val improvementRatio = if (avgCachedTime > 0) firstCallTime / avgCachedTime else 0.0
        println("性能改进倍数: ${String.format("%.1f", improvementRatio)}x")
        
        // 验证缓存效果
        assert(avgCachedTime < firstCallTime) { "缓存调用应该比首次调用更快" }
        assert(avgCachedTime < 1.0) { "缓存调用应该在1ms以内" }
        
        println("=== 性能测试完成 ===")
    }

    @Test
    fun testHighFrequencyCallsPerformance() = runTest {
        println("=== 高频调用性能测试 ===")
        
        val callCount = 1000
        val totalTime = measureTimeMillis {
            repeat(callCount) {
                CapabilityUtils.isApuSysSupported()
            }
        }
        
        val avgTimePerCall = totalTime.toDouble() / callCount
        println("${callCount}次调用总耗时: ${totalTime}ms")
        println("平均每次调用耗时: ${String.format("%.3f", avgTimePerCall)}ms")
        
        // 高频调用性能要求
        assert(avgTimePerCall < 0.1) { "高频调用平均耗时应该在0.1ms以内" }
        assert(totalTime < 100) { "1000次调用总耗时应该在100ms以内" }
        
        println("=== 高频调用测试完成 ===")
    }

    @Test
    fun testForceCheckVsCachedPerformance() = runTest {
        println("=== 强制检查 vs 缓存检查性能对比 ===")
        
        // 预热缓存
        CapabilityUtils.isApuSysSupported()
        
        // 测试缓存版本
        val cachedTime = measureTimeMillis {
            repeat(10) {
                CapabilityUtils.isApuSysSupported()
            }
        }
        
        // 测试强制检查版本
        val forceCheckTime = measureTimeMillis {
            repeat(10) {
                CapabilityUtils.forceCheckApuSysSupported()
            }
        }
        
        println("10次缓存检查耗时: ${cachedTime}ms")
        println("10次强制检查耗时: ${forceCheckTime}ms")
        
        val performanceGain = if (cachedTime > 0) forceCheckTime.toDouble() / cachedTime else 0.0
        println("缓存性能提升: ${String.format("%.1f", performanceGain)}x")
        
        // 验证缓存确实更快
        assert(cachedTime < forceCheckTime) { "缓存版本应该比强制检查更快" }
        
        println("=== 性能对比测试完成 ===")
    }

    @Test
    fun testConcurrentAccessPerformance() = runTest {
        println("=== 并发访问性能测试 ===")
        
        val concurrentTime = measureTimeMillis {
            // 模拟并发访问
            val jobs = (1..50).map {
                kotlinx.coroutines.async {
                    repeat(20) {
                        CapabilityUtils.isApuSysSupported()
                    }
                }
            }
            jobs.forEach { it.await() }
        }
        
        println("50个并发任务，每个20次调用，总耗时: ${concurrentTime}ms")
        
        val avgTimePerTask = concurrentTime.toDouble() / 50
        println("平均每个任务耗时: ${String.format("%.2f", avgTimePerTask)}ms")
        
        // 并发性能要求
        assert(concurrentTime < 1000) { "并发测试总耗时应该在1秒以内" }
        assert(avgTimePerTask < 20) { "平均每个任务耗时应该在20ms以内" }
        
        println("=== 并发访问测试完成 ===")
    }
}
