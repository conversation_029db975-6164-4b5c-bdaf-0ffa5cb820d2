package com.aispeech.modellibs

import com.aispeech.modellibs.ITaskStateCallback
import com.aispeech.modellibs.TaskState
import com.aispeech.modellibs.TaskStatus
import com.aispeech.modellibs.OfflineModelService
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.async
import org.junit.Test
import org.mockito.kotlin.*
import kotlin.system.measureTimeMillis
import java.util.concurrent.atomic.AtomicInteger

/**
 * getTaskStateAsync 性能测试
 * 验证优化后的性能改进效果
 */
class GetTaskStateAsyncPerformanceTest {

    @Test
    fun testSingleCallPerformance() = runTest {
        println("=== 单次调用性能测试 ===")
        
        // 模拟服务实例（实际测试中需要完整的依赖注入）
        val service = createMockService()
        
        val taskId = "test_task_123"
        var callbackInvoked = false
        
        val callback = object : ITaskStateCallback.Stub() {
            override fun onTaskStateRetrieved(taskState: TaskState?) {
                callbackInvoked = true
                println("任务状态获取成功: ${taskState?.getReadableStatus()}")
            }
            
            override fun onTaskNotFound(taskId: String?) {
                callbackInvoked = true
                println("任务未找到: $taskId")
            }
            
            override fun onQueryError(taskId: String?, errorCode: Int, errorMessage: String?) {
                callbackInvoked = true
                println("查询错误: $errorCode - $errorMessage")
            }
        }
        
        val callTime = measureTimeMillis {
            service.getTaskStateAsync(taskId, callback)
            // 等待回调执行
            var waitTime = 0
            while (!callbackInvoked && waitTime < 1000) {
                delay(10)
                waitTime += 10
            }
        }
        
        println("单次调用总耗时: ${callTime}ms")
        assert(callbackInvoked) { "回调应该被调用" }
        assert(callTime < 100) { "单次调用应该在100ms以内完成" }
        
        println("=== 单次调用测试完成 ===")
    }

    @Test
    fun testHighFrequencyCallsPerformance() = runTest {
        println("=== 高频调用性能测试 ===")
        
        val service = createMockService()
        val callCount = 100
        val completedCalls = AtomicInteger(0)
        
        val totalTime = measureTimeMillis {
            repeat(callCount) { index ->
                val taskId = "task_$index"
                val callback = object : ITaskStateCallback.Stub() {
                    override fun onTaskStateRetrieved(taskState: TaskState?) {
                        completedCalls.incrementAndGet()
                    }
                    
                    override fun onTaskNotFound(taskId: String?) {
                        completedCalls.incrementAndGet()
                    }
                    
                    override fun onQueryError(taskId: String?, errorCode: Int, errorMessage: String?) {
                        completedCalls.incrementAndGet()
                    }
                }
                
                service.getTaskStateAsync(taskId, callback)
            }
            
            // 等待所有回调完成
            var waitTime = 0
            while (completedCalls.get() < callCount && waitTime < 5000) {
                delay(50)
                waitTime += 50
            }
        }
        
        val avgTimePerCall = totalTime.toDouble() / callCount
        println("${callCount}次调用总耗时: ${totalTime}ms")
        println("平均每次调用耗时: ${String.format("%.2f", avgTimePerCall)}ms")
        println("完成的调用数: ${completedCalls.get()}/$callCount")
        
        assert(completedCalls.get() == callCount) { "所有调用都应该完成" }
        assert(avgTimePerCall < 10) { "平均每次调用应该在10ms以内" }
        
        println("=== 高频调用测试完成 ===")
    }

    @Test
    fun testConcurrentCallsPerformance() = runTest {
        println("=== 并发调用性能测试 ===")
        
        val service = createMockService()
        val concurrentCount = 20
        val callsPerTask = 10
        val completedCalls = AtomicInteger(0)
        
        val totalTime = measureTimeMillis {
            val jobs = (1..concurrentCount).map { taskIndex ->
                async {
                    repeat(callsPerTask) { callIndex ->
                        val taskId = "concurrent_task_${taskIndex}_$callIndex"
                        val callback = object : ITaskStateCallback.Stub() {
                            override fun onTaskStateRetrieved(taskState: TaskState?) {
                                completedCalls.incrementAndGet()
                            }
                            
                            override fun onTaskNotFound(taskId: String?) {
                                completedCalls.incrementAndGet()
                            }
                            
                            override fun onQueryError(taskId: String?, errorCode: Int, errorMessage: String?) {
                                completedCalls.incrementAndGet()
                            }
                        }
                        
                        service.getTaskStateAsync(taskId, callback)
                    }
                }
            }
            
            jobs.forEach { it.await() }
            
            // 等待所有回调完成
            val expectedCalls = concurrentCount * callsPerTask
            var waitTime = 0
            while (completedCalls.get() < expectedCalls && waitTime < 10000) {
                delay(100)
                waitTime += 100
            }
        }
        
        val expectedCalls = concurrentCount * callsPerTask
        val avgTimePerCall = totalTime.toDouble() / expectedCalls
        
        println("${concurrentCount}个并发任务，每个${callsPerTask}次调用")
        println("总调用数: $expectedCalls，完成数: ${completedCalls.get()}")
        println("总耗时: ${totalTime}ms")
        println("平均每次调用耗时: ${String.format("%.2f", avgTimePerCall)}ms")
        
        assert(completedCalls.get() == expectedCalls) { "所有并发调用都应该完成" }
        assert(totalTime < 5000) { "并发测试应该在5秒内完成" }
        
        println("=== 并发调用测试完成 ===")
    }

    @Test
    fun testMemoryUsageOptimization() = runTest {
        println("=== 内存使用优化测试 ===")
        
        val service = createMockService()
        
        // 获取初始内存使用
        System.gc()
        val initialMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()
        
        // 执行大量调用
        val callCount = 500
        val completedCalls = AtomicInteger(0)
        
        repeat(callCount) { index ->
            val taskId = "memory_test_task_$index"
            val callback = object : ITaskStateCallback.Stub() {
                override fun onTaskStateRetrieved(taskState: TaskState?) {
                    completedCalls.incrementAndGet()
                }
                
                override fun onTaskNotFound(taskId: String?) {
                    completedCalls.incrementAndGet()
                }
                
                override fun onQueryError(taskId: String?, errorCode: Int, errorMessage: String?) {
                    completedCalls.incrementAndGet()
                }
            }
            
            service.getTaskStateAsync(taskId, callback)
        }
        
        // 等待所有调用完成
        var waitTime = 0
        while (completedCalls.get() < callCount && waitTime < 10000) {
            delay(100)
            waitTime += 100
        }
        
        // 强制垃圾回收并检查内存使用
        System.gc()
        delay(100)
        val finalMemory = Runtime.getRuntime().totalMemory() - Runtime.getRuntime().freeMemory()
        
        val memoryIncrease = finalMemory - initialMemory
        val memoryPerCall = memoryIncrease.toDouble() / callCount
        
        println("初始内存使用: ${initialMemory / 1024}KB")
        println("最终内存使用: ${finalMemory / 1024}KB")
        println("内存增长: ${memoryIncrease / 1024}KB")
        println("平均每次调用内存开销: ${String.format("%.2f", memoryPerCall)}字节")
        println("完成的调用数: ${completedCalls.get()}/$callCount")
        
        assert(completedCalls.get() == callCount) { "所有调用都应该完成" }
        assert(memoryPerCall < 1000) { "平均每次调用内存开销应该小于1KB" }
        
        println("=== 内存使用测试完成 ===")
    }

    /**
     * 创建模拟的服务实例
     * 注意：这是简化的模拟，实际测试需要完整的依赖注入
     */
    private fun createMockService(): OfflineModelService {
        // 这里应该创建一个完整的模拟服务
        // 由于依赖复杂，这里只是示例代码
        return mock<OfflineModelService>()
    }
}
