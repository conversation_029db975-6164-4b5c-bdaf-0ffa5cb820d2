package com.aispeech.modellibs

import com.aispeech.modellibs.ITaskStateCallback
import com.aispeech.modellibs.TaskState
import com.aispeech.modellibs.TaskStatus
import com.aispeech.modellibs.dispatcher.TaskDispatcher
import com.aispeech.modellibs.database.TaskDao
import com.aispeech.modellibs.database.TaskEntity
import com.aispeech.modellibs.file.TaskFileManager
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.mockito.kotlin.*
import java.util.concurrent.atomic.AtomicLong

class TaskStateAsyncTest {

    @Test
    fun testGetTaskStateAsync_Success() = runTest {
        // Arrange
        val taskDao = mock<TaskDao>()
        val fileManager = mock<TaskFileManager>()
        val taskIdGenerator = AtomicLong(0)

        val dispatcher = TaskDispatcher(taskDao, fileManager, taskIdGenerator)

        val taskId = "test_task_123"
        val taskEntity = TaskEntity(
            taskId = taskId,
            taskType = 1,
            status = TaskStatus.COMPLETED,
            inputDataPath = "/test/path",
            resultData = "test result"
        )

        whenever(taskDao.getTaskById(taskId)).thenReturn(taskEntity)

        val callback = mock<ITaskStateCallback>()

        // Act - 现在是 suspend 函数
        dispatcher.getTaskStateAsync(taskId, callback)

        // Assert
        verify(callback).onTaskStateRetrieved(any())
        verify(callback, never()).onTaskNotFound(any())
        verify(callback, never()).onQueryError(any(), any(), any())
    }

    @Test
    fun testGetTaskStateAsync_TaskNotFound() = runTest {
        // Arrange
        val taskDao = mock<TaskDao>()
        val fileManager = mock<TaskFileManager>()
        val taskIdGenerator = AtomicLong(0)

        val dispatcher = TaskDispatcher(taskDao, fileManager, taskIdGenerator)

        val taskId = "nonexistent_task"
        whenever(taskDao.getTaskById(taskId)).thenReturn(null)

        val callback = mock<ITaskStateCallback>()

        // Act - 现在是 suspend 函数
        dispatcher.getTaskStateAsync(taskId, callback)

        // Assert
        verify(callback).onTaskNotFound(taskId)
        verify(callback, never()).onTaskStateRetrieved(any())
        verify(callback, never()).onQueryError(any(), any(), any())
    }

    @Test
    fun testGetTaskStateAsync_QueryError() = runTest {
        // Arrange
        val taskDao = mock<TaskDao>()
        val fileManager = mock<TaskFileManager>()
        val taskIdGenerator = AtomicLong(0)

        val dispatcher = TaskDispatcher(taskDao, fileManager, taskIdGenerator)

        val taskId = "error_task"
        whenever(taskDao.getTaskById(taskId)).thenThrow(RuntimeException("Database error"))

        val callback = mock<ITaskStateCallback>()

        // Act - 现在是 suspend 函数
        dispatcher.getTaskStateAsync(taskId, callback)

        // Assert
        verify(callback).onQueryError(eq(taskId), any(), contains("Database error"))
        verify(callback, never()).onTaskStateRetrieved(any())
        verify(callback, never()).onTaskNotFound(any())
    }
}
