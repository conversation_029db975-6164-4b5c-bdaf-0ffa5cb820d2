package com.aispeech.modellibs

import androidx.test.ext.junit.runners.AndroidJUnit4
import com.aispeech.modellibs.status.ServiceStatusManagerImpl
import com.aispeech.modellibs.task.ITaskManager
import io.mockk.every
import io.mockk.mockk
import junit.framework.Assert.assertEquals
import junit.framework.Assert.assertFalse
import junit.framework.Assert.assertTrue
import kotlinx.coroutines.flow.MutableSharedFlow
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith

@RunWith(AndroidJUnit4::class)
class StatusManagerTest {

    private lateinit var statusManager: ServiceStatusManagerImpl
    private lateinit var mockTaskManager: ITaskManager
    private lateinit var mockCallback: IModelStatusCallback

    @Before
    fun setup() {
        statusManager = ServiceStatusManagerImpl()
        mockTaskManager = mockk(relaxed = true)
        mockCallback = mockk(relaxed = true)
        every { mockTaskManager.taskEvents } returns MutableSharedFlow()
    }

    @Test
    fun testInitialStatus() {
        val status = statusManager.getCurrentStatus()
        assertEquals(ServiceStatusCode.INITIALIZING, status.statusCode)
        assertFalse(status.isAuthorized)
        assertFalse(status.isInitialized)
    }

    @Test
    fun testAuthorizationSuccess() {
        statusManager.setAuthorized()
        val status = statusManager.getCurrentStatus()
        assertTrue(status.isAuthorized)
    }

    @Test
    fun testAuthorizationFailure() {
        statusManager.setAuthorizationFailed("授权失败")
        val status = statusManager.getCurrentStatus()
        assertFalse(status.isAuthorized)
        assertEquals(ServiceStatusCode.ERROR, status.statusCode)
    }

    @Test
    fun testInitialization() {
        statusManager.setAuthorized()
        statusManager.setInitialized()

        val status = statusManager.getCurrentStatus()
        assertTrue(status.isInitialized)
        assertEquals(ServiceStatusCode.IDLE, status.statusCode)
    }
}
