package com.aispeech.modellibs

import android.app.Activity
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.TextView
import com.aispeech.aibase.AILog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch

/**
 * 模型服务测试活动
 * 使用 ModelServiceClient 测试 OfflineModelService 的功能
 */
class ModelServiceTestActivity : Activity() {

  companion object {
    private const val TAG = "ModelServiceTestActivity"
  }

  private lateinit var modelServiceClient: ModelServiceClient
  private var isServiceConnected = false

  private lateinit var btnConnect: Button
  private lateinit var btnDisconnect: Button
  private lateinit var btnCheckSupport: Button
  private lateinit var btnExtractAbstract: Button
  private lateinit var btnDetectActionItem: Button
  private lateinit var btnDetectMeetingType: Button
  private lateinit var btnQA: Button
  private lateinit var btnRoleSummary: Button
  private lateinit var btnGetStatus: Button
  private lateinit var btnRegisterStatus: Button
  private lateinit var btnUnregisterStatus: Button
  private lateinit var btnQueryTask: Button
  private lateinit var btnCancelTask: Button
  private lateinit var tvResult: TextView
  private lateinit var tvStatus: TextView
  private lateinit var tvLogs: TextView
  private lateinit var scrollView: ScrollView

  // 协程作用域
  private val activityScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

  // 状态监听相关
  private var statusMonitoringJob: Job? = null
  private var isStatusRegistered = false

  // 任务管理相关
  private var currentTaskId: String? = null

  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)

    // 初始化 ModelServiceClient
    modelServiceClient = ModelServiceClient(this)

    // 监听连接状态
    activityScope.launch {
      modelServiceClient.connectionState.collect { connected ->
        isServiceConnected = connected
        updateUI()
        if (connected) {
          appendLog("✅ 服务连接成功")
        } else {
          appendLog("❌ 服务连接断开")
        }
      }
    }

    createLayout()
  }

  private fun createLayout() {
    scrollView = ScrollView(this)

    val layout = LinearLayout(this).apply {
      orientation = LinearLayout.VERTICAL
      setPadding(32, 32, 32, 32)
    }

    // 标题
    val tvTitle = TextView(this).apply {
      text = "离线模型服务调试"
      textSize = 20f
      setPadding(0, 0, 0, 32)
    }

    // 连接控制
    val connectionLayout = LinearLayout(this).apply {
      orientation = LinearLayout.HORIZONTAL
    }

    btnConnect = Button(this).apply {
      text = "连接服务"
      setOnClickListener { connectToService() }
    }

    btnDisconnect = Button(this).apply {
      text = "断开连接"
      isEnabled = false
      setOnClickListener { disconnectFromService() }
    }

    btnCheckSupport = Button(this).apply {
      text = "检查硬件支持"
      isEnabled = false
      setOnClickListener { checkHardwareSupport() }
    }

    connectionLayout.addView(btnConnect)
    connectionLayout.addView(btnDisconnect)
    connectionLayout.addView(btnCheckSupport)

    // 状态控制按钮
    val statusLayout = LinearLayout(this).apply {
      orientation = LinearLayout.HORIZONTAL
    }

    btnGetStatus = Button(this).apply {
      text = "获取状态"
      isEnabled = false
      setOnClickListener { getCurrentStatus() }
    }

    btnRegisterStatus = Button(this).apply {
      text = "注册状态监听"
      isEnabled = false
      setOnClickListener { registerStatusMonitoring() }
    }

    btnUnregisterStatus = Button(this).apply {
      text = "取消状态监听"
      isEnabled = false
      setOnClickListener { unregisterStatusMonitoring() }
    }

    statusLayout.addView(btnGetStatus)
    statusLayout.addView(btnRegisterStatus)
    statusLayout.addView(btnUnregisterStatus)

    // 功能按钮
    val buttonLayout = LinearLayout(this).apply {
      orientation = LinearLayout.HORIZONTAL
    }

    btnExtractAbstract = Button(this).apply {
      text = "提取摘要"
      isEnabled = false
      setOnClickListener { extractAbstract() }
    }

    btnDetectActionItem = Button(this).apply {
      text = "检测待办事项"
      isEnabled = false
      setOnClickListener { detectActionItem() }
    }

    btnDetectMeetingType = Button(this).apply {
      text = "检测会议类型"
      isEnabled = false
      setOnClickListener { detectMeetingType() }
    }

    btnQA = Button(this).apply {
      text = "QA问答"
      isEnabled = false
      setOnClickListener { detectQA() }
    }

    btnRoleSummary = Button(this).apply {
      text = "说话人总结"
      isEnabled = false
      setOnClickListener { detectRoleSummary() }
    }

    buttonLayout.addView(btnExtractAbstract)
    buttonLayout.addView(btnDetectActionItem)
    buttonLayout.addView(btnDetectMeetingType)
    buttonLayout.addView(btnQA)
    buttonLayout.addView(btnRoleSummary)

    // 任务管理按钮
    val taskLayout = LinearLayout(this).apply {
      orientation = LinearLayout.HORIZONTAL
    }

    btnQueryTask = Button(this).apply {
      text = "查询任务"
      isEnabled = false
      setOnClickListener { queryCurrentTask() }
    }

    btnCancelTask = Button(this).apply {
      text = "取消任务"
      isEnabled = false
      setOnClickListener { cancelCurrentTask() }
    }

    taskLayout.addView(btnQueryTask)
    taskLayout.addView(btnCancelTask)

    // 结果显示
    val tvResultLabel = TextView(this).apply {
      text = "结果:"
      setPadding(0, 32, 0, 8)
    }

    tvResult = TextView(this).apply {
      text = "结果将显示在这里"
      setPadding(16, 16, 16, 16)
      setBackgroundColor(0xFFF0F0F0.toInt())
      minLines = 3
    }

    // 状态显示
    val tvStatusLabel = TextView(this).apply {
      text = "服务状态:"
      setPadding(0, 32, 0, 8)
    }

    tvStatus = TextView(this).apply {
      text = "状态信息将显示在这里"
      setPadding(16, 16, 16, 16)
      setBackgroundColor(0xFFE8F0FF.toInt())
      minLines = 4
    }

    // 日志显示
    val tvLogsLabel = TextView(this).apply {
      text = "调试日志:"
      setPadding(0, 32, 0, 8)
    }

    tvLogs = TextView(this).apply {
      text = "日志将显示在这里"
      setPadding(16, 16, 16, 16)
      setBackgroundColor(0xFFE8F5E8.toInt())
      minLines = 5
    }

    // 添加所有视图
    layout.addView(tvTitle)
    layout.addView(connectionLayout)
    layout.addView(statusLayout)
    layout.addView(buttonLayout)
    layout.addView(taskLayout)
    layout.addView(tvResultLabel)
    layout.addView(tvResult)
    layout.addView(tvStatusLabel)
    layout.addView(tvStatus)
    layout.addView(tvLogsLabel)
    layout.addView(tvLogs)

    scrollView.addView(layout)
    setContentView(scrollView)

    appendLog("🚀 界面初始化完成")
  }

  private fun connectToService() {
    appendLog("🔄 开始连接模型服务...")

    activityScope.launch {
      try {
        val connected = modelServiceClient.connectAndWait()
        if (connected) {
          appendLog("📡 服务连接成功")
        } else {
          appendLog("❌ 服务连接失败")
        }
      } catch (e: Exception) {
        appendLog("❌ 连接服务异常: ${e.message}")
      }
    }
  }

  private fun disconnectFromService() {
    try {
      modelServiceClient.disconnect()
      appendLog("🔌 服务连接已断开")
    } catch (e: Exception) {
      appendLog("❌ 断开服务连接失败: ${e.message}")
    }
  }

  private fun updateUI() {
    runOnUiThread {
      btnConnect.isEnabled = !isServiceConnected
      btnDisconnect.isEnabled = isServiceConnected
      btnCheckSupport.isEnabled = isServiceConnected
      btnExtractAbstract.isEnabled = isServiceConnected
      btnDetectActionItem.isEnabled = isServiceConnected
      btnDetectMeetingType.isEnabled = isServiceConnected
      btnQA.isEnabled = isServiceConnected
      btnRoleSummary.isEnabled = isServiceConnected

      btnGetStatus.isEnabled = isServiceConnected
      btnRegisterStatus.isEnabled = isServiceConnected && !isStatusRegistered
      btnUnregisterStatus.isEnabled = isServiceConnected && isStatusRegistered
      btnQueryTask.isEnabled = isServiceConnected && currentTaskId != null
      btnCancelTask.isEnabled = isServiceConnected && currentTaskId != null
    }
  }

  private fun checkHardwareSupport() {
    appendLog("🔍 检查 APUSys 硬件支持...")
    tvResult.text = "正在检查硬件支持..."

    activityScope.launch {
      try {
        val supported = modelServiceClient.isApuSysSupported()
        val resultText = if (supported) {
          "✅ 设备支持 APUSys 硬件"
        } else {
          "❌ 设备不支持 APUSys 硬件"
        }

        runOnUiThread {
          tvResult.text = resultText
        }
        appendLog("🔍 硬件支持检查结果: $supported")
      } catch (e: Exception) {
        val errorText = "硬件支持检查失败: ${e.message}"
        runOnUiThread {
          tvResult.text = errorText
        }
        appendLog("❌ 硬件支持检查异常: ${e.message}")
      }
    }
  }

  private fun extractAbstract() {
    val inputText = Constant.data_abstract
    if (inputText.isEmpty()) {
      appendLog("⚠️ 请输入文本")
      return
    }

    appendLog("📝 开始提取摘要，文本长度: ${inputText.length}")
    tvResult.text = "正在提取摘要..."

    activityScope.launch {
      try {
        appendLog("🔄 提交摘要提取任务...")
        val result = modelServiceClient.extractAbstractAsync(inputText)
        when (result) {
          is ModelServiceClient.TaskResult.Success -> {
            val displayResult = "摘要结果:\n${result.data}"
            runOnUiThread {
              tvResult.text = displayResult
            }
            Log.d(TAG, "extractAbstract-result: $result")
            appendLog("✅ 摘要提取成功: ${result.data.take(100)}${if (result.data.length > 100) "..." else ""}")
            // 任务完成，清除 taskId
            currentTaskId = null
            updateUI()
          }
          is ModelServiceClient.TaskResult.Error -> {
            val displayResult = "摘要提取失败:\n错误码: ${result.errorCode}\n错误信息: ${result.errorMessage}"
            runOnUiThread {
              tvResult.text = displayResult
            }
            appendLog("❌ 摘要提取失败: ${result.errorCode} - ${result.errorMessage}")
            // 任务失败，清除 taskId
            currentTaskId = null
            updateUI()
          }
        }
      } catch (e: Exception) {
        val errorText = "提取摘要异常: ${e.message}"
        runOnUiThread {
          tvResult.text = errorText
        }
        appendLog("❌ 提取摘要异常: ${e.message}")
        AILog.e(TAG, "Extract abstract error", e)
        // 异常时清除 taskId
        currentTaskId = null
        updateUI()
      }
    }
  }

  private fun detectActionItem() {
    val inputText = Constant.data_abstract
    if (inputText.isEmpty()) {
      appendLog("⚠️ 请输入文本")
      return
    }

    appendLog("🔍 开始检测待办事项，文本长度: ${inputText.length}")
    tvResult.text = "正在检测待办事项..."

    activityScope.launch {
      try {
        appendLog("🔄 提交待办事项检测任务...")
        val result = modelServiceClient.detectActionItemAsync(inputText)
        when (result) {
          is ModelServiceClient.TaskResult.Success -> {
            val displayResult = "待办事项结果:\n${result.data}"
            runOnUiThread {
              tvResult.text = displayResult
            }
            appendLog("✅ 待办事项检测成功: ${result.data.take(100)}${if (result.data.length > 100) "..." else ""}")
            // 任务完成，清除 taskId
            currentTaskId = null
            updateUI()
          }
          is ModelServiceClient.TaskResult.Error -> {
            val displayResult = "待办事项检测失败:\n错误码: ${result.errorCode}\n错误信息: ${result.errorMessage}"
            runOnUiThread {
              tvResult.text = displayResult
            }
            appendLog("❌ 待办事项检测失败: ${result.errorCode} - ${result.errorMessage}")
            // 任务失败，清除 taskId
            currentTaskId = null
            updateUI()
          }
        }
      } catch (e: Exception) {
        val errorText = "检测待办事项异常: ${e.message}"
        runOnUiThread {
          tvResult.text = errorText
        }
        appendLog("❌ 检测待办事项异常: ${e.message}")
        AILog.e(TAG, "Detect action item error", e)
        // 异常时清除 taskId
        currentTaskId = null
        updateUI()
      }
    }
  }

  private fun detectMeetingType() {
    val inputText = Constant.data_abstract
    if (inputText.isEmpty()) {
      appendLog("⚠️ 请输入文本")
      return
    }

    appendLog("🔍 开始检测会议类型，文本长度: ${inputText.length}")
    tvResult.text = "正在检测会议类型..."

    activityScope.launch {
      try {
        appendLog("🔄 提交会议类型检测任务...")
        //TODO
        val result = modelServiceClient.extractSceneRecognitionAsync(inputText)
        when (result) {
          is ModelServiceClient.TaskResult.Success -> {
            val displayResult = "会议类型检测结果:\n${result.data}"
            runOnUiThread {
              tvResult.text = displayResult
            }
            appendLog("✅ 会议类型检测成功: ${result.data.take(100)}${if (result.data.length > 100) "..." else ""}")
            // 任务完成，清除 taskId
            currentTaskId = null
            updateUI()
          }
          is ModelServiceClient.TaskResult.Error -> {
            val displayResult = "会议类型检测失败:\n错误码: ${result.errorCode}\n错误信息: ${result.errorMessage}"
            runOnUiThread {
              tvResult.text = displayResult
            }
            appendLog("❌ 会议类型检测失败: ${result.errorCode} - ${result.errorMessage}")
            // 任务失败，清除 taskId
            currentTaskId = null
            updateUI()
          }
        }
      } catch (e: Exception) {
        val errorText = "检测会议类型异常: ${e.message}"
        runOnUiThread {
          tvResult.text = errorText
        }
        appendLog("❌ 检测会议类型异常: ${e.message}")
        AILog.e(TAG, "Detect meeting type error", e)
        // 异常时清除 taskId
        currentTaskId = null
        updateUI()
      }
    }
  }

  private fun detectQA() {
    val inputText = Constant.data_abstract
    if (inputText.isEmpty()) {
      appendLog("⚠️ 请输入文本")
      return
    }

    appendLog("🔍 开始检测QA问答，文本长度: ${inputText.length}")
    tvResult.text = "正在检测QA问答..."

    activityScope.launch {
      try {
        appendLog("🔄 提交QA问答检测任务...")
        //TODO
        val result = modelServiceClient.extractQaAsync(inputText)
        when (result) {
          is ModelServiceClient.TaskResult.Success -> {
            val displayResult = "QA问答检测结果:\n${result.data}"
            runOnUiThread {
              tvResult.text = displayResult
            }
            appendLog("✅ QA问答检测成功: ${result.data.take(100)}${if (result.data.length > 100) "..." else ""}")
            // 任务完成，清除 taskId
            currentTaskId = null
            updateUI()
          }
          is ModelServiceClient.TaskResult.Error -> {
            val displayResult = "QA问答检测失败:\n错误码: ${result.errorCode}\n错误信息: ${result.errorMessage}"
            runOnUiThread {
              tvResult.text = displayResult
            }
            appendLog("❌ QA问答检测失败: ${result.errorCode} - ${result.errorMessage}")
            // 任务失败，清除 taskId
            currentTaskId = null
            updateUI()
          }
        }
      } catch (e: Exception) {
        val errorText = "QA问答异常: ${e.message}"
        runOnUiThread {
          tvResult.text = errorText
        }
        appendLog("❌ QA问答异常: ${e.message}")
        AILog.e(TAG, "Detect qa error", e)
        // 异常时清除 taskId
        currentTaskId = null
        updateUI()
      }
    }
  }


  private fun detectRoleSummary() {
    val inputText = Constant.data_abstract
    if (inputText.isEmpty()) {
      appendLog("⚠️ 请输入文本")
      return
    }

    appendLog("🔍 开始说话人总结，文本长度: ${inputText.length}")
    tvResult.text = "正在说话人总结..."

    activityScope.launch {
      try {
        appendLog("🔄 提交说话人总结任务...")
        //TODO
        val result = modelServiceClient.extractRoleSummarizeFlow(inputText)
        when (result) {
          is ModelServiceClient.TaskResult.Success -> {
            val displayResult = "说话人总结结果:\n${result.data}"
            runOnUiThread {
              tvResult.text = displayResult
            }
            appendLog("✅ 说话人总结成功: ${result.data.take(100)}${if (result.data.length > 100) "..." else ""}")
            // 任务完成，清除 taskId
            currentTaskId = null
            updateUI()
          }
          is ModelServiceClient.TaskResult.Error -> {
            val displayResult = "说话人总结失败:\n错误码: ${result.errorCode}\n错误信息: ${result.errorMessage}"
            runOnUiThread {
              tvResult.text = displayResult
            }
            appendLog("❌ 说话人总结失败: ${result.errorCode} - ${result.errorMessage}")
            // 任务失败，清除 taskId
            currentTaskId = null
            updateUI()
          }
        }
      } catch (e: Exception) {
        val errorText = "说话人总结异常: ${e.message}"
        runOnUiThread {
          tvResult.text = errorText
        }
        appendLog("❌ 说话人总结异常: ${e.message}")
        AILog.e(TAG, "Detect role summary error", e)
        // 异常时清除 taskId
        currentTaskId = null
        updateUI()
      }
    }
  }


  // ============ 状态管理功能 ============

  private fun getCurrentStatus() {
    appendLog("📊 获取当前服务状态...")
    tvStatus.text = "正在获取状态..."

    activityScope.launch {
      try {
        val status = modelServiceClient.getCurrentStatus()
        if (status != null) {
          val statusText = "🔍 服务状态详情:\n" +
            "主状态: ${status.getReadableStatus()}\n" +
            "授权状态: ${if (status.isAuthorized) "✅ 已授权" else "❌ 未授权"}\n" +
            "初始化: ${if (status.isInitialized) "✅ 已完成" else "❌ 未完成"}\n" +
            "服务支持: ${if (status.isServiceSupported) "✅ 支持" else "❌ 不支持"}\n" +
            "任务管理器: ${if (status.isTaskManagerRunning) "✅ 运行中" else "❌ 未运行"}\n" +
            "活跃任务数: ${status.activeTaskCount}\n" +
            "时间戳: ${java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(status.timestamp))}\n" +
            "${status.lastError?.let { "错误信息: $it\n" } ?: ""}" +
            "\n综合状态:\n" +
            "健康状态: ${if (status.isHealthy()) "✅ 健康" else "❌ 不健康"}\n" +
            "就绪状态: ${if (status.isReady()) "✅ 就绪" else "❌ 未就绪"}\n" +
            "错误状态: ${if (status.hasError()) "❌ 有错误" else "✅ 无错误"}"

          runOnUiThread {
            tvStatus.text = statusText
          }
          appendLog("✅ 状态获取成功: ${status.getReadableStatus()}")
        } else {
          runOnUiThread {
            tvStatus.text = "❌ 无法获取状态信息"
          }
          appendLog("❌ 状态获取失败: 返回null")
        }
      } catch (e: Exception) {
        val errorText = "❌ 获取状态异常: ${e.message}"
        runOnUiThread {
          tvStatus.text = errorText
        }
        appendLog("❌ 获取状态异常: ${e.message}")
      }
    }
  }

  private fun registerStatusMonitoring() {
    if (isStatusRegistered) {
      appendLog("⚠️ 状态监听已注册")
      return
    }

    appendLog("📡 注册状态监听...")

    try {
      // 使用 createStatusFlow 监听状态变化
      statusMonitoringJob = activityScope.launch {
        modelServiceClient.createStatusFlow("test_activity").collect { status ->
          val statusText = "🔄 状态更新 [${java.text.SimpleDateFormat("HH:mm:ss.SSS", java.util.Locale.getDefault()).format(java.util.Date())}]\n" +
            "主状态: ${status.getReadableStatus()}\n" +
            "授权: ${if (status.isAuthorized) "✅" else "❌"} | 初始化: ${if (status.isInitialized) "✅" else "❌"}\n" +
            "服务支持: ${if (status.isServiceSupported) "✅" else "❌"} | 任务管理器: ${if (status.isTaskManagerRunning) "✅" else "❌"}\n" +
            "活跃任务: ${status.activeTaskCount}\n" +
            "${status.lastError?.let { "错误: $it\n" } ?: ""}" +
            "健康: ${if (status.isHealthy()) "✅" else "❌"} | 就绪: ${if (status.isReady()) "✅" else "❌"}"

          runOnUiThread {
            tvStatus.text = statusText
          }

          // 打印状态更新到日志
          appendLog("🔄 状态更新: ${status.getReadableStatus()} (任务:${status.activeTaskCount})")
        }
      }

      isStatusRegistered = true
      updateUI()
      appendLog("✅ 状态监听注册成功")
    } catch (e: Exception) {
      appendLog("❌ 状态监听注册失败: ${e.message}")
    }
  }

  private fun unregisterStatusMonitoring() {
    if (!isStatusRegistered) {
      appendLog("⚠️ 状态监听未注册")
      return
    }

    appendLog("🔌 取消状态监听...")

    try {
      statusMonitoringJob?.cancel()
      statusMonitoringJob = null
      isStatusRegistered = false
      updateUI()

      runOnUiThread {
        tvStatus.text = "状态监听已取消"
      }
      appendLog("✅ 状态监听已取消")
    } catch (e: Exception) {
      appendLog("❌ 取消状态监听失败: ${e.message}")
    }
  }

  // ============ 任务管理功能 ============

  private fun queryCurrentTask() {
    val taskId = currentTaskId
    if (taskId == null) {
      appendLog("⚠️ 没有当前任务可查询")
      return
    }

    appendLog("🔍 查询任务状态: $taskId")

    activityScope.launch {
      try {
        val taskState = modelServiceClient.getTaskState(taskId)
        if (taskState != null) {
          val statusText = "📊 任务状态详情:\n" +
            "任务ID: ${taskState.taskId}\n" +
            "任务类型: ${taskState.getReadableTaskType()}\n" +
            "当前状态: ${taskState.getReadableStatus()}\n" +
            "${taskState.result?.let { "结果: ${it.take(200)}${if (it.length > 200) "..." else ""}\n" } ?: ""}" +
            "${taskState.errorMessage?.let { "错误信息: $it\n" } ?: ""}" +
            "\n状态检查:\n" +
            "已完成: ${if (taskState.isCompleted()) "✅" else "❌"}\n" +
            "执行中: ${if (taskState.isExecuting()) "✅" else "❌"}\n" +
            "队列中: ${if (taskState.isQueued()) "✅" else "❌"}\n" +
            "已取消: ${if (taskState.isCancelled()) "✅" else "❌"}\n" +
            "已失败: ${if (taskState.isFailed()) "✅" else "❌"}"

          runOnUiThread {
            tvResult.text = statusText
          }
          appendLog("✅ 任务状态查询成功: ${taskState.getReadableStatus()}")

          // 如果任务已完成或失败，清除 taskId
          if (taskState.isCompleted() || taskState.isFailed() || taskState.isCancelled()) {
            currentTaskId = null
            updateUI()
          }
        } else {
          runOnUiThread {
            tvResult.text = "❌ 任务不存在或已过期"
          }
          appendLog("❌ 任务查询失败: 任务不存在")
          currentTaskId = null
          updateUI()
        }
      } catch (e: Exception) {
        val errorText = "❌ 查询任务状态异常: ${e.message}"
        runOnUiThread {
          tvResult.text = errorText
        }
        appendLog("❌ 查询任务状态异常: ${e.message}")
        AILog.e(TAG, "Query task error", e)
      }
    }
  }

  private fun cancelCurrentTask() {
    val taskId = currentTaskId
    if (taskId == null) {
      appendLog("⚠️ 没有当前任务可取消")
      return
    }

    appendLog("🚫 取消任务: $taskId")

    activityScope.launch {
      try {
        val success = modelServiceClient.cancelTask(taskId)
        if (success) {
          runOnUiThread {
            tvResult.text = "✅ 任务已成功取消"
          }
          appendLog("✅ 任务取消成功")
          currentTaskId = null
          updateUI()
        } else {
          runOnUiThread {
            tvResult.text = "❌ 任务取消失败"
          }
          appendLog("❌ 任务取消失败")
        }
      } catch (e: Exception) {
        val errorText = "❌ 取消任务异常: ${e.message}"
        runOnUiThread {
          tvResult.text = errorText
        }
        appendLog("❌ 取消任务异常: ${e.message}")
        AILog.e(TAG, "Cancel task error", e)
      }
    }
  }

  private fun appendLog(message: String) {
    runOnUiThread {
      val timestamp = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())
      val logMessage = "[$timestamp] $message"

      val currentLogs = tvLogs.text.toString()
      val newLogs = if (currentLogs == "日志将显示在这里") {
        logMessage
      } else {
        "$logMessage\n$currentLogs"
      }

      // 限制日志行数
      val lines = newLogs.split("\n")
      val limitedLogs = if (lines.size > 20) {
        lines.take(20).joinToString("\n")
      } else {
        newLogs
      }

      tvLogs.text = limitedLogs
    }

    // 同时输出到系统日志
    AILog.i(TAG, message)
  }

  override fun onDestroy() {
    super.onDestroy()

    // 取消状态监听
    if (isStatusRegistered) {
      unregisterStatusMonitoring()
    }

    disconnectFromService()
    activityScope.cancel()
    appendLog("🏁 Activity 销毁")
  }
}
