package com.aispeech.modellibs.di

import com.aispeech.modellibs.dispatcher.ITaskDispatcher
import com.aispeech.modellibs.engine.IModelEngine
import com.aispeech.modellibs.status.IServiceStatusManager
import com.aispeech.modellibs.task.ITaskManager

interface IDependencyContainer {
  fun isInitialized(): Boolean
  fun getModelEngine(): IModelEngine
  fun getTaskManager(): ITaskManager
  fun getTaskDispatcher(): ITaskDispatcher
  fun getServiceStatusManager(): IServiceStatusManager
  suspend fun initialize(): Boolean
  fun shutdown()
}
