package com.aispeech.modellibs.di

import android.app.Application
import com.aispeech.aibase.AILog
import com.aispeech.modellibs.cleanup.TaskCleanupScheduler
import com.aispeech.modellibs.database.TaskDao
import com.aispeech.modellibs.database.TaskDatabase
import com.aispeech.modellibs.dispatcher.ITaskDispatcher
import com.aispeech.modellibs.dispatcher.TaskDispatcher
import com.aispeech.modellibs.engine.IModelEngine
import com.aispeech.modellibs.engine.LocalModelEngine
import com.aispeech.modellibs.engine.OnDemandLLMEngineManager
import com.aispeech.modellibs.monitor.MemoryMonitorManager
import com.aispeech.modellibs.status.IServiceStatusManager
import com.aispeech.modellibs.status.ServiceStatusManagerImpl
import com.aispeech.modellibs.task.DatabaseTaskManager
import com.aispeech.modellibs.task.ITaskManager
import com.aispeech.modellibs.task.TaskFileManager
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * 简化的依赖注入容器
 * 负责创建和管理业务组件，不包含授权逻辑
 */
class DependencyContainerImpl(
  private val context: Application,
  private val serviceStatusManager: IServiceStatusManager = ServiceStatusManagerImpl()
): IDependencyContainer {

  companion object {
    private const val TAG = "DependencyContainer"
  }

  // 初始化状态管理
  private val initializationMutex = Mutex()
  @Volatile
  private var _isInitialized = false

  // 核心组件
  private var modelEngine: IModelEngine? = null
  private var taskManager: ITaskManager? = null
  private var taskDispatcher: ITaskDispatcher? = null

  // 数据库相关组件
  private var taskDatabase: TaskDatabase? = null
  private var taskDao: TaskDao? = null
  private var taskFileManager: TaskFileManager? = null

  // 内存监控管理器
  private var memoryMonitorManager: MemoryMonitorManager? = null

  init {
    memoryMonitorManager = MemoryMonitorManager(context)
    memoryMonitorManager?.initialize()
    memoryMonitorManager?.startMonitoring()
  }

  /**
   * 公共初始化方法
   */
  override suspend fun initialize(): Boolean {
    return initializationMutex.withLock {
      if (_isInitialized) {
        AILog.i(TAG, "DependencyContainer already initialized")
        return@withLock true
      }

      try {
        AILog.i(TAG, "Starting business components initialization...")

        if (!createComponents()) {
          AILog.e(TAG, "Component creation failed")
          return@withLock false
        }

        _isInitialized = true
        AILog.i(TAG, "Business components initialization completed successfully")
        true

      } catch (e: Exception) {
        AILog.e(TAG, "Business components initialization error", e)
        cleanup()
        false
      }
    }
  }

  /**
   * 创建所有组件
   */
  private fun createComponents(): Boolean {
    return try {
      // 初始化数据库
      val database = TaskDatabase.getInstance(context)
      taskDatabase = database
      taskDao = database.taskDao()

      // 创建文件管理器
      taskFileManager = TaskFileManager(context)

      // 创建并初始化模型引擎
      val engine = LocalModelEngine()
      if (!engine.initialize(context)) {
        AILog.e(TAG, "Failed to initialize model engine")
        return false
      }
      modelEngine = engine

      // 创建LLM引擎管理器
      val engineManager = OnDemandLLMEngineManager()

      // 创建基于数据库的任务管理器
      taskManager = DatabaseTaskManager(
        taskDao = taskDao!!,
        fileManager = taskFileManager!!,
        modelEngine = engine,
        engineManager = engineManager
      )

      // 创建任务调度器
      taskDispatcher = TaskDispatcher(taskDao!!, taskFileManager!!)

      // 设置任务管理器到状态管理器
      serviceStatusManager.setTaskManager(taskManager!!)

      try {
        TaskCleanupScheduler.scheduleCleanupTask(context)
        AILog.i(TAG, "Task cleanup scheduler initialized successfully")
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to initialize task cleanup scheduler", e)
      }

      AILog.i(TAG, "All components created successfully")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Component creation failed", e)
      false
    }
  }

  /**
   * 设置初始化完成状态
   * 用于在完整初始化流程结束后设置最终状态
   */
  fun setInitializationCompleted() {
    try {
      serviceStatusManager.setInitialized()
      AILog.i(TAG, "Initialization completed status set")
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to set initialization completed status", e)
    }
  }

  // 公共访问方法
  override fun isInitialized(): Boolean = _isInitialized

  override fun getModelEngine(): IModelEngine {
    return modelEngine ?: error("Container not initialized")
  }

  override fun getTaskManager(): ITaskManager {
    return taskManager ?: error("Container not initialized")
  }

  override fun getTaskDispatcher(): ITaskDispatcher {
    return taskDispatcher ?: error("Container not initialized")
  }

  override fun getServiceStatusManager(): IServiceStatusManager {
    return serviceStatusManager
  }

  /**
   * 清理资源
   */
  private fun cleanup() {
    try {
      memoryMonitorManager?.shutdown()
      taskDispatcher?.shutdown()
      taskManager?.shutdown()
      modelEngine?.release()
      serviceStatusManager.shutdown()

      // 清理数据库相关组件
      taskDatabase?.close()

      memoryMonitorManager = null
      taskDispatcher = null
      taskManager = null
      modelEngine = null
      taskDatabase = null
      taskDao = null
      taskFileManager = null
      _isInitialized = false

      AILog.i(TAG, "Resources cleaned up")
    } catch (e: Exception) {
      AILog.e(TAG, "Error during cleanup", e)
    }
  }

  /**
   * 销毁容器
   */
  fun destroy() {
    cleanup()
  }

  override fun shutdown() {
    AILog.i(TAG, "Shutting down dependency container...")
    destroy()
    AILog.i(TAG, "Dependency container shutdown completed")
  }
}
