package com.aispeech.modellibs

import android.annotation.SuppressLint
import android.app.Application
import android.os.Build
import android.os.Environment
import android.provider.Settings
import com.aispeech.aibase.AILog
import com.aispeech.aibase.logger.LogLevel
import com.aispeech.modellibs.auth.AuthorizationManager
import com.aispeech.modellibs.di.DependencyContainerImpl
import com.aispeech.modellibs.di.IDependencyContainer
import com.aispeech.modellibs.status.ServiceStatusManagerImpl
import com.aispeech.tablet.core.common.DeviceUtils
import com.blankj.utilcode.util.AppUtils
import com.blankj.utilcode.util.PathUtils
import com.tencent.bugly.crashreport.CrashReport
import com.tencent.mmkv.MMKV
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.io.File
import kotlin.system.measureTimeMillis

class OfflineModelApplication : Application() {

  companion object {
    const val TAG = "OfflineModelApplication"
  }

  // 授权管理
  private lateinit var authorizationManager: AuthorizationManager

  // 状态管理
  private val serviceStatusManager = ServiceStatusManagerImpl()

  // 依赖注入
  private lateinit var dependencyContainer: IDependencyContainer

  // scope
  private val applicationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

  override fun onCreate() {
    super.onCreate()
    ConfigurationManager.initialize(this)

    measureTimeMillis {
      initAiLog()
      MMKV.initialize(this)
      configCrashReport()
      initDependencyContainer()
      initAuthorizationManager()
      startInitializationProcess()
    }.also {
      AILog.i(TAG,"====OfflineModelApp onCreate, cost: ${it}, version: ${AppUtils.getAppVersionName()}, ${AppUtils.getAppVersionCode()}====")
    }
  }

  /**
   * 初始化依赖注入容器
   */
  private fun initDependencyContainer() {
    dependencyContainer = DependencyContainerImpl(this, serviceStatusManager)
    AILog.i(TAG, "DependencyContainer created")
  }

  /**
   * 初始化授权管理器
   */
  private fun initAuthorizationManager() {
    authorizationManager = AuthorizationManager.getInstance(this)
    AILog.i(TAG, "AuthorizationManager created")
  }

  /**
   * 启动初始化流程
   */
  private fun startInitializationProcess() {
    applicationScope.launch {
      // 这里先不拦截了，不然后续的在 adb 也不管用
//      if (!checkApuSupport()) {
//        AILog.w(TAG, "APU not supported, skipping initialization")
//        serviceStatusManager.setError("设备不支持APU硬件")
//        return@launch
//      }

      val deviceIdentifier = getSafeDeviceIdentifier()
      val success = authorizationManager.executeFullInitialization(deviceIdentifier, dependencyContainer)

      if (success) {
        AILog.i(TAG, "Full initialization completed successfully")
      } else {
        AILog.w(TAG, "Full initialization failed, retry mechanism will handle it")
      }
    }
  }

  /**
   * 检查设备是否支持 APU 硬件
   * 这是必要的功能检查，确保设备具备运行离线模型的硬件能力
   */
  private fun checkApuSupport(): Boolean {
    return CapabilityUtils.isApuSysSupported()
  }


  @SuppressLint("HardwareIds")
  fun getSafeDeviceIdentifier(): String {
    return try {
      Build.getSerial()
    } catch (e: Exception) {
      Settings.Secure.getString(contentResolver, Settings.Secure.ANDROID_ID)
        ?: "UNKNOWN-${System.currentTimeMillis()}"
    }
  }

  /**
   * 获取依赖注入容器
   */
  fun getDependencyContainer(): IDependencyContainer {
    return dependencyContainer
  }

  /**
   * 获取授权管理器实例
   */
  fun getAuthorizationManager(): AuthorizationManager? {
    return if (::authorizationManager.isInitialized) authorizationManager else null
  }

  override fun onTerminate() {
    super.onTerminate()
    AILog.i(TAG, "Authorization system terminated")
  }

  /**
   * 配置bugly
   */
  private fun configCrashReport() {
    CrashReport.initCrashReport(
      this,
      if (BuildConfig.DEBUG) "76b40b3399" else "ad32085ea8",
      false
    )

    CrashReport.setUserId(DeviceUtils.getSN())
  }

  /**
   * 初始化 AILOG
   */
  private fun initAiLog() {
    var parentPath = PathUtils.getExternalStoragePath()
    if (parentPath.length < 2) {
      parentPath = Environment.getExternalStorageDirectory().absolutePath
    }

    var logLevel = LogLevel.RELEASE
    if (BuildConfig.DEBUG) {
      logLevel = LogLevel.FULL
    }

    AILog.Init(
      this, logLevel, ConfigurationManager.getMaxLogSize(), BuildConfig.DEBUG,
      parentPath + File.separator + ConfigurationManager.getLogDirName() + File.separator,
      ConfigurationManager.getLogFilePrefix()
    )
    AILog.i(TAG, "onCreate begin")
  }
}