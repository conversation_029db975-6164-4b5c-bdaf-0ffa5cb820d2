package com.aispeech.modellibs

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.aispeech.DUILiteConfig
import com.aispeech.aibase.AILog
import com.aispeech.export.config.AuthConfig
import com.aispeech.export.config.MagnusRuntimeConfig
import com.aispeech.export.config.MagnusRuntimeEnvInfo
import com.aispeech.lite.AuthType

/**
 * Magnus Runtime 路径管理器
 * 提供简单的路径配置管理，支持持久化存储
 */
object MagnusRuntimePathManager {
  private const val TAG = "MagnusRuntimePathManager"
  private const val PREFS_NAME = "magnus_runtime_config"
  private const val KEY_MAGNUS_RUNTIME_PATH = "magnus_runtime_path"

  // 默认路径
  private const val DEFAULT_MAGNUS_RUNTIME_PATH =
    "/data/local/tmp/magnus_llmserver_debug_android_aarch64_mtk_npu_v0.0.1_bak"

  private var sharedPreferences: SharedPreferences? = null
  private var currentPath: String = DEFAULT_MAGNUS_RUNTIME_PATH

  /**
   * 初始化路径管理器
   * @param context 应用上下文
   */
  fun initialize(context: Context) {
    sharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    currentPath = sharedPreferences?.getString(KEY_MAGNUS_RUNTIME_PATH, DEFAULT_MAGNUS_RUNTIME_PATH)
      ?: DEFAULT_MAGNUS_RUNTIME_PATH
    AILog.i(TAG, "Magnus runtime path manager initialized with path: $currentPath")
  }

  /**
   * 获取当前 Magnus Runtime 路径
   */
  fun getCurrentPath(): String = currentPath

  /**
   * 更新 Magnus Runtime 路径
   * @param newPath 新的路径
   * @return true 如果更新成功，false 如果路径无效或更新失败
   */
  fun updatePath(newPath: String): Boolean {
    if (newPath.isBlank()) {
      AILog.i(TAG, "Invalid path: path cannot be blank")
      return false
    }

    return try {
      val oldPath = currentPath
      currentPath = newPath

      // 持久化存储
      sharedPreferences?.edit()?.putString(KEY_MAGNUS_RUNTIME_PATH, newPath)?.apply()

      AILog.i(TAG, "Magnus runtime path updated: $oldPath -> $newPath")
      true
    } catch (e: Exception) {
      Log.e(TAG, "Failed to update Magnus runtime path", e)
      false
    }
  }
}

/**
 * 配置管理器
 * 统一管理所有配置信息，支持配置的集中管理和动态更新
 */
object ConfigurationManager {


  // SDK 配置
  object SdkConfig {
    const val API_KEY = "9ea8675e019a9ea8675e019a686baf42"
    const val PRODUCT_ID = "279631050"
    const val PRODUCT_KEY = "16b66b1ee6bcd03054f44dfdf51bacea"
    const val PRODUCT_SECRET = "72574917e9f375fce9f85f02883210b5"
    const val AUTH_TIMEOUT = 5000
    const val AUTH_SERVER = "https://auth.duiopen.com"
    val AUTH_TYPE = AuthType.ONLINE
  }

  // 路径配置
  object PathConfig {

    /**
     * 获取当前 Magnus Runtime 路径（动态配置）
     */
    fun getMagnusRuntimePath(): String = MagnusRuntimePathManager.getCurrentPath()
  }

  // 超时配置
  object TimeoutConfig {
    const val TASK_PROCESSING_TIMEOUT = 240_000L // 240秒
    const val SERVICE_BIND_TIMEOUT = 10_000L // 10秒
  }

  // 日志配置
  object LogConfig {
    const val MAX_LOG_SIZE = 3 * 1024 * 1024 // 3MB
    const val JAVA_LOG_LEVEL = 2
    const val NATIVE_LOG_LEVEL = 2
    const val MAGNUS_LOG_LEVEL = Log.WARN
    const val LOG_DIR_NAME = "AISpeechLog"
    const val LOG_FILE_PREFIX = "modellibs"
  }

  // 任务配置
  object TaskConfig {
    const val MAX_QUEUE_SIZE = 100
  }

  /**
   * 创建 DUILite SDK 配置
   */
  fun createDUILiteConfig(deviceIdentifier: String): DUILiteConfig {
    return DUILiteConfig.Builder()
      .setApiKey(SdkConfig.API_KEY)
      .setProductId(SdkConfig.PRODUCT_ID)
      .setProductKey(SdkConfig.PRODUCT_KEY)
      .setProductSecret(SdkConfig.PRODUCT_SECRET)
      .setAuthConfig(
        AuthConfig.Builder()
          .setAuthTimeout(SdkConfig.AUTH_TIMEOUT)
          .setCustomDeviceName(deviceIdentifier)
          .setType(SdkConfig.AUTH_TYPE)
          .setAuthServer(SdkConfig.AUTH_SERVER)
          .create()
      )
      .create()
  }

  /**
   * 创建 Magnus Runtime 环境信息
   */
  fun createMagnusRuntimeEnvInfo(): MagnusRuntimeEnvInfo {
    return MagnusRuntimeEnvInfo.Builder()
      .setType(MagnusRuntimeEnvInfo.TYPE_CLIENT)
      .setLogLevel(LogConfig.MAGNUS_LOG_LEVEL)
      .create()
  }

  /**
   * 创建 Magnus Runtime 配置（支持自定义路径）
   * @param envInfo 环境信息
   * @param customPath 自定义路径，如果为null则使用当前配置的路径
   */
  fun createMagnusRuntimeConfig(envInfo: MagnusRuntimeEnvInfo, customPath: String? = null): MagnusRuntimeConfig {
    val path = customPath ?: PathConfig.getMagnusRuntimePath()
    return MagnusRuntimeConfig.Builder()
      .setPath(path)
      .setInfo(envInfo.toJson())
      .setServerType(true)
      .create()
  }

  /**
   * 获取任务处理超时时间
   */
  fun getTaskProcessingTimeout(): Long = TimeoutConfig.TASK_PROCESSING_TIMEOUT

  /**
   * 获取最大日志大小
   */
  fun getMaxLogSize(): Int = LogConfig.MAX_LOG_SIZE

  /**
   * 获取Java日志级别
   */
  fun getJavaLogLevel(): Int = LogConfig.JAVA_LOG_LEVEL

  /**
   * 获取Native日志级别
   */
  fun getNativeLogLevel(): Int = LogConfig.NATIVE_LOG_LEVEL

  /**
   * 获取日志目录名
   */
  fun getLogDirName(): String = LogConfig.LOG_DIR_NAME

  /**
   * 获取日志文件前缀
   */
  fun getLogFilePrefix(): String = LogConfig.LOG_FILE_PREFIX

  /**
   * 获取任务队列最大大小
   */
  fun getMaxQueueSize(): Int = TaskConfig.MAX_QUEUE_SIZE



  // ========== Magnus Runtime Path 管理 ==========

  /**
   * 初始化配置管理器
   * @param context 应用上下文
   */
  fun initialize(context: Context) {
    MagnusRuntimePathManager.initialize(context)
  }

  /**
   * 获取当前 Magnus Runtime 路径
   */
  fun getMagnusRuntimePath(): String = MagnusRuntimePathManager.getCurrentPath()


  /**
   * 更新 Magnus Runtime 路径
   * @param newPath 新的路径
   * @return true 如果更新成功，false 如果路径无效或更新失败
   */
  fun updateMagnusRuntimePath(newPath: String): Boolean {
    return MagnusRuntimePathManager.updatePath(newPath)
  }

}
