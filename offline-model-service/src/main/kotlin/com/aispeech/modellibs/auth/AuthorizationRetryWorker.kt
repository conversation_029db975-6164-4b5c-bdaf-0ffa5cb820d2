package com.aispeech.modellibs.auth

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.Data
import androidx.work.WorkerParameters
import com.aispeech.aibase.AILog
import com.aispeech.modellibs.OfflineModelApplication

/**
 * 授权重试 Worker
 *
 * 使用 WorkManager 实现授权重试逻辑
 */
class AuthorizationRetryWorker(
  context: Context,
  params: WorkerParameters
) : CoroutineWorker(context, params) {

  companion object {
    private const val TAG = "AuthorizationRetryWorker"

    // Worker 输入参数
    const val KEY_DEVICE_IDENTIFIER = "device_identifier"
    const val KEY_RETRY_REASON = "retry_reason"

    // 重试原因
    const val RETRY_REASON_INITIAL_FAILURE = "initial_failure"
    const val RETRY_REASON_MANUAL_RETRY = "manual_retry"

    /**
     * 创建输入数据
     */
    fun createInputData(
      deviceIdentifier: String,
      retryReason: String = RETRY_REASON_MANUAL_RETRY
    ): Data {
      return Data.Builder()
        .putString(KEY_DEVICE_IDENTIFIER, deviceIdentifier)
        .putString(KEY_RETRY_REASON, retryReason)
        .build()
    }
  }

  override suspend fun doWork(): Result {
    val deviceIdentifier = inputData.getString(KEY_DEVICE_IDENTIFIER)
    val retryReason = inputData.getString(KEY_RETRY_REASON) ?: RETRY_REASON_MANUAL_RETRY

    if (deviceIdentifier == null) {
      AILog.e(TAG, "Device identifier is null, cannot proceed with authorization")
      return Result.failure()
    }

    AILog.i(TAG, "Starting authorization retry, reason: $retryReason")

    return try {
      // 从Application获取组件
      val app = applicationContext as? OfflineModelApplication
      if (app == null) {
        AILog.e(TAG, "Application is not OfflineModelApplication")
        return Result.failure()
      }

      val authorizationManager = app.getAuthorizationManager()
      val dependencyContainer = app.getDependencyContainer()

      if (authorizationManager == null) {
        AILog.e(TAG, "AuthorizationManager is not available")
        return Result.failure()
      }

      // 如果 DependencyContainer 已经初始化，先设置重试状态
      if (dependencyContainer.isInitialized()) {
        try {
          val statusManager = dependencyContainer.getServiceStatusManager()
          statusManager.setAuthorizationRetrying(retryReason)
          AILog.i(TAG, "Set retry status on existing ServiceStatusManager")
        } catch (e: Exception) {
          AILog.w(TAG, "Failed to set retry status with exception: ${e.message}")
        }
      }

      // 重新开始完整的初始化流程
      val success =
        authorizationManager.executeFullInitialization(deviceIdentifier, dependencyContainer)

      if (success) {
        AILog.i(TAG, "Authorization retry successful")
        Result.success()
      } else {
        AILog.w(TAG, "Authorization retry failed")

        // 设置失败状态
        if (dependencyContainer.isInitialized()) {
          try {
            val statusManager = dependencyContainer.getServiceStatusManager()
            statusManager.setAuthorizationFailed("授权重试失败: $retryReason")
          } catch (e: Exception) {
            AILog.w(TAG, "Failed to set failure status with exception: ${e.message}")
          }
        }

        Result.retry()
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Authorization retry error", e)

      // 设置错误状态
      try {
        val app = applicationContext as? OfflineModelApplication
        val dependencyContainer = app?.getDependencyContainer()
        if (dependencyContainer?.isInitialized() == true) {
          val statusManager = dependencyContainer.getServiceStatusManager()
          statusManager.setError("授权重试异常: ${e.message}")
        }
      } catch (statusException: Exception) {
        AILog.w(TAG, "Failed to set error status with exception: $statusException")
      }

      Result.failure()
    }
  }
}
