package com.aispeech.modellibs.auth

import android.content.Context
import com.aispeech.DUILiteSDK
import com.aispeech.aibase.AILog
import com.aispeech.modellibs.ConfigurationManager
import com.aispeech.modellibs.status.IServiceStatusManager
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlin.coroutines.resume

class Authorizer(
  private val applicationContext: Context,
  private val statusManager: IServiceStatusManager? = null
) {

  companion object {
    private const val TAG = "Authorizer"
  }

  suspend fun authorize(deviceIdentifier: String): Boolean {
    AILog.i(TAG, "Executing authorization and initialization...")

    val authSuccess = performAuthorization(deviceIdentifier)
    if (!authSuccess) {
      AILog.w(TAG, "Authorization failed, skipping engine initialization.")
      return false
    }
    AILog.i(TAG, "Authorization successful")
    return true
  }

  /**
   * 执行授权操作
   */
  private suspend fun performAuthorization(deviceIdentifier: String): <PERSON><PERSON><PERSON> {
    return suspendCancellableCoroutine { continuation ->
      try {
        val config = ConfigurationManager.createDUILiteConfig(deviceIdentifier)

        // 确保 SDK 已初始化
        DUILiteSDK.init(applicationContext)
        DUILiteSDK.setJavaLiteLogLevel(ConfigurationManager.getJavaLogLevel())
        DUILiteSDK.setNativeLogLevel(ConfigurationManager.getNativeLogLevel())

        DUILiteSDK.doAuth(applicationContext, config, object : DUILiteSDK.InitListener {
          override fun success() {
            AILog.i(TAG, "授权成功!")

            // 设置授权成功状态
            statusManager?.setAuthorized()
            // 继续初始化其他组件
            try {
              continuation.resume(true)
            } catch (e: Exception) {
              AILog.e(TAG, "Engine initialization failed after successful auth", e)
              continuation.resume(false)
            }
          }

          override fun error(errorCode: String, errorInfo: String) {
            AILog.i(TAG, "授权失败, errorcode: $errorCode, errorInfo: $errorInfo")
            continuation.resume(false)
          }
        })
      } catch (e: Exception) {
        AILog.e(TAG, "Authorization setup failed", e)
        continuation.resume(false)
      }
    }
  }
}