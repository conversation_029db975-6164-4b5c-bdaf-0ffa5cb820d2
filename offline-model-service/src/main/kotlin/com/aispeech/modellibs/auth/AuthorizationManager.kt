  package com.aispeech.modellibs.auth

  import android.content.Context
import androidx.work.BackoffPolicy
import androidx.work.Constraints
import androidx.work.ExistingWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.WorkManager
import com.aispeech.aibase.AILog
import com.aispeech.modellibs.CoroutineScopeManager
import com.aispeech.modellibs.di.IDependencyContainer
import kotlinx.coroutines.CoroutineScope
import java.util.concurrent.TimeUnit

  /**
   * 授权管理器
   */
  class AuthorizationManager private constructor(
    private val appContext: Context
  ) : CoroutineScope {
    companion object {
      private const val TAG = "AuthorizationManager"

      // WorkManager 相关常量
      private const val WORK_NAME_AUTH_RETRY = "authorization_retry"
      private const val INITIAL_BACKOFF_DELAY_SECONDS = 10L

      @Volatile
      private var INSTANCE: AuthorizationManager? = null

      fun getInstance(context: Context): AuthorizationManager {
        return INSTANCE ?: synchronized(this) {
          INSTANCE ?: AuthorizationManager(context.applicationContext).also { INSTANCE = it }
        }
      }
    }

    // 协程作用域委托 - 使用结构化并发
    private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
      moduleName = TAG,
      parentScope = CoroutineScopeManager.getIOParentScope()
    )

    // 委托CoroutineScope接口到scopeDelegate
    override val coroutineContext = scopeDelegate.coroutineContext

    // 授权失败的任务走 worker
    private val workManager = WorkManager.getInstance(appContext)

    /**
     * 初始化流程
     */
    suspend fun executeFullInitialization(deviceIdentifier: String, dependencyContainer: IDependencyContainer): Boolean {
      AILog.i(TAG, "Starting full initialization process...")

      // 获取状态管理器（现在可以直接获取，因为在构造函数中创建）
      val statusManager = dependencyContainer.getServiceStatusManager()
      val authorizerWithStatus = Authorizer(appContext, statusManager)

      // DUILite授权
      statusManager.setAuthorizationRetrying("开始授权")
      val authSuccess = authorizerWithStatus.authorize(deviceIdentifier)
      if (!authSuccess) {
        AILog.e(TAG, "DUILite authorization failed")
        statusManager.setAuthorizationFailed("DUILite授权失败")

        // 触发自动重试
        enqueueRetryWorker(deviceIdentifier, AuthorizationRetryWorker.RETRY_REASON_INITIAL_FAILURE)
        return false
      }

      // 设置授权成功状态
      statusManager.setAuthorized()
      AILog.i(TAG, "DUILite authorization completed successfully")

      // 业务服务初始化
      val servicesSuccess = dependencyContainer.initialize()
      if (!servicesSuccess) {
        AILog.e(TAG, "Business services initialization failed")
        statusManager.setError("业务服务初始化失败")
        return false
      }
      AILog.i(TAG, "Business services initialization completed successfully")

      // 设置最终状态
      statusManager.setInitialized() // 设置完全初始化完成
      AILog.i(TAG, "Full initialization process completed successfully!")
      return true
    }

    /**
     * 手动重试授权
     */
    fun manualRetry(deviceIdentifier: String) {
      AILog.i(TAG, "Manual retry requested. Enqueuing worker directly.")
      enqueueRetryWorker(deviceIdentifier, AuthorizationRetryWorker.RETRY_REASON_MANUAL_RETRY)
    }

    /**
     * 取消所有授权重试任务
     */
    fun cancelAllRetries() {
      AILog.i(TAG, "Cancelling all authorization retry tasks")
      workManager.cancelUniqueWork(WORK_NAME_AUTH_RETRY)
    }

    /**
     * 重试任务的入队
     */
    private fun enqueueRetryWorker(deviceIdentifier: String, retryReason: String) {
      val workRequest = OneTimeWorkRequestBuilder<AuthorizationRetryWorker>()
        .setInputData(
          AuthorizationRetryWorker.createInputData(
            deviceIdentifier = deviceIdentifier,
            retryReason = retryReason
          )
        )
        .setConstraints(
          Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .build()
        )
        .setBackoffCriteria(
          BackoffPolicy.EXPONENTIAL,
          INITIAL_BACKOFF_DELAY_SECONDS,
          TimeUnit.SECONDS
        )
        .build()

      workManager.enqueueUniqueWork(
        WORK_NAME_AUTH_RETRY,
        ExistingWorkPolicy.REPLACE,
        workRequest
      )
      AILog.i(TAG, "Authorization retry worker has been enqueued.")
    }
  }