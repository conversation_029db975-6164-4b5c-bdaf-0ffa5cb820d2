package com.aispeech.modellibs.database

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.aispeech.modellibs.TaskStatus
import kotlinx.coroutines.flow.Flow

/**
 * 任务数据访问对象
 */
@Dao
interface TaskDao {

  /**
   * 插入新任务
   * 如果任务ID已存在，则替换现有任务
   */
  @Insert(onConflict = OnConflictStrategy.REPLACE)
  suspend fun insertTask(task: TaskEntity): Long

  /**
   * 批量插入任务
   */
  @Insert(onConflict = OnConflictStrategy.REPLACE)
  suspend fun insertTasks(tasks: List<TaskEntity>)

  /**
   * 更新任务
   */
  @Update
  suspend fun updateTask(task: TaskEntity): Int

  /**
   * 删除任务
   */
  @Delete
  suspend fun deleteTask(task: TaskEntity): Int

  /**
   * 根据任务ID删除任务
   */
  @Query("DELETE FROM tasks WHERE task_id = :taskId")
  suspend fun deleteTaskById(taskId: String): Int

  /**
   * 删除所有已完成的任务（包括成功、失败、取消）
   */
  @Query("DELETE FROM tasks WHERE status IN (:finishedStatuses)")
  suspend fun deleteFinishedTasks(
    finishedStatuses: List<Int> = listOf(
      TaskStatus.COMPLETED,
      TaskStatus.FAILED,
      TaskStatus.CANCELLED
    )
  ): Int

  /**
   * 删除指定时间之前创建的任务
   */
  @Query("DELETE FROM tasks WHERE created_at < :beforeTimestamp")
  suspend fun deleteTasksCreatedBefore(beforeTimestamp: Long): Int

  /**
   * 根据任务ID查询任务
   */
  @Query("SELECT * FROM tasks WHERE task_id = :taskId")
  suspend fun getTaskById(taskId: String): TaskEntity?

  /**
   * 根据任务ID查询任务（响应式）
   */
  @Query("SELECT * FROM tasks WHERE task_id = :taskId")
  fun getTaskByIdFlow(taskId: String): Flow<TaskEntity?>

  /**
   * 查询指定状态的任务
   */
  @Query("SELECT * FROM tasks WHERE status = :status ORDER BY created_at ASC")
  suspend fun getTasksByStatus(status: Int): List<TaskEntity>

  /**
   * 查询指定状态的任务（响应式）
   */
  @Query("SELECT * FROM tasks WHERE status = :status ORDER BY created_at ASC")
  fun getTasksByStatusFlow(status: Int): Flow<List<TaskEntity>>

  /**
   * 查询队列中的任务（按创建时间排序）
   */
  @Query("SELECT * FROM tasks WHERE status = ${TaskStatus.QUEUED} ORDER BY created_at ASC")
  suspend fun getQueuedTasks(): List<TaskEntity>

  /**
   * 获取下一个待执行的任务
   * 返回创建时间最早的队列中任务
   */
  @Query("SELECT * FROM tasks WHERE status = ${TaskStatus.QUEUED} ORDER BY created_at ASC LIMIT 1")
  suspend fun getNextQueuedTask(): TaskEntity?

  /**
   * 查询正在执行的任务
   */
  @Query("SELECT * FROM tasks WHERE status = ${TaskStatus.EXECUTING}")
  suspend fun getExecutingTasks(): List<TaskEntity>

  /**
   * 查询所有任务
   */
  @Query("SELECT * FROM tasks ORDER BY created_at DESC")
  suspend fun getAllTasks(): List<TaskEntity>

  /**
   * 查询所有任务（响应式）
   */
  @Query("SELECT * FROM tasks ORDER BY created_at DESC")
  fun getAllTasksFlow(): Flow<List<TaskEntity>>

  /**
   * 统计指定状态的任务数量
   */
  @Query("SELECT COUNT(*) FROM tasks WHERE status = :status")
  suspend fun countTasksByStatus(status: Int): Int

  /**
   * 统计队列中的任务数量
   */
  @Query("SELECT COUNT(*) FROM tasks WHERE status = ${TaskStatus.QUEUED}")
  suspend fun countQueuedTasks(): Int

  /**
   * 统计正在执行的任务数量
   */
  @Query("SELECT COUNT(*) FROM tasks WHERE status = ${TaskStatus.EXECUTING}")
  suspend fun countExecutingTasks(): Int

  /**
   * 更新任务状态
   */
  @Query("UPDATE tasks SET status = :newStatus, updated_at = :updatedAt WHERE task_id = :taskId")
  suspend fun updateTaskStatus(taskId: String, newStatus: Int, updatedAt: Long = System.currentTimeMillis()): Int

  /**
   * 更新任务状态和错误信息
   */
  @Query("UPDATE tasks SET status = :newStatus, error_message = :errorMessage, updated_at = :updatedAt WHERE task_id = :taskId")
  suspend fun updateTaskStatusWithError(
    taskId: String,
    newStatus: Int,
    errorMessage: String?,
    updatedAt: Long = System.currentTimeMillis()
  ): Int

  /**
   * 更新任务状态和结果数据
   */
  @Query("UPDATE tasks SET status = :newStatus, result_data = :resultData, updated_at = :updatedAt WHERE task_id = :taskId")
  suspend fun updateTaskStatusWithResult(
    taskId: String,
    newStatus: Int,
    resultData: String?,
    updatedAt: Long = System.currentTimeMillis()
  ): Int

  /**
   * 原子性地将任务状态从 QUEUED 更新为 EXECUTING
   * 用于防止多个工作者同时获取同一个任务
   *
   * @return 更新的行数，1表示成功，0表示任务已被其他工作者获取或不存在
   */
  @Query("UPDATE tasks SET status = ${TaskStatus.EXECUTING}, updated_at = :updatedAt WHERE task_id = :taskId AND status = ${TaskStatus.QUEUED}")
  suspend fun atomicUpdateQueuedToExecuting(taskId: String, updatedAt: Long = System.currentTimeMillis()): Int

  /**
   * 取消指定任务（仅当任务在队列中时）
   */
  @Query("UPDATE tasks SET status = ${TaskStatus.CANCELLED}, updated_at = :updatedAt WHERE task_id = :taskId AND status = ${TaskStatus.QUEUED}")
  suspend fun cancelQueuedTask(taskId: String, updatedAt: Long = System.currentTimeMillis()): Int

  /**
   * 清理过期任务
   * 删除指定时间之前更新且已完成的任务
   */
  @Query("""
    DELETE FROM tasks 
    WHERE updated_at < :beforeTimestamp 
    AND status IN (${TaskStatus.COMPLETED}, ${TaskStatus.FAILED}, ${TaskStatus.CANCELLED})
  """)
  suspend fun cleanupExpiredTasks(beforeTimestamp: Long): Int

  /**
   * 将所有 EXECUTING 状态的任务标记为 FAILED
   * 用于服务重启时清理孤立的执行中任务
   *
   * @param errorMessage 错误信息
   * @param updatedAt 更新时间戳
   * @return 更新的任务数量
   */
  @Query("UPDATE tasks SET status = ${TaskStatus.FAILED}, error_message = :errorMessage, updated_at = :updatedAt WHERE status = ${TaskStatus.EXECUTING}")
  suspend fun markExecutingTasksAsFailed(
    errorMessage: String = "服务重启，任务被中断",
    updatedAt: Long = System.currentTimeMillis()
  ): Int

  /**
   * 获取任务统计信息
   */
  @Query("""
    SELECT 
      COUNT(*) as total,
      SUM(CASE WHEN status = ${TaskStatus.QUEUED} THEN 1 ELSE 0 END) as queued,
      SUM(CASE WHEN status = ${TaskStatus.EXECUTING} THEN 1 ELSE 0 END) as executing,
      SUM(CASE WHEN status = ${TaskStatus.COMPLETED} THEN 1 ELSE 0 END) as completed,
      SUM(CASE WHEN status = ${TaskStatus.FAILED} THEN 1 ELSE 0 END) as failed,
      SUM(CASE WHEN status = ${TaskStatus.CANCELLED} THEN 1 ELSE 0 END) as cancelled
    FROM tasks
  """)
  suspend fun getTaskStatistics(): TaskStatistics
}

/**
 * 任务统计信息数据类
 */
data class TaskStatistics(
  val total: Int,
  val queued: Int,
  val executing: Int,
  val completed: Int,
  val failed: Int,
  val cancelled: Int
)
