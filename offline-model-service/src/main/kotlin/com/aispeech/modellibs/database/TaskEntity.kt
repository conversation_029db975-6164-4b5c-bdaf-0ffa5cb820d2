package com.aispeech.modellibs.database

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey
import com.aispeech.modellibs.TaskStatus
import com.aispeech.modellibs.fromDatabaseValueToTaskTypeByte
import com.aispeech.modellibs.toApiKey

/**
 * 任务数据库实体
 *
 * 作为任务元数据的单一数据源，存储任务的完整生命周期信息。
 * 任务的输入数据以文件形式存储，结果数据直接存储在数据库中。
 *
 * @property taskId 任务唯一标识符（主键）
 * @property taskType 任务类型，使用 TaskType 常量
 * @property status 任务状态，使用 TaskStatus 常量
 * @property inputDataPath 输入数据文件路径（存储在应用内部私有目录）
 * @property resultData 结果数据内容（可选，成功时设置）
 * @property errorMessage 错误信息（失败时设置）
 * @property createdAt 任务创建时间戳（毫秒）
 * @property updatedAt 任务最后更新时间戳（毫秒）
 */
@Entity(tableName = "tasks")
data class TaskEntity(
  @PrimaryKey
  @ColumnInfo(name = "task_id")
  val taskId: String,

  @ColumnInfo(name = "task_type")
  val taskType: Int,

  @ColumnInfo(name = "status")
  val status: Int,

  @ColumnInfo(name = "input_data_path")
  val inputDataPath: String,

  @ColumnInfo(name = "result_data")
  val resultData: String? = null,

  @ColumnInfo(name = "error_message")
  val errorMessage: String? = null,

  @ColumnInfo(name = "created_at")
  val createdAt: Long,

  @ColumnInfo(name = "updated_at")
  val updatedAt: Long
) {

  /**
   * 检查任务是否已完成（成功或失败）
   */
  fun isCompleted(): Boolean {
    return status == TaskStatus.COMPLETED
  }

  /**
   * 检查任务是否已结束（包括成功、失败、取消）
   */
  fun isFinished(): Boolean {
    return status == TaskStatus.COMPLETED ||
      status == TaskStatus.FAILED ||
      status == TaskStatus.CANCELLED
  }

  /**
   * 检查任务是否正在执行
   */
  fun isExecuting(): Boolean {
    return status == TaskStatus.EXECUTING
  }

  /**
   * 检查任务是否在队列中等待
   */
  fun isQueued(): Boolean {
    return status == TaskStatus.QUEUED
  }

  /**
   * 检查任务是否被取消
   */
  fun isCancelled(): Boolean {
    return status == TaskStatus.CANCELLED
  }

  /**
   * 检查任务是否失败
   */
  fun isFailed(): Boolean {
    return status == TaskStatus.FAILED
  }

  /**
   * 获取可读的任务类型描述
   */
  fun getReadableTaskType(): String {
    return fromDatabaseValueToTaskTypeByte(taskType)?.toApiKey() ?: ""
  }

  /**
   * 获取可读的状态描述
   */
  fun getReadableStatus(): String {
    return when (status) {
      TaskStatus.QUEUED -> "队列中"
      TaskStatus.EXECUTING -> "执行中"
      TaskStatus.COMPLETED -> "已完成"
      TaskStatus.FAILED -> "失败"
      TaskStatus.CANCELLED -> "已取消"
      else -> "未知状态($status)"
    }
  }

  /**
   * 转换为 TaskState 对象（用于 AIDL 传输）
   * 直接返回结果数据内容
   */
  fun toTaskState(): com.aispeech.modellibs.TaskState {
    return com.aispeech.modellibs.TaskState(
      taskId = taskId,
      taskType = taskType,
      status = status,
      result = if (status == TaskStatus.COMPLETED) resultData else null,
      errorMessage = errorMessage
    )
  }

  companion object {
    /**
     * 创建新任务实体
     */
    fun create(
      taskId: String,
      taskType: Int,
      inputDataPath: String
    ): TaskEntity {
      val currentTime = System.currentTimeMillis()
      return TaskEntity(
        taskId = taskId,
        taskType = taskType,
        status = TaskStatus.QUEUED,
        inputDataPath = inputDataPath,
        createdAt = currentTime,
        updatedAt = currentTime
      )
    }

    /**
     * 更新任务状态
     */
    fun TaskEntity.updateStatus(
      newStatus: Int,
      errorMessage: String? = null,
      resultData: String? = null
    ): TaskEntity {
      return copy(
        status = newStatus,
        errorMessage = errorMessage,
        resultData = resultData,
        updatedAt = System.currentTimeMillis()
      )
    }
  }
}
