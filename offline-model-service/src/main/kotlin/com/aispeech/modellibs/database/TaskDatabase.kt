package com.aispeech.modellibs.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.sqlite.db.SupportSQLiteDatabase
import com.aispeech.aibase.AILog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 任务数据库
 *
 * 使用Room数据库作为任务元数据的单一数据源。
 * 支持数据库版本管理和自动迁移。
 */
@Database(
  entities = [TaskEntity::class],
  version = 1,
  exportSchema = false
)
abstract class TaskDatabase : RoomDatabase() {

  /**
   * 获取任务数据访问对象
   */
  abstract fun taskDao(): TaskDao

  companion object {
    private const val TAG = "TaskDatabase"
    const val DATABASE_NAME = "task_database"

    @Volatile
    private var INSTANCE: TaskDatabase? = null

    /**
     * 获取数据库实例（单例模式）
     */
    fun getInstance(context: Context): TaskDatabase {
      return INSTANCE ?: synchronized(this) {
        val instance = Room.databaseBuilder(
          context.applicationContext,
          TaskDatabase::class.java,
          DATABASE_NAME
        )
          .addCallback(DatabaseCallback())
          .addMigrations()
          .build()

        INSTANCE = instance
        AILog.i(TAG, "TaskDatabase instance created")
        instance
      }
    }

    /**
     * 清理数据库实例（主要用于测试）
     */
    fun clearInstance() {
      synchronized(this) {
        INSTANCE?.close()
        INSTANCE = null
        AILog.i(TAG, "TaskDatabase instance cleared")
      }
    }
  }

  /**
   * 数据库回调
   * 处理数据库创建和打开事件
   */
  private class DatabaseCallback : Callback() {

    override fun onCreate(db: SupportSQLiteDatabase) {
      super.onCreate(db)
      AILog.i(TAG, "TaskDatabase created")

      // 在数据库创建后执行初始化操作
      INSTANCE?.let { _ ->
        CoroutineScope(Dispatchers.IO).launch {
          AILog.i(TAG, "TaskDatabase initialization completed")
        }
      }
    }

    override fun onOpen(db: SupportSQLiteDatabase) {
      super.onOpen(db)
      AILog.i(TAG, "TaskDatabase opened")

      // 在数据库打开后执行清理操作
      INSTANCE?.let { database ->
        CoroutineScope(Dispatchers.IO).launch {
          performMaintenanceTasks(database)
        }
      }
    }

    /**
     * 执行数据库维护任务
     */
    private suspend fun performMaintenanceTasks(database: TaskDatabase) {
      try {
        val taskDao = database.taskDao()

        // 清理过期任务（7天前的已完成任务）
        val sevenDaysAgo = System.currentTimeMillis() - (7 * 24 * 60 * 60 * 1000L)
        val deletedCount = taskDao.cleanupExpiredTasks(sevenDaysAgo)

        if (deletedCount > 0) {
          AILog.i(TAG, "Cleaned up $deletedCount expired tasks")
        }

        // 记录当前任务统计
        val stats = taskDao.getTaskStatistics()
        AILog.i(TAG, "Current task statistics: $stats")

      } catch (e: Exception) {
        AILog.e(TAG, "Error performing maintenance tasks", e)
      }
    }
  }
}

/**
 * 数据库迁移工具类
 */
object TaskDatabaseMigrations

/**
 * 数据库工具类
 * 提供数据库相关的实用方法
 */
object TaskDatabaseUtils {

  /**
   * 检查数据库是否健康
   */
  suspend fun checkDatabaseHealth(database: TaskDatabase): Boolean {
    return try {
      val taskDao = database.taskDao()
      val stats = taskDao.getTaskStatistics()
      AILog.i("TaskDatabaseUtils", "Database health check passed: $stats")
      true
    } catch (e: Exception) {
      AILog.e("TaskDatabaseUtils", "Database health check failed", e)
      false
    }
  }

  /**
   * 获取数据库大小信息
   */
  fun getDatabaseSize(context: Context): Long {
    return try {
      val dbFile = context.getDatabasePath(TaskDatabase.DATABASE_NAME)
      if (dbFile.exists()) {
        dbFile.length()
      } else {
        0L
      }
    } catch (e: Exception) {
      AILog.e("TaskDatabaseUtils", "Error getting database size", e)
      0L
    }
  }

  /**
   * 清理数据库（删除所有数据）
   */
  suspend fun clearAllData(database: TaskDatabase) {
    try {
      database.clearAllTables()
      AILog.w("TaskDatabaseUtils", "All database data cleared")
    } catch (e: Exception) {
      AILog.e("TaskDatabaseUtils", "Error clearing database data", e)
    }
  }
}
