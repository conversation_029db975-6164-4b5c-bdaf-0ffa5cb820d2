package com.aispeech.modellibs.engine

import com.aispeech.export.engines2.AILLMEngine

/**
 * LLM引擎管理器接口
 * 定义LLM引擎的生命周期管理策略
 */
interface ILLMEngineManager {

  /**
   * 请求使用引擎服务
   * @return true 如果服务就绪，false 如果启动失败
   */
  suspend fun acquireEngineService(): Boolean

  /**
   * 释放引擎服务
   */
  suspend fun releaseEngineService()

  /**
   * 强制销毁引擎服务
   */
  suspend fun forceDestroy()

  /**
   * 获取引擎实例（仅在acquire成功后调用）
   */
  fun getEngineInstance(): AILLMEngine?
}