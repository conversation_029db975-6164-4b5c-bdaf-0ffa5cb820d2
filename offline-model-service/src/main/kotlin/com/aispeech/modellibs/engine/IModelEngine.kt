package com.aispeech.modellibs.engine

import android.content.Context
import com.aispeech.modellibs.TaskTypeByte

/**
 * 模型引擎接口
 * 负责底层模型的初始化和处理逻辑
 */
interface IModelEngine {

  /**
   * 引擎是否已初始化
   */
  val isInitialized: Boolean

  /**
   * 初始化引擎
   */
  fun initialize(context: Context): Boolean

  /**
   * 提取文本摘要
   * @param inputText 输入文本
   * @return 摘要结果，失败或输入不合法时返回 null
   */
  fun extractAbstract(inputText: String): String?

  /**
   * 检测待办事项
   * @param inputText 输入文本
   * @return 检测结果，失败或输入不合法时返回 null
   */
  fun detectActionItem(inputText: String): String?


  /**
   * 通用的文本处理入口。
   * @param taskType 任务类型枚举，明确要执行的操作。
   * @param inputText 输入文本。
   * @return 处理结果，失败或输入不合法时返回 null。
   */
  fun process(taskType: TaskTypeByte, inputText: String): String?

  /**
   * 释放资源
   */
  fun release()
}
