package com.aispeech.modellibs.engine

import com.aispeech.aibase.AILog
import com.aispeech.export.engines2.AILLMEngine
import com.aispeech.export.engines2.MagnusRuntimeHelper
import com.aispeech.modellibs.ConfigurationManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext

/**
 * 按需LLM引擎服务管理器
 */
class OnDemandLLMEngineManager: ILLMEngineManager {

  companion object {
    private const val TAG = "OnDemandLLMEngineManager"
  }

  // 引擎实例和状态
  private var llmEngine: AILLMEngine? = null
  private var isInitialized = false
  private var lastUsedPath: String? = null // 记录上次使用的路径
  private val mutex = Mutex()

  /**
   * 确保服务已启动。
   * @return true 如果服务已就绪, false 如果启动失败。
   */
  override suspend fun acquireEngineService(): Boolean = mutex.withLock {
    val currentPath = ConfigurationManager.getMagnusRuntimePath()

    // 检查路径是否发生变更，如果变更则需要重新初始化
    if (isInitialized && lastUsedPath != currentPath) {
      AILog.i(TAG, "Magnus runtime path changed from '$lastUsedPath' to '$currentPath', reinitializing engine...")
      destroyEngine()
    }

    if (isInitialized) {
      AILog.i(TAG, "Engine service is already running.")
      true
    } else {
      AILog.i(TAG, "Engine service is not running. Starting...")
      initializeEngine()
    }
  }

  /**
   * 停止服务
   */
  override suspend fun releaseEngineService() {
    mutex.withLock {
      if (!isInitialized) {
        AILog.i(TAG, "Engine service is already stopped.")
        return@withLock
      }
      AILog.i(TAG, "Releasing engine service as requested.")
      destroyEngine()
    }
  }

  /**
   * 强制停止服务，用于应用退出等必须清理资源的场景。
   */
  override suspend fun forceDestroy() {
    mutex.withLock {
      if (isInitialized) {
        AILog.i(TAG, "Force destroying engine service...")
        destroyEngine()
      }
    }
  }

  /**
   * 获取引擎实例（仅在acquire成功后调用）
   */
  override fun getEngineInstance(): AILLMEngine? {
    return if (isInitialized) llmEngine else null
  }

  /**
   * 初始化引擎服务
   */
  private fun initializeEngine(): Boolean {
    return try {
      val currentPath = ConfigurationManager.getMagnusRuntimePath()
      AILog.i(TAG, "Initializing LLM engine service with path: $currentPath")

      // 初始化Magnus运行时环境
      val magnusRuntimeEnvInfo = ConfigurationManager.createMagnusRuntimeEnvInfo()
      val magnusRuntimeConfig = ConfigurationManager.createMagnusRuntimeConfig(magnusRuntimeEnvInfo)
      MagnusRuntimeHelper.getInstance().init(magnusRuntimeConfig)

      // 创建并初始化LLM引擎
      val engine = AILLMEngine.createInstance()
      val code = engine.initEngine()

      if (code == 0) {
        llmEngine = engine
        isInitialized = true
        lastUsedPath = currentPath
        true
      } else {
        AILog.e(TAG, "LLM engine service failed to start, error code: $code")
        engine.destroy()
        isInitialized = false // 确保状态正确
        false
      }
    } catch (e: Exception) {
      AILog.e(TAG, "LLM engine service initialization threw an exception", e)
      isInitialized = false
      false
    }
  }

  /**
   * 销毁引擎服务
   */
  private suspend fun destroyEngine() = withContext(Dispatchers.IO) {
    try {
      llmEngine?.let { engine ->
        AILog.i(TAG, "Destroying LLM engine service...")
        engine.destroy()
      }

      MagnusRuntimeHelper.getInstance().destroy()
      AILog.i(TAG, "Destroying Magnus runtime")
    } catch (e: Exception) {
      AILog.e(TAG, "Exception while destroying LLM engine service", e)
    } finally {
      // 确保状态总是被重置
      llmEngine = null
      isInitialized = false
      lastUsedPath = null
      AILog.i(TAG, "LLM engine service has been shut down.")
    }
  }
}


