package com.aispeech.modellibs.engine

import android.content.Context
import com.aispeech.aibase.AILog
import com.aispeech.modellibs.TaskType
import com.aispeech.modellibs.TaskTypeByte
import com.aispeech.modellibs.toApiKey
import com.aispeech.modelutil.ModelEngine
import kotlinx.coroutines.runBlocking
import kotlin.system.measureTimeMillis

/**
 * 模型引擎单例类
 * 负责管理底层模型的初始化和处理逻辑
 */
class LocalModelEngine : IModelEngine {

  companion object {
    const val TAG = "LocalModelEngine"
  }

  private var modelEngine: ModelEngine? = null

  private var _isInitialized = false
  override val isInitialized: Boolean get() = _isInitialized

  /**
   * 初始化引擎
   */
  override fun initialize(context: Context): Boolean {
    if (_isInitialized) {
      AILog.i(TAG, "ModelEngine already initialized")
      return true
    }
    return try {
      // [TODO] 这里简单处理了，因为目前使用的是 LocalModelProvider 的方式，映射是固定死的，后续如果拓展成热更新的方式，这里需要修改成挂起的
      runBlocking {
        modelEngine = ModelEngine.getInstance(context.applicationContext)
        modelEngine?.initialize()
      }
      _isInitialized = true
      AILog.i(TAG, "ModelEngine initialized successfully")
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to initialize ModelEngine", e)
      false
    }
  }

  /**
   * 提取文本摘要
   * @param inputText 输入文本
   * @return 摘要结果，失败或输入不合法时返回 null
   */
  override fun extractAbstract(inputText: String): String? =
    processText("extractAbstract", inputText) {
      modelEngine?.extractAbstract(it)
    }

  /**
   * 检测待办事项
   * @param inputText 输入文本
   * @return 检测结果，失败或输入不合法时返回 null
   */
  override fun detectActionItem(inputText: String): String? =
    processText("detectActionItem", inputText) {
      modelEngine?.dectionActionItem(it)
    }

  override fun process(taskType: TaskTypeByte, inputText: String): String? {
    if (!_isInitialized) {
      AILog.i(TAG, "ModelEngine not initialized")
      return null
    }

    val trimmed = inputText.trim()
    if (trimmed.isEmpty()) {
      AILog.w(TAG, "Input text is blank for ${taskType.toApiKey()}")
      return null
    }

    val action = taskType.toApiKey()

    return try {
      AILog.i(TAG, "$action start, text length: ${trimmed.length}, content: ${trimmed.take(50)}")
      val result: String?
      val duration = measureTimeMillis {
        result = when (taskType) {
          TaskType.EXTRACT_ABSTRACT -> modelEngine?.extractAbstract(trimmed)
          TaskType.DETECT_ACTION_ITEM -> modelEngine?.dectionActionItem(trimmed)
          TaskType.EXTRACT_QA -> modelEngine?.extractQA(trimmed)
          TaskType.SUMMARIZE_BY_ROLE -> modelEngine?.summarizeByRole(trimmed)
          TaskType.RECOGNIZE_SCENE -> modelEngine?.recognizeScene(trimmed)
          else -> {
            throw IllegalArgumentException("Unsupported task type value from DB: $taskType")
          }
        }
      }
      AILog.i(TAG, "$action completed in ${duration}ms, result preview: ${result?.take(200)}")
      result
    } catch (e: Exception) {
      AILog.i(TAG, "Error in $action and exception: ${e.message}")
      null
    }
  }


  /**
   * 通用处理逻辑：包含初始化检查、输入合法性、异常捕获等
   */
  private inline fun processText(
    action: String,
    inputText: String,
    block: (String) -> String?
  ): String? {
    if (!_isInitialized) {
      AILog.i(TAG, "ModelEngine not initialized")
      return null
    }
    val trimmed = inputText.trim()
    if (trimmed.isEmpty()) {
      AILog.w(TAG, "Input text is blank for $action")
      return null
    }
    return try {
      AILog.i(TAG, "$action start, text length: ${trimmed.length}")
      val result: String?
      val duration = measureTimeMillis {
        result = block(trimmed)
      }
      AILog.i(TAG, "$action completed in ${duration}ms, take result: ${result?.take(30)}")
      result
    } catch (e: Exception) {
      AILog.e(TAG, "Error in $action", e)
      null
    }
  }

  /**
   * 释放资源
   */
  override fun release() {
    try {
      _isInitialized = false
      AILog.i(TAG, "ModelEngine released")
    } catch (e: Exception) {
      AILog.e(TAG, "Error releasing ModelEngine", e)
    }
  }

}
