package com.aispeech.modellibs.monitor

import android.content.Context
import com.aispeech.aibase.AILog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 内存监控管理器
 * 负责管理ResourceMonitor的生命周期，并提供便捷的访问接口
 */
class MemoryMonitorManager(
  private val context: Context
) {

  companion object {
    private const val TAG = "MemoryMonitorManager"
  }

  // 资源监控器
  private var resourceMonitor: ResourceMonitor? = null

  // 管理器作用域
  private val managerScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

  /**
   * 初始化内存监控
   */
  fun initialize() {
    if (resourceMonitor != null) {
      AILog.w(TAG, "Memory monitor already initialized")
      return
    }

    try {
      resourceMonitor = ResourceMonitor(context)
      AILog.i(TAG, "Memory monitor initialized successfully")
      recordMemorySnapshotBeforeModelLoad()
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to initialize memory monitor", e)
    }
  }

  /**
   * 开始内存监控
   */
  fun startMonitoring() {
    val monitor = resourceMonitor
    if (monitor == null) {
      AILog.w(TAG, "Memory monitor not initialized")
      return
    }

    try {
      monitor.startMonitoring()
      AILog.i(TAG, "Memory monitoring started")
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to start memory monitoring", e)
    }
  }

  /**
   * 停止内存监控
   */
  fun stopMonitoring() {
    try {
      resourceMonitor?.stopMonitoring()
      AILog.i(TAG, "Memory monitoring stopped")
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to stop memory monitoring", e)
    }
  }

  /**
   * 获取内存状态流
   */
  fun getMemoryStatusFlow(): StateFlow<MemoryStatus>? {
    return resourceMonitor?.memoryStatus
  }

  /**
   * 获取当前内存快照
   */
  fun getCurrentMemorySnapshot(): MemoryStatus? {
    return resourceMonitor?.memoryStatus?.value
  }

  /**
   * 记录模型加载前的内存快照
   */
  fun recordMemorySnapshotBeforeModelLoad() {
    try {
      resourceMonitor?.recordBeforeModelLoad()
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to record memory snapshot before model load", e)
    }
  }

  /**
   * 记录模型加载后的内存快照
   */
  fun recordMemorySnapshotAfterModelLoad() {
    try {
      resourceMonitor?.recordAfterModelLoad()
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to record memory snapshot after model load", e)
    }
  }

  /**
   * 获取格式化的内存报告
   */
  fun getFormattedMemoryReport(): String {
    val snapshot = getCurrentMemorySnapshot() ?: return "Memory monitor not available"

    return buildString {
      appendLine("=== 内存监控报告 ===")
      appendLine("时间戳: ${formatTimestamp(snapshot.timestamp)}")
      appendLine()

      // 系统内存
      appendLine("【系统内存】")
      appendLine("  总内存: ${formatBytes(snapshot.systemTotalMemory)}")
      appendLine("  可用内存: ${formatBytes(snapshot.systemAvailableMemory)}")
      appendLine("  已使用: ${formatBytes(snapshot.systemTotalMemory - snapshot.systemAvailableMemory)}")
      appendLine("  使用率: ${formatPercentage(snapshot.systemTotalMemory - snapshot.systemAvailableMemory, snapshot.systemTotalMemory)}")
      appendLine("  低内存状态: ${if (snapshot.isLowMemory) "是" else "否"}")
      appendLine()

      // 应用内存
      appendLine("【应用内存】")
      appendLine("  PSS总内存: ${formatBytes(snapshot.appPssMemory)}")
      appendLine("  Runtime最大: ${formatBytes(snapshot.runtimeMaxMemory)}")
      appendLine("  Runtime已用: ${formatBytes(snapshot.runtimeUsedMemory)}")
      appendLine("  Runtime空闲: ${formatBytes(snapshot.runtimeFreeMemory)}")
      appendLine("  Runtime使用率: ${formatPercentage(snapshot.runtimeUsedMemory, snapshot.runtimeMaxMemory)}")
      appendLine("  应用内存占设备比例: ${formatPercentage(snapshot.appPssMemory, snapshot.systemTotalMemory)}")
      appendLine()

      // 模型内存
      if (snapshot.modelMemoryUsage > 0) {
        appendLine("【模型内存】")
        appendLine("  模型实际占用: ${formatBytes(snapshot.modelMemoryUsage)}")
        appendLine("  模型占PSS比例: ${formatPercentage(snapshot.modelMemoryUsage, snapshot.appPssMemory)}")
        appendLine()
      }
    }
  }

  /**
   * 打印内存报告到日志
   */
  fun logMemoryReport() {
    managerScope.launch {
      try {
        val report = getFormattedMemoryReport()
        AILog.i(TAG, "Memory Report:\n$report")
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to generate memory report", e)
      }
    }
  }

  /**
   * 格式化时间戳
   */
  private fun formatTimestamp(timestamp: Long): String {
    return SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date(timestamp))
  }

  /**
   * 格式化字节数
   */
  private fun formatBytes(bytes: Long): String {
    if (bytes < 0) return "N/A"

    val units = arrayOf("B", "KB", "MB", "GB")
    var size = bytes.toDouble()
    var unitIndex = 0

    while (size >= 1024 && unitIndex < units.size - 1) {
      size /= 1024
      unitIndex++
    }

    return String.format(Locale.US, "%.1f %s", size, units[unitIndex])
  }

  /**
   * 格式化百分比
   */
  private fun formatPercentage(value: Long, total: Long): String {
    if (total <= 0) return "0.0%"
    val percentage = (value.toFloat() / total) * 100
    return String.format(Locale.US, "%.1f%%", percentage)
  }

  /**
   * 释放资源
   */
  fun shutdown() {
    try {
      resourceMonitor?.shutdown()
      resourceMonitor = null
      AILog.i(TAG, "Memory monitor manager shutdown completed")
    } catch (e: Exception) {
      AILog.e(TAG, "Error during shutdown", e)
    }
  }
}
