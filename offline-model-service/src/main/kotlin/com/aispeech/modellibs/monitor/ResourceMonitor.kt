package com.aispeech.modellibs.monitor

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.os.Process
import com.aispeech.aibase.AILog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.util.Locale

/**
 * 简化的资源内存监控器
 */
class ResourceMonitor(private val context: Context) {

  companion object {
    private const val TAG = "ResourceMonitor"
    private const val MONITOR_INTERVAL_MS = 5000L
    private const val MEMORY_WARNING_THRESHOLD = 0.8f
    private const val PSS_WARNING_THRESHOLD = 0.4f
    private const val LARGE_MODEL_SIZE_THRESHOLD = 1024 * 1024 * 1024L // 1GB
  }

  private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
  private var monitoringJob: Job? = null
  private var isMonitoring = false

  private var pssBeforeLoad: Long = 0L
  private var pssAfterLoad: Long = 0L

  private val _memoryStatus = MutableStateFlow(MemoryStatus())
  val memoryStatus: StateFlow<MemoryStatus> = _memoryStatus.asStateFlow()

  private val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager

  /**
   * 开始监控
   */
  fun startMonitoring() {
    if (isMonitoring) return

    checkEnvironment()
    isMonitoring = true

    monitoringJob = scope.launch {
      while (isActive) {
        try {
          val status = collectMemoryStatus()
          _memoryStatus.value = status
          checkMemoryWarnings(status)
          delay(MONITOR_INTERVAL_MS)
        } catch (e: Exception) {
          AILog.e(TAG, "监控异常", e)
          delay(MONITOR_INTERVAL_MS)
        }
      }
    }
  }

  /**
   * 停止监控
   */
  fun stopMonitoring() {
    isMonitoring = false
    monitoringJob?.cancel()
    monitoringJob = null
  }

  /**
   * 记录模型加载前的内存快照
   */
  fun recordBeforeModelLoad() {
    pssBeforeLoad = getCurrentPssMemory()
    AILog.i(TAG, "模型加载前内存: ${formatBytes(pssBeforeLoad)}")
  }

  /**
   * 记录模型加载后的内存快照
   */
  fun recordAfterModelLoad() {
    pssAfterLoad = getCurrentPssMemory()
    val modelMemory = pssAfterLoad - pssBeforeLoad
    AILog.i(TAG, "模型加载后内存: ${formatBytes(pssAfterLoad)}")
    AILog.i(TAG, "模型实际占用: ${formatBytes(modelMemory)}")

    if (modelMemory > LARGE_MODEL_SIZE_THRESHOLD) {
      AILog.w(TAG, "检测到大模型! 内存占用: ${formatBytes(modelMemory)}")
    }
  }

  /**
   * 获取当前PSS内存
   */
  private fun getCurrentPssMemory(): Long {
    val memoryInfo = Debug.MemoryInfo()
    Debug.getMemoryInfo(memoryInfo)
    return memoryInfo.totalPss * 1024L
  }

  /**
   * 检查运行环境
   */
  private fun checkEnvironment() {
    val is64Bit = Process.is64Bit()
    AILog.i(TAG, "进程架构: ${if (is64Bit) "64位" else "32位"}")

    if (!is64Bit) {
      AILog.e(TAG, "警告: 32位进程可能无法加载大模型!")
    }

    val memoryInfo = ActivityManager.MemoryInfo()
    activityManager.getMemoryInfo(memoryInfo)
    val totalMemoryGB = memoryInfo.totalMem / (1024 * 1024 * 1024f)
    AILog.i(TAG, "设备总内存: ${String.format(Locale.US,"%.1f GB", totalMemoryGB)}")
  }

  /**
   * 收集内存状态
   */
  private fun collectMemoryStatus(): MemoryStatus {
    val appMemory = getCurrentPssMemory()
    val systemMemory = getSystemMemoryInfo()
    val runtimeMemory = getRuntimeMemoryInfo()
    val modelMemory = if (pssAfterLoad > pssBeforeLoad) pssAfterLoad - pssBeforeLoad else 0L

    return MemoryStatus(
      timestamp = System.currentTimeMillis(),
      appPssMemory = appMemory,
      systemTotalMemory = systemMemory.totalMem,
      systemAvailableMemory = systemMemory.availMem,
      isLowMemory = systemMemory.lowMemory,
      runtimeUsedMemory = runtimeMemory.used,
      runtimeMaxMemory = runtimeMemory.max,
      runtimeFreeMemory = runtimeMemory.free,
      modelMemoryUsage = modelMemory
    )
  }

  /**
   * 获取系统内存信息
   */
  private fun getSystemMemoryInfo(): ActivityManager.MemoryInfo {
    val memoryInfo = ActivityManager.MemoryInfo()
    activityManager.getMemoryInfo(memoryInfo)
    return memoryInfo
  }

  /**
   * 获取Runtime内存信息 (替代JVM内存信息)
   */
  private fun getRuntimeMemoryInfo(): RuntimeMemoryInfo {
    val runtime = Runtime.getRuntime()
    val maxMemory = runtime.maxMemory()
    val totalMemory = runtime.totalMemory()
    val freeMemory = runtime.freeMemory()
    val usedMemory = totalMemory - freeMemory

    return RuntimeMemoryInfo(
      max = maxMemory,
      total = totalMemory,
      free = freeMemory,
      used = usedMemory
    )
  }

  /**
   * 检查内存警告
   */
  private fun checkMemoryWarnings(status: MemoryStatus) {
    // 系统低内存警告
    if (status.isLowMemory) {
      AILog.e(TAG, "🚨 系统低内存警告! 应用可能被杀死!")
    }

    // 应用PSS占用率检查
    val appPssRatio = status.appPssMemory.toFloat() / status.systemTotalMemory
    if (appPssRatio >= PSS_WARNING_THRESHOLD) {
      AILog.w(TAG, "应用内存占用已达到 ${(appPssRatio * 100).toInt()}% 设备内存")
      AILog.w(TAG, "应用PSS: ${formatBytes(status.appPssMemory)} / 设备总内存: ${formatBytes(status.systemTotalMemory)}")
    }

    // 系统内存使用率检查
    val systemUsageRatio = (status.systemTotalMemory - status.systemAvailableMemory).toFloat() / status.systemTotalMemory
    if (systemUsageRatio >= MEMORY_WARNING_THRESHOLD) {
      AILog.w(TAG, "系统内存使用率: ${(systemUsageRatio * 100).toInt()}%")
    }

    // Runtime堆内存检查
    val runtimeRatio = status.runtimeUsedMemory.toFloat() / status.runtimeMaxMemory
    if (runtimeRatio >= MEMORY_WARNING_THRESHOLD) {
      AILog.w(TAG, "Runtime堆内存使用率: ${(runtimeRatio * 100).toInt()}%")
    }
  }

  /**
   * 格式化字节数显示
   */
  private fun formatBytes(bytes: Long): String {
    if (bytes < 0) return "N/A"

    val units = arrayOf("B", "KB", "MB", "GB")
    var size = bytes.toDouble()
    var unitIndex = 0

    while (size >= 1024 && unitIndex < units.size - 1) {
      size /= 1024
      unitIndex++
    }

    return String.format(Locale.US, "%.1f %s", size, units[unitIndex])
  }

  /**
   * 释放资源
   */
  fun shutdown() {
    stopMonitoring()
    scope.cancel()
    AILog.i(TAG, "ResourceMonitor 已关闭")
  }
}

/**
 * Runtime内存信息数据类
 */
data class RuntimeMemoryInfo(
  val max: Long,
  val total: Long,
  val free: Long,
  val used: Long
)

/**
 * 简化的内存状态数据类
 */
data class MemoryStatus(
  val timestamp: Long = 0L,
  val appPssMemory: Long = 0L,
  val systemTotalMemory: Long = 0L,
  val systemAvailableMemory: Long = 0L,
  val isLowMemory: Boolean = false,
  val runtimeUsedMemory: Long = 0L,
  val runtimeMaxMemory: Long = 0L,
  val runtimeFreeMemory: Long = 0L,
  val modelMemoryUsage: Long = 0L
)
