package com.aispeech.modellibs.cleanup

import android.annotation.SuppressLint
import android.content.Context
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkInfo
import androidx.work.WorkManager
import com.aispeech.aibase.AILog
import java.util.concurrent.TimeUnit

/**
 * 任务清理调度器
 * 
 * 负责配置和管理 WorkManager 的定期清理任务。
 * 确保系统中只有一个清理任务计划，并在合适的时机执行。
 */
object TaskCleanupScheduler {
    
    private const val TAG = "TaskCleanupScheduler"
    
    /**
     * 初始化并调度清理任务
     * 
     * @param context 应用上下文
     */
    @SuppressLint("IdleBatteryChargingConstraints")
    fun scheduleCleanupTask(context: Context) {
        try {
            AILog.i(TAG, "Scheduling task cleanup work...")
            
            // 设备空闲且充电时执行
            val constraints = Constraints.Builder()
                .setRequiresDeviceIdle(true)
                .setRequiresCharging(true)
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
                .build()
            
            // 周期性工作请求
            val cleanupWorkRequest = PeriodicWorkRequestBuilder<TaskCleanupWorker>(
                repeatInterval = CleanupConfig.CLEANUP_INTERVAL_MS,
                repeatIntervalTimeUnit = TimeUnit.MILLISECONDS
            )
                .setConstraints(constraints)
                .addTag(CleanupConfig.CLEANUP_WORK_TAG)
                .build()
            
            WorkManager.getInstance(context).enqueueUniquePeriodicWork(
                CleanupConfig.CLEANUP_WORK_NAME,
                ExistingPeriodicWorkPolicy.KEEP,
                cleanupWorkRequest
            )
            
            AILog.i(TAG, "Task cleanup work scheduled successfully with ${CleanupConfig.CLEANUP_INTERVAL_MS}ms interval")
            
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to schedule task cleanup work", e)
        }
    }
    
    /**
     * 取消清理任务
     * 
     * 在需要停止清理任务时调用，例如应用卸载或服务停止时。
     * 
     * @param context 应用上下文
     */
    fun cancelCleanupTask(context: Context) {
        try {
            AILog.i(TAG, "Cancelling task cleanup work...")
            
            WorkManager.getInstance(context).cancelUniqueWork(CleanupConfig.CLEANUP_WORK_NAME)
            
            AILog.i(TAG, "Task cleanup work cancelled successfully")
            
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to cancel task cleanup work", e)
        }
    }

    /**
     * 立即执行一次清理任务
     *
     * 用于测试或手动触发清理。
     *
     * @param context 应用上下文
     */
    fun executeCleanupNow(context: Context) {
        try {
            AILog.i(TAG, "Executing immediate task cleanup...")

            val immediateCleanupRequest = OneTimeWorkRequestBuilder<TaskCleanupWorker>()
                .addTag(CleanupConfig.CLEANUP_WORK_TAG)
                .build()

            WorkManager.getInstance(context).enqueue(immediateCleanupRequest)

            AILog.i(TAG, "Immediate task cleanup enqueued successfully")

        } catch (e: Exception) {
            AILog.e(TAG, "Failed to execute immediate task cleanup", e)
        }
    }
    
    /**
     * 获取清理任务的工作状态
     * 
     * @param context 应用上下文
     * @return 工作状态的 LiveData
     */
    fun getCleanupWorkStatus(context: Context) = 
        WorkManager.getInstance(context).getWorkInfosForUniqueWorkLiveData(CleanupConfig.CLEANUP_WORK_NAME)
    
    /**
     * 检查清理任务是否已调度
     * 
     * @param context 应用上下文
     * @param callback 回调函数，参数为是否已调度
     */
    fun isCleanupTaskScheduled(context: Context, callback: (Boolean) -> Unit) {
        try {
            val workInfosFuture = WorkManager.getInstance(context)
                .getWorkInfosForUniqueWork(CleanupConfig.CLEANUP_WORK_NAME)
            
            workInfosFuture.addListener({
                try {
                    val workInfos = workInfosFuture.get()
                    val isScheduled = workInfos.any { workInfo ->
                        workInfo.state == WorkInfo.State.ENQUEUED || workInfo.state == WorkInfo.State.RUNNING
                    }
                    callback(isScheduled)
                } catch (e: Exception) {
                    AILog.e(TAG, "Error checking cleanup task status", e)
                    callback(false)
                }
            }, { it.run() })
            
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to check cleanup task status", e)
            callback(false)
        }
    }
}