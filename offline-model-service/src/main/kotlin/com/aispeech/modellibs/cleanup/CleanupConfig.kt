package com.aispeech.modellibs.cleanup

/**
 * 任务清理配置常量
 * 
 * 定义清理任务的相关配置参数，包括数据保留期、清理周期等。
 */
object CleanupConfig {
    
    /**
     * 数据保留期（毫秒）
     * 默认 7 天，只清理早于此时间的已完成和已失败任务
     */
    const val DATA_RETENTION_PERIOD_MS = 7L * 24L * 60L * 60L * 1000L // 7天
    
    /**
     * 清理任务执行周期（毫秒）
     * 默认 24 小时执行一次
     */
    const val CLEANUP_INTERVAL_MS = 24L * 60L * 60L * 1000L // 24小时
    
    /**
     * WorkManager 清理任务的唯一标识
     */
    const val CLEANUP_WORK_NAME = "task_cleanup_work"
    
    /**
     * 清理任务标签，用于标识和管理
     */
    const val CLEANUP_WORK_TAG = "task_cleanup"
    
    /**
     * 清理任务超时时间（毫秒）
     * 防止清理任务执行时间过长
     */
    const val CLEANUP_TIMEOUT_MS = 10L * 60L * 1000L // 10分钟
}