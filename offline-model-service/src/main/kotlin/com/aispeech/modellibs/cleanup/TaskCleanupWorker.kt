package com.aispeech.modellibs.cleanup

import android.content.Context
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.aispeech.aibase.AILog
import com.aispeech.modellibs.TaskStatus
import com.aispeech.modellibs.database.TaskDao
import com.aispeech.modellibs.database.TaskDatabase
import com.aispeech.modellibs.task.TaskFileManager
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout

/**
 * 任务清理 Worker
 * 
 * 定期清理过期的已完成和已失败任务，包括数据库记录和关联文件。
 * 采用 WorkManager 实现，在设备空闲且充电时执行，最大程度降低对用户体验的影响。
 */
class TaskCleanupWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {

    companion object {
        private const val TAG = "TaskCleanupWorker"
    }

    private val taskDao: TaskDao by lazy {
        TaskDatabase.getInstance(applicationContext).taskDao()
    }

    private val fileManager: TaskFileManager by lazy {
        TaskFileManager(applicationContext)
    }

    /**
     * 执行清理任务
     * 
     * 清理流程：
     * 1. 计算过期时间点（当前时间 - 数据保留期）
     * 2. 查询过期的终态任务（COMPLETED、FAILED）
     * 3. 先清理文件：删除每个任务关联的本地文件
     * 4. 后清理数据库：批量删除任务记录
     * 5. 报告清理结果
     */
    override suspend fun doWork(): Result {
        return try {
            withTimeout(CleanupConfig.CLEANUP_TIMEOUT_MS) {
                performCleanup()
            }
        } catch (e: Exception) {
            AILog.e(TAG, "Task cleanup failed", e)
            Result.retry()
        }
    }

    /**
     * 执行具体的清理逻辑
     */
    private suspend fun performCleanup(): Result = withContext(Dispatchers.IO) {
        try {
            AILog.i(TAG, "Starting task cleanup process...")

            // 计算过期时间点
            val currentTime = System.currentTimeMillis()
            val expirationTime = currentTime - CleanupConfig.DATA_RETENTION_PERIOD_MS

            AILog.i(TAG, "Cleanup expiration time: $expirationTime (${CleanupConfig.DATA_RETENTION_PERIOD_MS}ms ago)")

            // 查询过期的终态任务
            val expiredTasks = getExpiredFinishedTasks(expirationTime)
            
            if (expiredTasks.isEmpty()) {
                AILog.i(TAG, "No expired tasks found, cleanup completed")
                return@withContext Result.success()
            }

            AILog.i(TAG, "Found ${expiredTasks.size} expired tasks to cleanup")

            // 3. 先清理文件
            val fileCleanupResult = cleanupTaskFiles(expiredTasks)
            AILog.i(TAG, "File cleanup completed: ${fileCleanupResult.successCount} success, ${fileCleanupResult.failureCount} failed")

            // 4. 后清理数据库
            val dbCleanupResult = cleanupDatabaseRecords(expirationTime)
            AILog.i(TAG, "Database cleanup completed: $dbCleanupResult records deleted")

            // 5. 报告清理结果
            AILog.i(TAG, "Task cleanup process completed successfully. Files: ${fileCleanupResult.successCount}/${expiredTasks.size}, DB records: $dbCleanupResult")

            Result.success()

        } catch (e: Exception) {
            AILog.e(TAG, "Error during cleanup process", e)
            Result.retry()
        }
    }

    /**
     * 查询过期的已完成任务
     * 
     * @param expirationTime 过期时间点
     * @return 过期的任务列表
     */
    private suspend fun getExpiredFinishedTasks(expirationTime: Long): List<String> {
        return try {
            taskDao.getExpiredFinishedTaskIds(expirationTime)
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to query expired tasks", e)
            emptyList()
        }
    }

    /**
     * 清理任务文件
     * 
     * @param taskIds 要清理的任务ID列表
     * @return 清理结果统计
     */
    private suspend fun cleanupTaskFiles(taskIds: List<String>): FileCleanupResult {
        var successCount = 0
        var failureCount = 0

        for (taskId in taskIds) {
            try {
                val deleted = fileManager.deleteTaskFiles(taskId)
                if (deleted) {
                    successCount++
                    AILog.i(TAG, "Successfully deleted files for task: $taskId")
                } else {
                    failureCount++
                    AILog.w(TAG, "Failed to delete files for task: $taskId")
                }
            } catch (e: Exception) {
                failureCount++
                AILog.w(TAG, "Exception while deleting files for task: $taskId, exception: ${e.message}")
            }
        }

        return FileCleanupResult(successCount, failureCount)
    }

    /**
     * 清理数据库记录
     * 
     * @param expirationTime 过期时间点
     * @return 删除的记录数量
     */
    private suspend fun cleanupDatabaseRecords(expirationTime: Long): Int {
        return try {
            taskDao.cleanupExpiredTasks(expirationTime)
        } catch (e: Exception) {
            AILog.e(TAG, "Failed to cleanup database records", e)
            0
        }
    }

    /**
     * 文件清理结果
     */
    private data class FileCleanupResult(
        val successCount: Int,
        val failureCount: Int
    )
}

/**
 * 查询过期的已完成任务ID
 */
private suspend fun TaskDao.getExpiredFinishedTaskIds(expirationTime: Long): List<String> {
    val allTasks = getAllTasks()
    return allTasks.filter { task ->
        task.updatedAt < expirationTime && 
        (task.status == TaskStatus.COMPLETED || task.status == TaskStatus.FAILED)
    }.map { it.taskId }
}