package com.aispeech.modellibs.task

import android.os.ParcelFileDescriptor


// 用于标记需要 PFD 的任务
interface PfdTask {
  val inputPfd: ParcelFileDescriptor
  val dataSize: Int
}

// 用于标记使用文本的任务
interface TextTask {
  val inputText: String
}


/**
 * 模型任务密封类
 */
sealed class ModelTask(
  val taskId: String
) {

  override fun equals(other: Any?): <PERSON><PERSON>an {
    if (this === other) return true
    if (javaClass != other?.javaClass) return false
    other as ModelTask
    return taskId == other.taskId
  }

  override fun hashCode(): Int {
    return taskId.hashCode()
  }

  class AbstractTask(
    override val inputPfd: ParcelFileDescriptor,
    override val dataSize: Int,
    taskId: String,
  ) : ModelTask(taskId), PfdTask

  class AbstractTaskWithText(
    override val inputText: String,
    taskId: String,
  ) : ModelTask(taskId), TextTask

  class ActionItemTask(
    override val inputPfd: ParcelFileDescriptor,
    override val dataSize: Int,
    taskId: String,
  ) : ModelTask(taskId), PfdTask

  class ActionItemTaskWithText(
    override val inputText: String,
    taskId: String,
  ) : ModelTask(taskId), TextTask
}
