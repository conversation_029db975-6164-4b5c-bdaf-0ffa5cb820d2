package com.aispeech.modellibs.task

import android.os.ParcelFileDescriptor
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.SharedFlow


sealed class TaskEvent {
  abstract val taskId: String

  /**
   * 当一个任务成功提交到队列时发布。
   */
  data class TaskSubmitted(override val taskId: String) : TaskEvent()

  /**
   * 当一个任务从队列中被取出，即将开始执行时发布。
   */
  data class ExecutionStarted(override val taskId: String) : TaskEvent()

  /**
   * 当一个任务成功执行完毕时发布。
   */
  data class ExecutionSucceeded(override val taskId: String, val result: Any) : TaskEvent()

  /**
   * 当一个任务执行失败时发布（不包括取消）。
   */
  data class ExecutionFailed(
    override val taskId: String,
    val errorCode: Int,
    val errorMessage: String
  ) : TaskEvent()

  /**
   * 当一个任务被取消时发布（无论是在队列中还是在执行中）。
   */
  data class Cancelled(override val taskId: String) : TaskEvent()
}

/**
 * 封装了 ModelTaskManager 的可查询状态快照。
 */
data class TaskManagerStatus(
  val queuedTaskCount: Int,
  val isExecuting: Boolean,
  val executingTaskId: String?,
  val queuedTaskIds: List<String>
)

/**
 * 任务管理器接口
 * 负责任务队列管理和执行
 */
interface ITaskManager : CoroutineScope {

  /**
   * 任务事件流
   */
  val taskEvents: SharedFlow<TaskEvent>

  /**
   * 提交文本摘要任务（PFD方式）
   */
  fun submitAbstractTask(
    inputPfd: ParcelFileDescriptor,
    dataSize: Int,
    taskId: String,
  ): String

  /**
   * 提交文本摘要任务（文本方式）
   */
  fun submitAbstractTaskWithText(
    inputText: String,
    taskId: String,
  ): String

  /**
   * 提交待办事项检测任务（PFD方式）
   */
  fun submitActionItemTask(
    inputPfd: ParcelFileDescriptor,
    dataSize: Int,
    taskId: String,
  ): String

  /**
   * 提交待办事项检测任务（文本方式）
   */
  fun submitActionItemTaskWithText(
    inputText: String,
    taskId: String,
  ): String

  /**
   * 取消任务
   */
  fun cancelTask(taskId: String): Boolean

  /**
   * 获取任务管理器状态
   */
  suspend fun getStatus(): TaskManagerStatus

  /**
   * 关闭任务管理器
   */
  fun shutdown()
}
