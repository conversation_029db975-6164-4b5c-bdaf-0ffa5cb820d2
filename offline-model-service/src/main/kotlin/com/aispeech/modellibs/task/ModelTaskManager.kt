package com.aispeech.modellibs.task

import android.os.ParcelFileDescriptor
import com.aispeech.aibase.AILog
import com.aispeech.modellibs.ConfigurationManager
import com.aispeech.modellibs.CoroutineScopeManager
import com.aispeech.modellibs.engine.ILLMEngineManager
import com.aispeech.modellibs.engine.IModelEngine
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import java.io.FileInputStream
import java.nio.charset.StandardCharsets
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicReference
import kotlin.coroutines.cancellation.CancellationException


/**
 * 错误代码常量
 */
object ErrorCodes {
  const val READ_INPUT_FAILED = 1001
  const val PROCESSING_FAILED = 1002
  const val INTERNAL_ERROR = 1003
  const val SERVICE_SHUTDOWN = 1004
  const val PROCESSING_TIMEOUT = 1005
  const val QUEUE_FULL = 1006
  const val TASK_CANCELLED = 1007
  const val INVALID_PARAMETER = 1008
}

// 任务队列满了
class QueueFullException(override val message: String?): Exception(message)

/**
 * 模型任务管理器
 * 负责管理任务队列，因为 ModelEngine 只支持单个任务处理
 * 实现 CoroutineScope 接口，使用结构化并发管理协程生命周期
 */
class ModelTaskManager(
  private val modelEngine: IModelEngine,
  private val engineManager: ILLMEngineManager,
): ITaskManager, CoroutineScope {

  companion object {
    const val TAG = "ModelTaskManager"
  }

  private val taskQueue = LinkedBlockingQueue<ModelTask>(ConfigurationManager.getMaxQueueSize())
  private val currentExecutingTask = AtomicReference<Pair<String, Job>?>(null)

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  // 对外
  private val _taskEvents = MutableSharedFlow<TaskEvent>(extraBufferCapacity = 10)
  override val taskEvents = _taskEvents.asSharedFlow()

  init {
    // 启动任务处理协程
    launch {
      processTasksLoop()
    }
  }

  /**
   * 通用任务提交函数
   *
   * @param taskId 可选的任务ID，如果为null则自动生成
   * @param taskFactory 任务创建工厂函数
   * @return 最终使用的任务ID
   */
  private fun submitTaskGeneric(
    taskId: String,
    taskFactory: (String) -> ModelTask
  ): String {
    val task = taskFactory(taskId)
    submitTask(task)
    return taskId
  }

  /**
   * 提交文本摘要任务（PFD方式）
   */
  override fun submitAbstractTask(
    inputPfd: ParcelFileDescriptor,
    dataSize: Int,
    taskId: String,
  ): String {
    return submitTaskGeneric(taskId) { finalTaskId ->
      ModelTask.AbstractTask(inputPfd, dataSize, finalTaskId)
    }
  }

  /**
   * 提交文本摘要任务（文本方式）
   */
  override fun submitAbstractTaskWithText(
    inputText: String,
    taskId: String,
  ): String {
    return submitTaskGeneric(taskId) { finalTaskId ->
      ModelTask.AbstractTaskWithText(inputText, finalTaskId)
    }
  }

  /**
   * 提交待办事项检测任务（PFD方式）
   */
  override fun submitActionItemTask(
    inputPfd: ParcelFileDescriptor,
    dataSize: Int,
    taskId: String,
  ): String {
    return submitTaskGeneric(taskId) { finalTaskId ->
      ModelTask.ActionItemTask(inputPfd, dataSize, finalTaskId)
    }
  }

  /**
   * 提交待办事项检测任务（文本方式）
   */
  override fun submitActionItemTaskWithText(
    inputText: String,
    taskId: String,
  ): String {
    return submitTaskGeneric(taskId) { finalTaskId ->
      ModelTask.ActionItemTaskWithText(inputText, finalTaskId)
    }
  }

  /**
   * 通过 ID 取消任务。
   */
  override fun cancelTask(taskId: String): Boolean {
    currentExecutingTask.get()?.let { (currentId, currentJob) ->
      if (currentId == taskId) {
        AILog.i(TAG, "Cancelling EXECUTING task. ID: $taskId")
        currentJob.cancel(CancellationException("Task cancelled by ID while executing."))
        return true
      }
    }

    val taskToCancel = taskQueue.find { it.taskId == taskId }
    if (taskToCancel != null && taskQueue.remove(taskToCancel)) {
      AILog.i(TAG, "Cancelled QUEUED task. ID: $taskId")
      launch { _taskEvents.emit(TaskEvent.Cancelled(taskId)) }
      return true
    }

    AILog.w(TAG, "Cannot cancel task. No active or queued task found for ID: $taskId")
    return false
  }

  /**
   * 获取 ModelTaskManager 的当前状态快照。
   */
  override suspend fun getStatus(): TaskManagerStatus {
    val queuedTasks = taskQueue.toList()
    val executingTaskInfo = currentExecutingTask.get()
    return TaskManagerStatus(
      queuedTaskCount = queuedTasks.size,
      isExecuting = executingTaskInfo != null,
      executingTaskId = executingTaskInfo?.first,
      queuedTaskIds = queuedTasks.map { it.taskId }
    )
  }


  /**
   * 提交任务
   */
  private fun submitTask(task: ModelTask) {
    if (!taskQueue.offer(task)) {
      AILog.w(TAG, "Failed to submit task, queue is full. ID: ${task.taskId}")
      throw QueueFullException("Task queue is full")
    }

    AILog.i(TAG, "Task submitted to queue. ID: ${task.taskId}, Queue size: ${taskQueue.size}")

    // 异步发送成功事件
    launch {
      _taskEvents.emit(TaskEvent.TaskSubmitted(task.taskId))
    }
  }

  /**
   * 任务处理循环
   */
  private suspend fun processTasksLoop() {
    AILog.i(TAG, "Task processing loop started. Waiting for tasks...")
    try {
      while (coroutineContext.isActive) {
        // 获取第一个任务
        val firstTask = try {
          withContext(Dispatchers.IO) { taskQueue.take() }
        } catch (e: InterruptedException) {
          AILog.i(TAG, "Task loop interrupted. Shutting down.")
          break
        } catch (e: CancellationException) {
          AILog.i(TAG, "Task loop cancelled. Shutting down.")
          break
        }

        // 确保服务启动
        val serviceReady = engineManager.acquireEngineService()
        if (!serviceReady) {
          handleEngineServiceFailure(firstTask)
          continue
        }

        // 处理第一个任务
        processSingleTaskWithCleanup(firstTask)

        // 处理剩余任务
        processRemainingTasksInBatch()

        AILog.i(TAG, "Stopping engine service due to idle queue.")
        engineManager.releaseEngineService()
      }
    } finally {
      AILog.i(TAG, "Task loop is ending. Performing final cleanup...")
      engineManager.forceDestroy()
      taskQueue.clear()
      AILog.i(TAG, "ModelTaskManager final cleanup completed.")
    }
    AILog.i(TAG, "Task processing loop has ended.")
  }

  /**
   * 处理引擎服务启动失败
   */
  private suspend fun handleEngineServiceFailure(firstTask: ModelTask) {
    AILog.e(TAG, "Engine service failed to start. Failing task ${firstTask.taskId} and clearing queue.")

    _taskEvents.emit(TaskEvent.ExecutionFailed(
      firstTask.taskId,
      ErrorCodes.INTERNAL_ERROR,
      "Engine service failed to start."
    ))

    val remainingTasks = mutableListOf<ModelTask>()
    taskQueue.drainTo(remainingTasks)
    for (task in remainingTasks) {
      _taskEvents.emit(TaskEvent.ExecutionFailed(
        task.taskId,
        ErrorCodes.INTERNAL_ERROR,
        "Engine service failed to start."
      ))
    }
  }

  /**
   * 批量处理剩余任务
   */
  private suspend fun processRemainingTasksInBatch() {
    while (coroutineContext.isActive) {
      val nextTask = taskQueue.poll()
      if (nextTask != null) {
        AILog.i(TAG, "Processing next task from queue without stopping engine.")
        processSingleTaskWithCleanup(nextTask)
      } else {
        AILog.i(TAG, "Task queue is now empty.")
        break
      }
    }
  }

  /**
   * 封装了单个任务的完整处理流程，包括状态设置和清理。
   * @param task 要处理的任务
   */
  private suspend fun processSingleTaskWithCleanup(task: ModelTask) {
    val taskJob = Job(coroutineContext[Job])
    currentExecutingTask.set(task.taskId to taskJob)
    try {
      withContext(taskJob) {
        processTask(task)
      }
    } catch (e: CancellationException) {
      AILog.i(TAG, "Task ${task.taskId} was cancelled externally.")
      if (!_taskEvents.tryEmit(TaskEvent.Cancelled(task.taskId))) {
        _taskEvents.emit(TaskEvent.Cancelled(task.taskId))
      }
    } catch (e: Exception) {
      AILog.e(TAG, "An unexpected error occurred while launching task ${task.taskId}", e)
      _taskEvents.emit(TaskEvent.ExecutionFailed(task.taskId, ErrorCodes.INTERNAL_ERROR, "Task launcher failed: ${e.message}"))
    } finally {
      currentExecutingTask.set(null)
    }
  }


  /**
   * 处理单个任务
   */
  private suspend fun processTask(task: ModelTask) {
    _taskEvents.emit(TaskEvent.ExecutionStarted(task.taskId))
    AILog.i(TAG, "Task execution started. ID: ${task.taskId}")
    try {
      val result = withTimeout(ConfigurationManager.getTaskProcessingTimeout()) {
        processTaskInternal(task)
      }

      if (result != null) {
        _taskEvents.emit(TaskEvent.ExecutionSucceeded(task.taskId, result))
        AILog.i(TAG, "Task ${task.taskId} completed successfully, result length: ${result.length}")
      } else {
        val taskTypeName = when (task) {
          is ModelTask.AbstractTask, is ModelTask.AbstractTaskWithText -> "摘要提取"
          is ModelTask.ActionItemTask, is ModelTask.ActionItemTaskWithText -> "待办事项检测"
          else -> "未知任务"
        }
        val errorMessage =
          "${taskTypeName}引擎返回空结果，可能原因：模型未正确初始化、输入文本格式不符合要求、或模型处理失败"

        _taskEvents.emit(
          TaskEvent.ExecutionFailed(
            task.taskId,
            ErrorCodes.PROCESSING_FAILED,
            errorMessage
          )
        )
        AILog.w(TAG, "Task ${task.taskId} failed: $errorMessage")
      }
    } catch (e: Exception) {
      when (e) {
        is CancellationException -> {
          _taskEvents.emit(TaskEvent.Cancelled(task.taskId))
          throw e
        }

        else -> {
          _taskEvents.emit(
            TaskEvent.ExecutionFailed(
              task.taskId,
              ErrorCodes.PROCESSING_FAILED,
              e.message ?: "未知错误"
            )
          )
        }
      }
    }
  }


  /**
   * 内部任务处理逻辑
   */
  private suspend fun processTaskInternal(task: ModelTask): String? {
    val inputText = when (task) {
      is PfdTask -> task.inputPfd.use { pfd -> readTextFromPfd(pfd, task.dataSize) }
      is TextTask -> task.inputText
      else -> {
        AILog.e(TAG, "Unhandled task type: ${task.javaClass.simpleName}")
        error("Unhandled task type")
      }
    } ?: error("Failed to read input data")

    AILog.i(TAG, "Processing task ${task.taskId}, input text length: ${inputText.length}")

    return when (task) {
      is ModelTask.AbstractTask, is ModelTask.AbstractTaskWithText -> {
        val result = modelEngine.extractAbstract(inputText)
        if (result == null) {
          AILog.w(TAG, "extractAbstract returned null for task ${task.taskId}")
        }
        result
      }

      is ModelTask.ActionItemTask, is ModelTask.ActionItemTaskWithText -> {
        val result = modelEngine.detectActionItem(inputText)
        if (result == null) {
          AILog.w(TAG, "detectActionItem returned null for task ${task.taskId}")
        }
        result
      }
    }
  }

  /**
   * 从 ParcelFileDescriptor 读取文本数据
   */
  private fun readTextFromPfd(pfd: ParcelFileDescriptor, dataSize: Int): String? {
    return try {
      FileInputStream(pfd.fileDescriptor).use { inputStream ->
        val buffer = ByteArray(dataSize)
        var totalRead = 0

        while (totalRead < dataSize) {
          val bytesRead = inputStream.read(buffer, totalRead, dataSize - totalRead)
          if (bytesRead == -1) break
          totalRead += bytesRead
        }

        if (totalRead > 0) {
          String(buffer, 0, totalRead, StandardCharsets.UTF_8)
        } else {
          null
        }
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Error reading text from ParcelFileDescriptor", e)
      null
    }
  }

  /**
   * 关闭任务管理器。
   */
  override fun shutdown() {
    AILog.i(TAG, "Shutting down ModelTaskManager...")
    scopeDelegate.shutdown("ModelTaskManager shutdown")
    AILog.i(TAG, "ModelTaskManager shutdown completed.")
  }

}

