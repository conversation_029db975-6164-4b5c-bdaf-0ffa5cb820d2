package com.aispeech.modellibs.task

import android.os.ParcelFileDescriptor
import com.aispeech.aibase.AILog
import com.aispeech.modellibs.CoroutineScopeManager
import com.aispeech.modellibs.TaskStatus
import com.aispeech.modellibs.TaskTypeByte
import com.aispeech.modellibs.database.TaskDao
import com.aispeech.modellibs.database.TaskEntity
import com.aispeech.modellibs.engine.ILLMEngineManager
import com.aispeech.modellibs.engine.IModelEngine
import com.aispeech.modellibs.fromDatabaseValueToTaskTypeByte
import com.aispeech.modellibs.toApiKey
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import java.util.concurrent.atomic.AtomicReference
import kotlin.coroutines.cancellation.CancellationException

/**
 * 基于数据库的任务管理器
 */
class DatabaseTaskManager(
  private val taskDao: TaskDao,
  private val fileManager: TaskFileManager,
  private val modelEngine: IModelEngine,
  private val engineManager: ILLMEngineManager
) : ITaskManager, CoroutineScope {

  companion object {
    const val TAG = "DatabaseTaskManager"
    private const val MINUTE_MS = 60 * 1_000
    private const val TASK_POLL_INTERVAL_MS = 1000L
    private const val TASK_PROCESSING_TIMEOUT_MS = 45L * MINUTE_MS
  }

  // 协程作用域委托
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  override val coroutineContext = scopeDelegate.coroutineContext

  // 当前执行的任务
  private val currentExecutingTask = AtomicReference<Pair<String, Job>?>(null)

  // 任务事件流（保持兼容性）
  private val _taskEvents = MutableSharedFlow<TaskEvent>(extraBufferCapacity = 10)
  override val taskEvents = _taskEvents.asSharedFlow()

  init {
    // 启动任务处理协程
    launch {
      cleanupOrphanedExecutingTasks()

      processTasksLoop()
    }
  }

  /**
   * 清理孤立的执行中任务
   * 在服务启动时调用，将所有 EXECUTING 状态的任务标记为失败
   * 这种情况通常发生在服务崩溃重启后，避免任务永远卡在执行状态
   */
  private suspend fun cleanupOrphanedExecutingTasks() {
    try {
      val executingTasks = taskDao.getExecutingTasks()
      if (executingTasks.isNotEmpty()) {
        AILog.w(TAG, "Found ${executingTasks.size} orphaned executing tasks, marking as failed")

        // 清理每个孤立任务的关联文件
        executingTasks.forEach { task ->
          try {
            fileManager.deleteTaskFiles(task.taskId)
            AILog.i(TAG, "Cleaned up files for orphaned task: ${task.taskId}")
          } catch (e: Exception) {
            AILog.i(TAG, "Failed to cleanup files for orphaned task: ${task.taskId}, error: ${e.message}")
          }
        }

        val updatedCount = taskDao.markExecutingTasksAsFailed("服务重启，任务被中断")
        AILog.i(TAG, "Marked $updatedCount executing tasks as failed")

        // 发送失败事件通知
        executingTasks.forEach { task ->
          _taskEvents.tryEmit(TaskEvent.ExecutionFailed(
            task.taskId,
            ErrorCodes.INTERNAL_ERROR,
            "服务重启，任务被中断"
          ))
        }
      } else {
        AILog.i(TAG, "No orphaned executing tasks found")
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Error cleaning up orphaned executing tasks", e)
    }
  }

  /**
   * 主任务处理循环
   */
  private suspend fun processTasksLoop() {
    AILog.i(TAG, "Task processing loop started")

    try {
      while (coroutineContext.isActive) {
        try {
          val taskProcessed = processNextAvailableTask()
          if (!taskProcessed) {
            delay(TASK_POLL_INTERVAL_MS)
          }
        } catch (e: CancellationException) {
          AILog.i(TAG, "Task processing loop cancelled")
          throw e
        } catch (e: Exception) {
          AILog.e(TAG, "Error in task processing loop", e)
          delay(TASK_POLL_INTERVAL_MS)
        }
      }
    } finally {
      AILog.i(TAG, "Task processing loop ending, performing cleanup...")
      engineManager.forceDestroy()
      AILog.i(TAG, "DatabaseTaskManager cleanup completed")
    }
  }

  /**
   * 处理下一个可用任务
   * @return true 如果处理了任务，false 如果没有可处理的任务
   */
  private suspend fun processNextAvailableTask(): Boolean {
    // 从数据库获取下一个待执行的任务
    val nextTask = taskDao.getNextQueuedTask() ?: return false

    // 原子性地将任务状态从 QUEUED 更新为 EXECUTING
    val updateResult = taskDao.atomicUpdateQueuedToExecuting(nextTask.taskId)
    if (updateResult == 0) {
      AILog.i(TAG, "Task ${nextTask.taskId} already taken by another worker")
      return false
    }

    AILog.i(TAG, "Starting to process task: ${nextTask.taskId}")

    // 获取引擎服务
    engineManager.acquireEngineService()

    // 处理单个任务
    processSingleTaskWithCleanup(nextTask)

    // 检查是否还有待处理的任务，如果没有则释放引擎
    val queuedCount = taskDao.countQueuedTasks()
    if (queuedCount == 0) {
      AILog.i(TAG, "No more queued tasks, releasing engine service")
      engineManager.releaseEngineService()
    }

    return true
  }


  /**
   * 处理单个任务（包含完整的生命周期管理）
   */
  private suspend fun processSingleTaskWithCleanup(taskEntity: TaskEntity) {
    val taskJob = Job(coroutineContext[Job])
    currentExecutingTask.set(taskEntity.taskId to taskJob)

    try {
      withContext(taskJob) {
        processTask(taskEntity)
      }
    } catch (e: CancellationException) {
      AILog.i(TAG, "Task ${taskEntity.taskId} was cancelled")
      taskDao.updateTaskStatusWithError(
        taskEntity.taskId,
        TaskStatus.CANCELLED,
        "Task was cancelled"
      )
      _taskEvents.tryEmit(TaskEvent.Cancelled(taskEntity.taskId))
    } catch (e: Exception) {
      AILog.i(TAG, "Unexpected error while processing task ${taskEntity.taskId} exception: ${e.message}")
      taskDao.updateTaskStatusWithError(
        taskEntity.taskId,
        TaskStatus.FAILED,
        "Task processing failed: ${e.message}"
      )
      _taskEvents.tryEmit(TaskEvent.ExecutionFailed(
        taskEntity.taskId,
        ErrorCodes.INTERNAL_ERROR,
        "Task processing failed: ${e.message}"
      ))
    } finally {
      currentExecutingTask.set(null)
      // 清理任务文件
      try {
        fileManager.deleteTaskFiles(taskEntity.taskId)
        AILog.i(TAG, "Cleaned up files for task: ${taskEntity.taskId}")
      } catch (e: Exception) {
        AILog.i(TAG, "Failed to cleanup files for task: ${taskEntity.taskId} exception: ${e.message}")
      }
    }
  }

  /**
   * 执行具体的任务处理逻辑
   */
  private suspend fun processTask(taskEntity: TaskEntity) {
    AILog.i(TAG, "Processing task: ${taskEntity.taskId}, type: ${taskEntity.getReadableTaskType()}")

    // 发送开始执行事件
    _taskEvents.tryEmit(TaskEvent.ExecutionStarted(taskEntity.taskId))

    try {
      // 从文件读取输入数据
      val inputText = fileManager.readInputFile(taskEntity.inputDataPath)
        ?: error("Failed to read input data from file: ${taskEntity.inputDataPath}")

      AILog.i(TAG, "Task content take ${inputText.take(50)} process before")

      // 从数据库映射成 TaskTypeByte
      val taskTypeFromDb: Int = taskEntity.taskType
      val taskType: TaskTypeByte? = fromDatabaseValueToTaskTypeByte(taskTypeFromDb)

      AILog.i(TAG, "Task type ${taskType?.toApiKey()}")

      // 根据任务类型调用相应的模型引擎方法
      val result = withTimeoutOrNull(TASK_PROCESSING_TIMEOUT_MS) {
        taskType?.let { modelEngine.process(it, inputText) }
      }

      // 处理执行结果
      if (!result.isNullOrEmpty()) {

        taskDao.updateTaskStatusWithResult(
          taskEntity.taskId,
          TaskStatus.COMPLETED,
          result
        )

        // 发送成功事件
        val success = _taskEvents.tryEmit(TaskEvent.ExecutionSucceeded(taskEntity.taskId, result))

        AILog.i(TAG, "Task ${taskEntity.taskId} completed successfully, result length: ${result.length}, success: $success")
      } else {
        // 引擎返回空结果
        val errorMessage = "${taskEntity.getReadableTaskType()}引擎返回空结果"

        taskDao.updateTaskStatusWithError(
          taskEntity.taskId,
          TaskStatus.FAILED,
          errorMessage
        )

        _taskEvents.tryEmit(TaskEvent.ExecutionFailed(
          taskEntity.taskId,
          ErrorCodes.PROCESSING_FAILED,
          errorMessage
        ))

        AILog.i(TAG, "Task ${taskEntity.taskId} failed: $errorMessage")
      }

    } catch (e: Exception) {
      when (e) {
        is CancellationException -> {
          throw e
        }
        else -> {
          val errorMessage = e.message ?: "Unknown error"

          taskDao.updateTaskStatusWithError(
            taskEntity.taskId,
            TaskStatus.FAILED,
            errorMessage
          )

          _taskEvents.tryEmit(TaskEvent.ExecutionFailed(
            taskEntity.taskId,
            ErrorCodes.PROCESSING_FAILED,
            errorMessage
          ))

          AILog.e(TAG, "Task ${taskEntity.taskId} execution failed", e)
        }
      }
    }
  }

  // ============ ITaskManager 接口实现 ============

  /**
   * 这些方法在新架构中不再使用，任务提交由 TaskDispatcher 处理
   */

  override fun submitAbstractTask(inputPfd: ParcelFileDescriptor, dataSize: Int, taskId: String): String {
    AILog.w(TAG, "submitAbstractTask called but not used in new architecture")
    return taskId
  }

  override fun submitAbstractTaskWithText(inputText: String, taskId: String): String {
    AILog.w(TAG, "submitAbstractTaskWithText called but not used in new architecture")
    return taskId
  }

  override fun submitActionItemTask(inputPfd: ParcelFileDescriptor, dataSize: Int, taskId: String): String {
    AILog.w(TAG, "submitActionItemTask called but not used in new architecture")
    return taskId
  }

  override fun submitActionItemTaskWithText(inputText: String, taskId: String): String {
    AILog.w(TAG, "submitActionItemTaskWithText called but not used in new architecture")
    return taskId
  }

  /**
   * 取消任务
   */
  override fun cancelTask(taskId: String): Boolean {
    return try {
      launch {
        val cancelledRows = taskDao.cancelQueuedTask(taskId)
        if (cancelledRows > 0) {
          AILog.i(TAG, "Task cancelled: $taskId")
          fileManager.deleteTaskFiles(taskId)
          _taskEvents.tryEmit(TaskEvent.Cancelled(taskId))
        } else {
          // 检查是否是正在执行的任务
          val executingTask = currentExecutingTask.get()
          if (executingTask?.first == taskId) {
            executingTask.second.cancel()
            AILog.i(TAG, "Cancelled executing task: $taskId")
          } else {
            AILog.w(TAG, "Failed to cancel task: $taskId")
          }
        }
      }
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Error cancelling task: $taskId", e)
      false
    }
  }


  /**
   * 获取任务管理器状态
   */
  override suspend fun getStatus(): TaskManagerStatus {
    return try {
      val queuedCount = taskDao.countQueuedTasks()
      val queuedTasks = taskDao.getQueuedTasks()
      val queuedTaskIds = queuedTasks.map { it.taskId }
      val executingTaskId = currentExecutingTask.get()?.first

      TaskManagerStatus(
        queuedTaskCount = queuedCount,
        isExecuting = executingTaskId != null,
        executingTaskId = executingTaskId,
        queuedTaskIds = queuedTaskIds
      )
    } catch (e: Exception) {
      AILog.e(TAG, "Error getting task manager status", e)
      TaskManagerStatus(
        queuedTaskCount = 0,
        isExecuting = false,
        executingTaskId = null,
        queuedTaskIds = emptyList()
      )
    }
  }


  /**
   * 关闭任务管理器
   */
  override fun shutdown() {
    try {
      currentExecutingTask.get()?.second?.cancel()
      scopeDelegate.shutdown()
      AILog.i(TAG, "DatabaseTaskManager shutdown completed")
    } catch (e: Exception) {
      AILog.e(TAG, "Error during DatabaseTaskManager shutdown", e)
    }
  }
}
