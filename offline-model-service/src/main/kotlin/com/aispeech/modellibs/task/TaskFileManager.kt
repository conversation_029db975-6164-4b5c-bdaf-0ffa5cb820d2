package com.aispeech.modellibs.task

import android.content.Context
import android.os.ParcelFileDescriptor
import com.aispeech.aibase.AILog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.InputStream
import java.io.OutputStream
import java.nio.charset.StandardCharsets
import java.util.concurrent.ConcurrentHashMap

/**
 * 任务文件存储管理器
 */
class TaskFileManager(private val context: Context) {

  companion object {
    private const val TAG = "TaskFileManager"
    private const val TASK_DATA_DIR = "task_data"
    private const val INPUT_DIR = "input"
    private const val TEMP_DIR = "temp"
    private const val FILE_EXTENSION = ".txt"
    private const val BUFFER_SIZE = 8192
  }

  // 存储目录
  private val taskDataDir: File
  private val inputDir: File
  private val tempDir: File

  // 文件操作锁（按文件路径）
  private val fileLocks = ConcurrentHashMap<String, Any>()

  init {
    // 初始化存储目录
    taskDataDir = File(context.filesDir, TASK_DATA_DIR)
    inputDir = File(taskDataDir, INPUT_DIR)
    tempDir = File(taskDataDir, TEMP_DIR)

    // 确保目录存在
    createDirectoriesIfNeeded()

    AILog.i(TAG, "TaskFileManager initialized, data dir: ${taskDataDir.absolutePath}")
  }

  /**
   * 创建必要的目录
   */
  private fun createDirectoriesIfNeeded() {
    listOf(taskDataDir, inputDir, tempDir).forEach { dir ->
      if (!dir.exists()) {
        val created = dir.mkdirs()
        AILog.i(TAG, "Directory ${dir.name} created: $created")
      }
    }
  }

  /**
   * 保存文本输入数据到文件
   *
   * @param taskId 任务ID
   * @param inputText 输入文本
   * @return 文件路径，失败时返回null
   */
  suspend fun saveInputText(taskId: String, inputText: String): String? {
    return withContext(Dispatchers.IO) {
      try {
        val filePath = getInputFilePath(taskId)
        val file = File(filePath)

        synchronized(getFileLock(filePath)) {
          file.writeText(inputText, StandardCharsets.UTF_8)
        }

        AILog.i(TAG, "Input text saved for task $taskId, size: ${inputText.length} chars")
        filePath
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to save input text for task $taskId", e)
        null
      }
    }
  }

  /**
   * 保存PFD输入数据到文件
   *
   * @param taskId 任务ID
   * @param inputPfd ParcelFileDescriptor
   * @param dataSize 数据大小
   * @return 文件路径，失败时返回null
   */
  suspend fun saveInputFromPfd(
    taskId: String,
    inputPfd: ParcelFileDescriptor,
    dataSize: Int
  ): String? {
    return withContext(Dispatchers.IO) {
      try {
        val filePath = getInputFilePath(taskId)
        val file = File(filePath)

        synchronized(getFileLock(filePath)) {
          inputPfd.use { pfd ->
            FileInputStream(pfd.fileDescriptor).use { inputStream ->
              FileOutputStream(file).use { outputStream ->
                copyStreamWithSize(inputStream, outputStream, dataSize)
              }
            }
          }
        }

        AILog.i(TAG, "Input data saved from PFD for task $taskId, size: $dataSize bytes")
        filePath
      } catch (e: Exception) {
        AILog.i(TAG, "Failed to save input data from PFD for task $taskId exception: ${e.message}")
        null
      }
    }
  }

  /**
   * 读取输入文件内容
   *
   * @param filePath 文件路径
   * @return 文件内容，失败时返回null
   */
  suspend fun readInputFile(filePath: String): String? {
    return withContext(Dispatchers.IO) {
      try {
        val file = File(filePath)
        if (!file.exists()) {
          AILog.i(TAG, "Input file not found: $filePath")
          return@withContext null
        }

        synchronized(getFileLock(filePath)) {
          file.readText(StandardCharsets.UTF_8)
        }
      } catch (e: Exception) {
        AILog.i(TAG, "Failed to read input file: $filePath, exception: ${e.message}")
        null
      }
    }
  }



  /**
   * 删除任务相关的输入文件
   *
   * @param taskId 任务ID
   * @return 删除是否成功
   */
  suspend fun deleteTaskFiles(taskId: String): Boolean {
    return withContext(Dispatchers.IO) {
      try {
        val inputFile = File(getInputFilePath(taskId))
        var success = true

        if (inputFile.exists()) {
          synchronized(getFileLock(inputFile.absolutePath)) {
            success = inputFile.delete()
          }
        }

        // 清理文件锁
        fileLocks.remove(inputFile.absolutePath)

        AILog.i(TAG, "Task input file deleted for task $taskId, success: $success")
        success
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to delete task input file for task $taskId", e)
        false
      }
    }
  }

  /**
   * 清理过期文件
   *
   * @param beforeTimestamp 清理此时间之前的文件
   * @return 清理的文件数量
   */
  suspend fun cleanupExpiredFiles(beforeTimestamp: Long): Int {
    return withContext(Dispatchers.IO) {
      try {
        var deletedCount = 0

        // 清理输入文件
        deletedCount += cleanupDirectoryFiles(inputDir, beforeTimestamp)

        // 清理临时文件
        deletedCount += cleanupDirectoryFiles(tempDir, beforeTimestamp)

        AILog.i(TAG, "Cleaned up $deletedCount expired files")
        deletedCount
      } catch (e: Exception) {
        AILog.e(TAG, "Failed to cleanup expired files", e)
        0
      }
    }
  }

  // ============ 私有辅助方法 ============

  private fun getInputFilePath(taskId: String): String {
    return File(inputDir, "$taskId$FILE_EXTENSION").absolutePath
  }

  private fun getFileLock(filePath: String): Any {
    return fileLocks.computeIfAbsent(filePath) { Any() }
  }

  private fun copyStreamWithSize(
    inputStream: InputStream,
    outputStream: OutputStream,
    maxSize: Int
  ) {
    val buffer = ByteArray(BUFFER_SIZE)
    var totalRead = 0

    while (totalRead < maxSize) {
      val toRead = minOf(BUFFER_SIZE, maxSize - totalRead)
      val bytesRead = inputStream.read(buffer, 0, toRead)
      if (bytesRead == -1) break

      outputStream.write(buffer, 0, bytesRead)
      totalRead += bytesRead
    }
  }

  private fun cleanupDirectoryFiles(directory: File, beforeTimestamp: Long): Int {
    if (!directory.exists()) return 0

    var deletedCount = 0
    directory.listFiles()?.forEach { file ->
      if (file.isFile && file.lastModified() < beforeTimestamp) {
        synchronized(getFileLock(file.absolutePath)) {
          if (file.delete()) {
            deletedCount++
            fileLocks.remove(file.absolutePath)
          }
        }
      }
    }
    return deletedCount
  }
}
