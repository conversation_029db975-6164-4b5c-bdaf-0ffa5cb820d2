package com.aispeech.modellibs.dispatcher

import android.os.ParcelFileDescriptor
import com.aispeech.modellibs.ITaskSubmitCallback
import com.aispeech.modellibs.TaskRequest
import com.aispeech.modellibs.TaskState

/**
 * 任务调度器接口
 * 负责任务分发、状态管理和任务取消
 */
interface ITaskDispatcher {

  /**
   * 调度摘要提取任务（PFD方式）
   */
  fun dispatchAbstractTask(
    inputPfd: ParcelFileDescriptor,
    dataSize: Int,
    submitCallback: ITaskSubmitCallback
  )

  /**
   * 调度摘要提取任务（文本方式）
   */
  fun dispatchAbstractTaskWithText(
    inputText: String,
    submitCallback: ITaskSubmitCallback
  )

  /**
   * 调度待办事项检测任务（PFD方式）
   */
  fun dispatchActionItemTask(
    inputPfd: ParcelFileDescriptor,
    dataSize: Int,
    submitCallback: ITaskSubmitCallback
  )

  /**
   * 调度待办事项检测任务（文本方式）
   */
  fun dispatchActionItemTaskWithText(
    inputText: String,
    submitCallback: ITaskSubmitCallback
  )

  /**
   * 调度一个通用的模型任务。
   * 这是所有任务的统一入口。
   *
   * @param request 包含 apiKey 和具体输入参数的通用请求对象。
   * @param submitCallback 任务提交回调，用于立即返回 taskId 或提交错误。
   */
  fun dispatchTask(request: TaskRequest, submitCallback: ITaskSubmitCallback)

  /**
   * 取消指定任务
   */
  fun cancelTask(taskId: String): Boolean

  /**
   * 获取任务状态
   */
  fun getTaskState(taskId: String): TaskState?

  /**
   * 关闭调度器
   */
  fun shutdown()
}
