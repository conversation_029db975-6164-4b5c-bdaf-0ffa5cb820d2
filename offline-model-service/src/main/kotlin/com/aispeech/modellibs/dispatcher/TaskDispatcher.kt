package com.aispeech.modellibs.dispatcher

import android.os.ParcelFileDescriptor
import com.aispeech.aibase.AILog
import com.aispeech.modellibs.CoroutineScopeManager
import com.aispeech.modellibs.ITaskSubmitCallback
import com.aispeech.modellibs.TaskRequest
import com.aispeech.modellibs.TaskState
import com.aispeech.modellibs.TaskStatus
import com.aispeech.modellibs.TaskType
import com.aispeech.modellibs.database.TaskDao
import com.aispeech.modellibs.database.TaskEntity
import com.aispeech.modellibs.task.ErrorCodes
import com.aispeech.modellibs.task.TaskFileManager
import com.aispeech.modellibs.toApiKey
import com.aispeech.modellibs.toDatabaseValue
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import java.util.concurrent.atomic.AtomicInteger

/**
 * 任务调度器
 */
class TaskDispatcher(
  private val taskDao: TaskDao,
  private val fileManager: TaskFileManager,
) : IT<PERSON>D<PERSON>patcher, CoroutineScope {

  companion object {
    private const val TAG = "TaskDispatcher"
  }

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  // 任务ID生成器
  private val taskIdGenerator = AtomicInteger(0)


  /**
   * 统一的内部调度核心。
   * 它处理所有任务的共性逻辑：ID生成、数据保存、数据库插入。
   *
   * @param apiKey 用于生成 taskId 和存入数据库的字符串标识。
   * @param taskType 兼容旧的数据库 schema 的整型任务类型。
   * @param submitCallback 任务提交回调。
   * @param saveDataAction 一个挂起函数，负责将具体输入源（String 或 PFD）保存到文件。
   */
  private fun dispatchCore(
    apiKey: String,
    taskType: Int,
    submitCallback: ITaskSubmitCallback,
    saveDataAction: suspend (taskId: String) -> String?
  ) {
    val taskId = generateTaskId(apiKey)
    AILog.i(TAG, "Dispatching core task: $taskId, apiKey: $apiKey")

    launch {
      try {
        // 保存数据操作
        val inputFilePath = saveDataAction(taskId)
        if (inputFilePath == null) {
          submitCallback.onError(ErrorCodes.PROCESSING_FAILED, "Failed to save input data")
          return@launch
        }

        // 在数据库中创建任务记录
        val taskEntity = TaskEntity.create(
          taskId = taskId,
          taskType = taskType,
          inputDataPath = inputFilePath
        )

        val insertResult = taskDao.insertTask(taskEntity)
        if (insertResult <= 0) {
          fileManager.deleteTaskFiles(taskId)
          submitCallback.onError(ErrorCodes.PROCESSING_FAILED, "Failed to create task record")
          return@launch
        }

        // 成功回调
        submitCallback.onSubmitted(taskId)
        AILog.i(TAG, "Task submitted successfully: $taskId, file: $inputFilePath")

      } catch (e: Exception) {
        try {
          fileManager.deleteTaskFiles(taskId)
        } catch (cleanupException: Exception) {
          AILog.i(TAG, "Failed to cleanup files for task $taskId and exception: ${cleanupException.message}")
        }
        submitCallback.onError(ErrorCodes.PROCESSING_FAILED, "任务调度失败: ${e.message}")
        AILog.i(TAG, "Failed to dispatch task $taskId and exception: ${e.message}")
      }
    }
  }

  /**
   * 调度摘要提取任务（PFD方式）
   */
  override fun dispatchAbstractTask(
    inputPfd: ParcelFileDescriptor,
    dataSize: Int,
    submitCallback: ITaskSubmitCallback
  ) {
    val taskType = TaskType.EXTRACT_ABSTRACT
    dispatchCore(
      apiKey = taskType.toApiKey(),
      taskType = taskType.toDatabaseValue(),
      submitCallback = submitCallback
    ) { taskId ->
      fileManager.saveInputFromPfd(taskId, inputPfd, dataSize)
    }
  }

  /**
   * 调度摘要提取任务（文本方式）
   */
  override fun dispatchAbstractTaskWithText(
    inputText: String,
    submitCallback: ITaskSubmitCallback
  ) {
    val taskType = TaskType.EXTRACT_ABSTRACT
    dispatchCore(
      apiKey = taskType.toApiKey(),
      taskType = taskType.toDatabaseValue(),
      submitCallback = submitCallback
    ) { taskId ->
      fileManager.saveInputText(taskId, inputText)
    }
  }

  /**
   * 调度待办事项检测任务（PFD方式）
   */
  override fun dispatchActionItemTask(
    inputPfd: ParcelFileDescriptor,
    dataSize: Int,
    submitCallback: ITaskSubmitCallback
  ) {
    val taskType = TaskType.DETECT_ACTION_ITEM
    dispatchCore(
      apiKey = taskType.toApiKey(),
      taskType = taskType.toDatabaseValue(),
      submitCallback = submitCallback
    ) { taskId ->
      fileManager.saveInputFromPfd(taskId, inputPfd, dataSize)
    }
  }

  /**
   * 调度待办事项检测任务（文本方式）
   */
  override fun dispatchActionItemTaskWithText(
    inputText: String,
    submitCallback: ITaskSubmitCallback
  ) {
    val taskType = TaskType.DETECT_ACTION_ITEM
    dispatchCore(
      apiKey = taskType.toApiKey(),
      taskType = taskType.toDatabaseValue(),
      submitCallback = submitCallback
    ) { taskId ->
      fileManager.saveInputText(taskId, inputText)
    }
  }

  override fun dispatchTask(request: TaskRequest, submitCallback: ITaskSubmitCallback) {
    if (request.input == null) {
      AILog.i(TAG, "Dispatch failed: TaskRequest.input is null for apiKey ${request.taskType.toApiKey()}")
      submitCallback.onError(ErrorCodes.INVALID_PARAMETER, "Task input is missing.")
      return
    }

    val aidlTaskType = request.taskType
    dispatchCore(
      apiKey = aidlTaskType.toApiKey(),
      taskType = aidlTaskType.toDatabaseValue(),
      submitCallback = submitCallback
    ) { taskId ->
      val input = request.input
      when {
        input.inputText != null -> {
          AILog.i(TAG, "Saving input from text for task $taskId")
          fileManager.saveInputText(taskId, input.inputText)
        }
        input.inputPfd != null -> {
          AILog.i(TAG, "Saving input from PFD for task $taskId")
          fileManager.saveInputFromPfd(taskId, input.inputPfd, input.dataSize)
        }
        else -> {
          AILog.e(TAG, "Invalid input in TaskRequest for task $taskId: both text and PFD are null.")
          null
        }
      }
    }
  }

  /**
   * 取消指定任务
   * 直接操作数据库，取消队列中的任务
   */
  override fun cancelTask(taskId: String): Boolean {
    return try {
      launch {
        val cancelledRows = taskDao.cancelQueuedTask(taskId)
        if (cancelledRows > 0) {
          AILog.i(TAG, "Task cancelled: $taskId")
          // 清理相关文件
          fileManager.deleteTaskFiles(taskId)
        } else {
          AILog.w(TAG, "Failed to cancel task (not in queue or not found): $taskId")
        }
      }
      true // 异步操作，返回true表示请求已提交
    } catch (e: Exception) {
      AILog.e(TAG, "Error cancelling task: $taskId", e)
      false
    }
  }


  /**
   * 获取任务状态
   */
  override fun getTaskState(taskId: String): TaskState? {
    return try {
      runBlocking(coroutineContext) {
        val taskEntity = taskDao.getTaskById(taskId) ?: return@runBlocking null
        val taskState = taskEntity.toTaskState()

        if (taskState.status == TaskStatus.COMPLETED && !taskState.result.isNullOrEmpty()) {
          AILog.i(TAG, "Successfully retrieved task result for $taskId, content length: ${taskState.result?.length}")
        }

        taskState
      }
    } catch (e: Exception) {
      AILog.i(TAG, "Error getting task state: $taskId with exception: ${e.message}")
      null
    }
  }

  /**
   * 生成任务ID
   */
  private fun generateTaskId(prefix: String): String {
    val id = taskIdGenerator.incrementAndGet()
    return "${prefix}_${System.currentTimeMillis()}_$id"
  }

  /**
   * 关闭调度器
   */
  override fun shutdown() {
    try {
      scopeDelegate.shutdown()
      AILog.i(TAG, "TaskDispatcher shutdown completed")
    } catch (e: Exception) {
      AILog.e(TAG, "Error during TaskDispatcher shutdown", e)
    }
  }
}
