package com.aispeech.modellibs

import android.annotation.SuppressLint
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Intent
import android.os.Build
import android.os.IBinder
import android.os.ParcelFileDescriptor
import androidx.core.app.NotificationCompat
import com.aispeech.aibase.AILog
import com.aispeech.modellibs.di.IDependencyContainer
import com.aispeech.modellibs.dispatcher.ITaskDispatcher
import com.aispeech.modellibs.engine.IModelEngine
import com.aispeech.modellibs.status.IServiceStatusManager
import com.aispeech.modellibs.task.ITaskManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock

/**
 * 离线模型服务实现
 * 作为 AIDL 服务的入口，不包含具体逻辑，委托给 ModelTaskManager 处理
 */
class OfflineModelService : Service() {

  companion object {
    private const val TAG = "OfflineModelService"
    private const val CHANNEL_ID = "offline_model_service_channel"
    private const val NOTIFICATION_ID = 2001

    private const val ERROR_CODE_SERVICE_UNAVAILABLE = -101
    private const val ERROR_MSG_SERVICE_UNAVAILABLE = "Offline service is unavailable (hardware or components not ready)."
  }

  // 依赖注入容器
  private var dependencyContainer: IDependencyContainer? = null

  // 从依赖容器注入的组件
  private var taskDispatcher: ITaskDispatcher? = null
  private var taskManager: ITaskManager? = null
  private var modelEngine: IModelEngine? = null
  private var serviceStatusManager: IServiceStatusManager? = null

  // 服务协程作用域
  private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

  private var componentsReady = false
  private val initializationMutex = Mutex()

  /**
   * 检查设备是否支持 APUSys 硬件。
   * 使用 lazy 属性确保文件检查只在首次访问时执行一次，并将结果缓存。
   */
  private val isApuSysSupported: Boolean
    get() = CapabilityUtils.isApuSysSupported()

  /**
   * 内部状态回调
   */
  private val innerStatusCallback = object : IModelStatusCallback.Stub() {
    override fun onStatusChanged(status: ModelServiceStatus) {
      if (status.isInitialized && !componentsReady) {
        AILog.i(TAG, "State transitioned to Initialized. Initializing service components.")
        initializeServiceComponents()
      }

      updateNotification(status)
    }

    override fun onRegistrationSuccess(status: ModelServiceStatus) {
      AILog.i(TAG, "Status callback registered, current status: ${status.statusMessage}")
      onStatusChanged(status)
    }

    override fun onRegistrationFailed(errorCode: Int, errorMessage: String?) {
      AILog.i(TAG, "Status callback registration failed: $errorCode - $errorMessage")
    }

    override fun onConnectionLost(reason: String?) {
      AILog.i(TAG, "Status callback connection lost: $reason")
    }

    override fun onServiceError(
      errorCode: Int,
      errorMessage: String?,
      currentStatus: ModelServiceStatus?
    ) {
      AILog.i(TAG, "Status callback error: $errorCode, message: $errorMessage")
    }
  }

  @SuppressLint("InlinedApi")
  override fun onCreate() {
    super.onCreate()
    AILog.i(TAG, "OfflineModelService onCreate")

    try {
      createNotificationChannel()

      // 启动前台服务
      if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
        startForeground(
          NOTIFICATION_ID,
          createNotification(),
          android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_DATA_SYNC
        )
      } else {
        startForeground(NOTIFICATION_ID, createNotification())
      }

      initializeComponents()
      AILog.i(TAG, "OfflineModelService started in foreground")
    } catch (e: Exception) {
      AILog.e(TAG, "Service onCreate failed", e)
      throw e
    }
  }

  /**
   * 初始化组件设置
   * 设置依赖容器引用，但不立即初始化组件
   * 等待通过状态回调触发实际的组件初始化
   */
  private fun initializeComponents() {
    try {
      val app = application as? OfflineModelApplication
      if (app == null) {
        AILog.e(TAG, "Application is not OfflineModelApplication")
        return
      }

      dependencyContainer = app.getDependencyContainer()

      // 获取状态管理器用于监听初始化状态
      val statusManager = dependencyContainer?.getServiceStatusManager()
      statusManager?.registerStatusCallback(innerStatusCallback)

    } catch (e: Exception) {
      AILog.i(TAG, "Failed to setup service component initialization with ${e.message}")
      componentsReady = false
    }
  }

  /**
   * 初始化服务组件
   * 当DI容器初始化完成时调用
   */
  private fun initializeServiceComponents() {
    serviceScope.launch {
      initializationMutex.withLock {
        if (componentsReady) {
          AILog.i(TAG, "Service components already initialized")
          return@withLock
        }

        val container = dependencyContainer
        if (container == null || !container.isInitialized()) {
          AILog.w(TAG, "DI container not ready, cannot initialize service components")
          return@withLock
        }

        try {
          taskDispatcher = container.getTaskDispatcher()
          taskManager = container.getTaskManager()
          modelEngine = container.getModelEngine()
          serviceStatusManager = container.getServiceStatusManager()

          componentsReady = true
          AILog.i(TAG, "Service components initialized successfully")

          // 通知服务已经就绪
          serviceStatusManager?.setServiceComponentsReady()

          // 更新通知状态
          updateNotificationFromStatus()

        } catch (e: Exception) {
          AILog.e(TAG, "Failed to initialize service components", e)
          componentsReady = false
        }
      }
    }
  }

  override fun onBind(intent: Intent?): IBinder {
    AILog.i(TAG, "OfflineModelService onBind")
    return modelServiceStub
  }

  override fun onDestroy() {
    super.onDestroy()
    AILog.i(TAG, "OfflineModelService onDestroy")

    // 取消服务协程作用域
    serviceScope.cancel()
    // 取消回调
    dependencyContainer?.getServiceStatusManager()?.unregisterStatusCallback(innerStatusCallback)

    if (componentsReady) {
      AILog.i(TAG, "Service destroyed, components managed by Application")
    }
  }

  /**
   * AIDL 服务实现
   */
  private val modelServiceStub = object : IModelService.Stub() {

    override fun extractAbstract(
      inputPfd: ParcelFileDescriptor,
      dataSize: Int,
      submitCallback: ITaskSubmitCallback
    ) {
      AILog.i(TAG, "extractAbstract called, dataSize: $dataSize")

      // 检查基础组件状态
      if (!isServiceReadyForTaskExecution()) {
        submitCallback.onError(ERROR_CODE_SERVICE_UNAVAILABLE, ERROR_MSG_SERVICE_UNAVAILABLE)
        return
      }

      // 使用引擎管理器执行任务
      serviceScope.launch {
        taskDispatcher!!.dispatchAbstractTask(inputPfd, dataSize, submitCallback)
      }
    }

    override fun extractAbstractWithText(
      inputText: String,
      submitCallback: ITaskSubmitCallback
    ) {
      AILog.i(TAG, "extractAbstractWithText called, textLength: ${inputText.length}")

      if (!isServiceReadyForTaskExecution()) {
        submitCallback.onError(ERROR_CODE_SERVICE_UNAVAILABLE, ERROR_MSG_SERVICE_UNAVAILABLE)
        return
      }

      serviceScope.launch {
        taskDispatcher!!.dispatchAbstractTaskWithText(inputText, submitCallback)
      }
    }

    override fun dectionActionItem(
      inputPfd: ParcelFileDescriptor,
      dataSize: Int,
      submitCallback: ITaskSubmitCallback
    ) {
      AILog.i(TAG, "dectionActionItem called, dataSize: $dataSize")

      if (!isServiceReadyForTaskExecution()) {
        submitCallback.onError(ERROR_CODE_SERVICE_UNAVAILABLE, ERROR_MSG_SERVICE_UNAVAILABLE)
        return
      }

      serviceScope.launch {
        taskDispatcher!!.dispatchActionItemTask(inputPfd, dataSize, submitCallback)
      }
    }

    override fun dectionActionItemWithText(
      inputText: String,
      submitCallback: ITaskSubmitCallback
    ) {
      AILog.i(TAG, "dectionActionItemWithText called, textLength: ${inputText.length}")

      if (!isServiceReadyForTaskExecution()) {
        submitCallback.onError(ERROR_CODE_SERVICE_UNAVAILABLE, ERROR_MSG_SERVICE_UNAVAILABLE)
        return
      }

      serviceScope.launch {
        taskDispatcher!!.dispatchActionItemTaskWithText(inputText, submitCallback)
      }
    }

    override fun submitTask(request: TaskRequest, submitCallback: ITaskSubmitCallback) {
      AILog.i(TAG, "submitTask called, taskName: ${request.taskType.toApiKey()}")

      if (!isServiceReadyForTaskExecution()) {
        submitCallback.onError(ERROR_CODE_SERVICE_UNAVAILABLE, ERROR_MSG_SERVICE_UNAVAILABLE)
        return
      }

      serviceScope.launch {
        taskDispatcher!!.dispatchTask(request, submitCallback)
      }
    }


    override fun isApuSysSupported(): Boolean {
      AILog.i(TAG, "isApuSysSupported called")
      return <EMAIL>
    }

    override fun getCurrentStatus(): ModelServiceStatus {
      AILog.i(TAG, "getCurrentStatus called")

      // 获取基础服务状态
      val statusManager = if (componentsReady && serviceStatusManager != null) {
        serviceStatusManager
      } else {
        dependencyContainer?.getServiceStatusManager()
      }

      val baseStatus = statusManager?.getCurrentStatus()
          ?: ModelServiceStatus(
            statusCode = ServiceStatusCode.INITIALIZING,
            statusMessage = "服务初始化中",
            isAuthorized = false,
            isInitialized = false,
            isServiceSupported = true,
            activeTaskCount = 0,
            isTaskManagerRunning = false
          )

      val enhancedStatusMessage = buildString {
        append(baseStatus.statusMessage)
      }

      return baseStatus.copy(
        statusMessage = enhancedStatusMessage
      )
    }

    override fun registerStatusCallback(callback: IModelStatusCallback): Boolean {
      AILog.i(TAG, "registerStatusCallback called")

      // 尝试从依赖容器获取状态管理器，即使组件未完全就绪
      val statusManager = if (componentsReady && serviceStatusManager != null) {
        serviceStatusManager
      } else {
        dependencyContainer?.getServiceStatusManager()
      }

      return if (statusManager != null) {
        statusManager.registerStatusCallback(callback)
      } else {
        AILog.w(TAG, "Status manager not available")
        false
      }
    }

    override fun unregisterStatusCallback(callback: IModelStatusCallback): Boolean {
      AILog.i(TAG, "unregisterStatusCallback called")

      // 尝试从依赖容器获取状态管理器
      val statusManager = if (componentsReady && serviceStatusManager != null) {
        serviceStatusManager
      } else {
        dependencyContainer?.getServiceStatusManager()
      }

      return if (statusManager != null) {
        statusManager.unregisterStatusCallback(callback)
      } else {
        AILog.w(TAG, "Status manager not available")
        false
      }
    }

    override fun unregisterAllStatusCallbacks() {
      AILog.i(TAG, "unregisterAllStatusCallbacks called")

      // 尝试从依赖容器获取状态管理器
      val statusManager = if (componentsReady && serviceStatusManager != null) {
        serviceStatusManager
      } else {
        dependencyContainer?.getServiceStatusManager()
      }

      statusManager?.unregisterAllStatusCallbacks()
    }

    override fun getTaskState(taskId: String): TaskState? {
      AILog.i(TAG, "getTaskState called, taskId: $taskId")

      if (!isServiceReadyForTaskExecution()) {
        AILog.w(TAG, "Cannot getTaskState, service is not ready for execution.")
        return null
      }

      return taskDispatcher!!.getTaskState(taskId)
    }

    override fun getTaskStateAsync(taskId: String, callback: com.aispeech.modellibs.ITaskStateCallback) {
      AILog.i(TAG, "getTaskStateAsync called, taskId: $taskId")

      if (!isServiceReadyForTaskExecution()) {
        AILog.w(TAG, "Cannot getTaskStateAsync, service is not ready for execution.")
        callback.onQueryError(taskId, ERROR_CODE_SERVICE_UNAVAILABLE, ERROR_MSG_SERVICE_UNAVAILABLE)
        return
      }

      // 在后台线程执行异步查询
      serviceScope.launch {
        taskDispatcher!!.getTaskStateAsync(taskId, callback)
      }
    }

    override fun cancelTask(taskId: String): Boolean {
      AILog.i(TAG, "cancelTask called, taskId: $taskId")

      if (!isServiceReadyForTaskExecution()) {
        AILog.w(TAG, "Cannot cancelTask, service is not ready for execution.")
        return false
      }

      return taskDispatcher!!.cancelTask(taskId)
    }

    override fun updateMagnusRuntimePath(newPath: String): Boolean {
      return try {
        ConfigurationManager.updateMagnusRuntimePath(newPath)
      } catch (e: Exception) {
        AILog.e(TAG, "Error updating Magnus runtime path", e)
        false
      }
    }
  }

  /**
   * 创建通知渠道
   */
  private fun createNotificationChannel() {
    val channel = NotificationChannel(
      CHANNEL_ID,
      "离线模型服务",
      NotificationManager.IMPORTANCE_LOW
    ).apply {
      description = "离线模型处理服务"
      setShowBadge(false)
    }

    val notificationManager = getSystemService(NotificationManager::class.java)
    notificationManager.createNotificationChannel(channel)
  }

  /**
   * 创建通知
   */
  private fun createNotification(status: ModelServiceStatus? = null): Notification {
    val currentStatus = status ?: getCurrentServiceStatus()

    val statusText = when (currentStatus.statusCode) {
      ServiceStatusCode.INITIALIZING -> "初始化中"
      ServiceStatusCode.IDLE -> "空闲"
      ServiceStatusCode.RUNNING -> "处理中"
      ServiceStatusCode.BUSY -> "繁忙"
      ServiceStatusCode.ERROR -> "错误"
      ServiceStatusCode.STOPPING -> "停止中"
      ServiceStatusCode.STOPPED -> "已停止"
      else -> "未知状态"
    }

    val contentText = buildString {
      append("状态: $statusText")
      if (currentStatus.activeTaskCount > 0) {
        append(" | 任务: ${currentStatus.activeTaskCount}")
      }
      if (!currentStatus.isAuthorized) {
        append(" | 未授权")
      }
    }

    return NotificationCompat.Builder(this, CHANNEL_ID)
      .setContentTitle("离线模型服务")
      .setContentText(contentText)
      .setSmallIcon(android.R.drawable.ic_dialog_info)
      .setOngoing(true)
      .setShowWhen(false)
      .build()
  }

  /**
   * 更新通知
   */
  private fun updateNotification(status: ModelServiceStatus) {
    try {
      val notification = createNotification(status)
      val notificationManager = getSystemService(NotificationManager::class.java)
      notificationManager.notify(NOTIFICATION_ID, notification)
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to update notification", e)
    }
  }

  /**
   * 从当前状态更新通知
   */
  private fun updateNotificationFromStatus() {
    try {
      val currentStatus = getCurrentServiceStatus()
      updateNotification(currentStatus)
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to update notification from status", e)
    }
  }

  /**
   * 获取当前服务状态
   */
  private fun getCurrentServiceStatus(): ModelServiceStatus {
    // 尝试从依赖容器获取状态管理器
    val statusManager = if (componentsReady && serviceStatusManager != null) {
      serviceStatusManager
    } else {
      dependencyContainer?.getServiceStatusManager()
    }

    return statusManager?.getCurrentStatus()
        ?: ModelServiceStatus(
          statusCode = ServiceStatusCode.INITIALIZING,
          statusMessage = "服务初始化中",
          isAuthorized = false,
          isInitialized = false,
          isServiceSupported = true,
          activeTaskCount = 0,
          isTaskManagerRunning = false
        )
  }

  /**
   * 检查基础组件是否就绪
   */
  private fun checkBasicComponentsReady(): Boolean {
    return componentsReady && taskDispatcher != null
  }

  /**
   * 增强的守卫方法：检查服务是否为【任务执行】准备就绪
   * 包含了硬件和软件两方面的检查。
   * @return true 如果可以执行任务，否则 false。
   */
  private fun isServiceReadyForTaskExecution(): Boolean {
    if (!isApuSysSupported) {
      AILog.i(TAG, "Service is not ready for task execution: Hardware not supported.")
      return false
    }

    if (!checkBasicComponentsReady()) {
      AILog.i(TAG, "Service is not ready for task execution: Components not ready.")
      return false
    }

    return true
  }
}