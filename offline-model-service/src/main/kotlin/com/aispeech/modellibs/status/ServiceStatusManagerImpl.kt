package com.aispeech.modellibs.status

import com.aispeech.aibase.AILog
import com.aispeech.modellibs.CoroutineScopeManager
import com.aispeech.modellibs.IModelStatusCallback
import com.aispeech.modellibs.ModelServiceStatus
import com.aispeech.modellibs.ServiceStatusCode
import com.aispeech.modellibs.task.ITaskManager
import com.aispeech.modellibs.task.TaskEvent
import com.aispeech.modellibs.task.TaskManagerStatus
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger
import java.util.concurrent.atomic.AtomicReference

/**
 * 服务状态管理器实现
 * 负责管理服务状态和状态回调，订阅 TaskManager 的状态更新
 * 实现 CoroutineScope 接口，使用结构化并发管理协程生命周期
 */
class ServiceStatusManagerImpl : IServiceStatusManager, CoroutineScope {

  companion object {
    private const val TAG = "ServiceStatusManagerImpl"
  }

  // 状态回调管理
  private val statusCallbacks = ConcurrentHashMap<String, IModelStatusCallback>()
  private val callbackIdGenerator = AtomicInteger(0)

  // 当前服务状态
  private val currentStatus = AtomicReference<ModelServiceStatus>()

  // 协程作用域委托 - 使用结构化并发
  private val scopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getIOParentScope()
  )

  // 委托CoroutineScope接口到scopeDelegate
  override val coroutineContext = scopeDelegate.coroutineContext

  // 竞争锁
  private val statusMutex = Mutex()

  // 注入的任务管理器引用
  private var taskManager: ITaskManager? = null

  init {
    // 初始化默认状态
    updateStatus(
      statusCode = ServiceStatusCode.INITIALIZING,
      statusMessage = "服务初始化中",
      isAuthorized = false,
      isInitialized = false,
      isServiceSupported = true,
      isTaskManagerRunning = false
    )
  }

  /**
   * 设置任务管理器引用并开始监听任务事件
   */
  override fun setTaskManager(taskManager: ITaskManager) {
    this.taskManager = taskManager

    // 开始监听任务事件
    launch {
      taskManager.taskEvents.collect { event ->
        handleTaskEvent(event)
      }
    }

    AILog.i(TAG, "Task manager set and event monitoring started")
  }

  /**
   * 处理任务事件
   */
  private suspend fun handleTaskEvent(event: TaskEvent) {
    this.taskManager ?: return

    when (event) {
      is TaskEvent.TaskSubmitted -> {
        updateTaskManagerStatus( "任务已排队: ${event.taskId}")
      }
      is TaskEvent.ExecutionStarted -> {
        updateTaskManagerStatus( "任务执行中: ${event.taskId}")
      }
      is TaskEvent.ExecutionSucceeded -> {
        updateTaskManagerStatus( "任务完成: ${event.taskId}")
      }
      is TaskEvent.ExecutionFailed -> {
        updateTaskManagerStatus( "任务失败: ${event.taskId}", event.errorMessage)
      }
      is TaskEvent.Cancelled -> {
        updateTaskManagerStatus("任务已取消: ${event.taskId}")
      }
    }
  }

  /**
   * 根据任务管理器状态更新服务状态
   */
  private suspend fun updateTaskManagerStatus(
    message: String,
    errorMessage: String? = null
  ) {
    updateStatusAtomic { old, taskManagerStatus ->

      if (taskManagerStatus == null) {
        AILog.w(TAG, "updateTaskManagerStatus skipped: failed to get task manager status.")
        return@updateStatusAtomic old // 返回旧状态，相当于什么都不做
      }

      val newStatusCode = when {
        taskManagerStatus.isExecuting -> ServiceStatusCode.RUNNING
        taskManagerStatus.queuedTaskCount > 0 -> ServiceStatusCode.BUSY
        errorMessage != null -> ServiceStatusCode.ERROR
        old.isInitialized && old.isAuthorized -> ServiceStatusCode.IDLE
        else -> old.statusCode
      }

      old.copy(
        statusCode = newStatusCode,
        statusMessage = message,
        timestamp = System.currentTimeMillis(),
        activeTaskCount = taskManagerStatus.queuedTaskCount + if (taskManagerStatus.isExecuting) 1 else 0,
        isTaskManagerRunning = true,
        lastError = errorMessage ?: if (newStatusCode != ServiceStatusCode.ERROR) null else old.lastError
      )
    }
  }

  /**
   * 获取当前状态
   */
  override fun getCurrentStatus(): ModelServiceStatus {
    return currentStatus.get() ?: createDefaultStatus()
  }

  /**
   * 原子状态更新
   */
  private suspend fun updateStatusAtomic(
    updater: (old: ModelServiceStatus, taskManagerStatus: TaskManagerStatus?) -> ModelServiceStatus) {
    statusMutex.withLock {
      val taskManagerStatus = try {
        taskManager?.getStatus()
      } catch (e: Exception) {
        AILog.w(TAG, "Failed to get task manager status inside mutex: ${e.message}")
        null
      }

      val oldStatus = currentStatus.get() ?: createDefaultStatus()
      val newStatus = updater(oldStatus, taskManagerStatus)

      if (hasStatusChanged(oldStatus, newStatus)) {
        currentStatus.set(newStatus)
        notifyStatusChanged(newStatus)
        AILog.i(TAG, "Status updated (mutex): ${newStatus.getReadableStatus()}")
      }
    }
  }

  /**
   * 更新服务状态
   */
  private fun updateStatus(
    statusCode: Int,
    statusMessage: String,
    isAuthorized: Boolean? = null,
    isInitialized: Boolean? = null,
    isServiceSupported: Boolean? = null,
    isTaskManagerRunning: Boolean? = null,
    lastError: String? = null,
    isServiceComponentsReady: Boolean? = null
  ) {
    launch {
      updateStatusAtomic { old, taskManagerStatus ->
        old.copy(
          statusCode = statusCode,
          statusMessage = statusMessage,
          timestamp = System.currentTimeMillis(),
          isAuthorized = isAuthorized ?: old.isAuthorized,
          isInitialized = isInitialized ?: old.isInitialized,
          isServiceSupported = isServiceSupported ?: old.isServiceSupported,
          isTaskManagerRunning = isTaskManagerRunning ?: old.isTaskManagerRunning,
          lastError = lastError ?: old.lastError,
          isServiceComponentsReady = isServiceComponentsReady ?: old.isServiceComponentsReady,
          activeTaskCount = taskManagerStatus?.let {
            it.queuedTaskCount + if (it.isExecuting) 1 else 0
          } ?: old.activeTaskCount
        )
      }
    }
  }

  /**
   * 设置授权完成
   */
  override fun setAuthorized() {
    updateStatus(
      statusCode = ServiceStatusCode.IDLE,
      statusMessage = "授权成功",
      isAuthorized = true
    )
  }

  /**
   * 设置授权失败
   */
  override fun setAuthorizationFailed(errorMessage: String) {
    updateStatus(
      statusCode = ServiceStatusCode.ERROR,
      statusMessage = "授权失败",
      isAuthorized = false,
      lastError = errorMessage
    )
  }

  /**
   * 设置授权重试中
   */
  override fun setAuthorizationRetrying(retryReason: String) {
    updateStatus(
      statusCode = ServiceStatusCode.INITIALIZING,
      statusMessage = "授权重试中: $retryReason",
      isAuthorized = false
    )
  }

  /**
   * 设置初始化完成
   */
  override fun setInitialized() {
    updateStatus(
      statusCode = ServiceStatusCode.IDLE,
      statusMessage = "服务就绪",
      isInitialized = true,
      isTaskManagerRunning = true
    )
  }

  override fun setServiceComponentsReady() {
    updateStatus(
      statusCode = ServiceStatusCode.IDLE, // 假设此时服务应处于空闲状态
      statusMessage = "服务组件就绪，可接收任务",
      isServiceComponentsReady = true // 直接更新新字段
    )
  }

  /**
   * 设置服务错误
   */
  override fun setError(errorMessage: String) {
    updateStatus(
      statusCode = ServiceStatusCode.ERROR,
      statusMessage = "服务错误",
      lastError = errorMessage
    )
  }

  /**
   * 注册状态回调
   */
  override fun registerStatusCallback(callback: IModelStatusCallback): Boolean {
    return try {
      val callbackId = "callback_${callbackIdGenerator.incrementAndGet()}"
      statusCallbacks[callbackId] = callback

      AILog.i(TAG, "Status callback registered: $callbackId")

      // 立即发送当前状态
      launch {
        try {
          callback.onRegistrationSuccess(getCurrentStatus())
        } catch (e: Exception) {
          AILog.e(TAG, "Error sending registration success", e)
          statusCallbacks.remove(callbackId)
        }
      }

      true
    } catch (e: Exception) {
      AILog.e(TAG, "Error registering status callback", e)
      false
    }
  }

  /**
   * 取消注册状态回调
   */
  override fun unregisterStatusCallback(callback: IModelStatusCallback): Boolean {
    return try {
      val removedEntries = statusCallbacks.entries.filter { it.value == callback }
      removedEntries.forEach { entry ->
        statusCallbacks.remove(entry.key)
        AILog.i(TAG, "Status callback unregistered: ${entry.key}")
      }
      removedEntries.isNotEmpty()
    } catch (e: Exception) {
      AILog.e(TAG, "Error unregistering status callback", e)
      false
    }
  }

  /**
   * 取消所有状态回调
   */
  override fun unregisterAllStatusCallbacks() {
    try {
      val count = statusCallbacks.size
      statusCallbacks.clear()
      AILog.i(TAG, "All status callbacks unregistered: $count")
    } catch (e: Exception) {
      AILog.e(TAG, "Error unregistering all status callbacks", e)
    }
  }

  /**
   * 刷新服务状态
   */
  override fun refreshServiceStatus() {
    launch {
      val currentStatus = getCurrentStatus()
      notifyStatusChanged(currentStatus)
      AILog.i(TAG, "Service status refreshed")
    }
  }

  /**
   * 通知状态变化
   */
  private fun notifyStatusChanged(status: ModelServiceStatus) {
    val callbacksToRemove = mutableListOf<String>()

    statusCallbacks.forEach { (callbackId, callback) ->
      try {
        callback.onStatusChanged(status)
      } catch (e: Exception) {
        AILog.e(TAG, "Error notifying status callback $callbackId", e)
        callbacksToRemove.add(callbackId)
      }
    }

    // 移除失效的回调
    callbacksToRemove.forEach { callbackId ->
      statusCallbacks.remove(callbackId)
      AILog.w(TAG, "Removed failed status callback: $callbackId")
    }
  }

  /**
   * 检查状态是否发生变化
   */
  private fun hasStatusChanged(old: ModelServiceStatus, new: ModelServiceStatus): Boolean {
    return old.statusCode != new.statusCode ||
      old.statusMessage != new.statusMessage ||
      old.isAuthorized != new.isAuthorized ||
      old.isInitialized != new.isInitialized ||
      old.activeTaskCount != new.activeTaskCount ||
      old.isTaskManagerRunning != new.isTaskManagerRunning ||
      old.lastError != new.lastError ||
      old.isServiceComponentsReady != new.isServiceComponentsReady
  }

  /**
   * 创建默认状态
   */
  private fun createDefaultStatus(): ModelServiceStatus {
    return ModelServiceStatus(
      statusCode = ServiceStatusCode.INITIALIZING,
      statusMessage = "服务初始化中",
      isAuthorized = false,
      isInitialized = false,
      isServiceSupported = true,
      activeTaskCount = 0,
      isTaskManagerRunning = false,
      isServiceComponentsReady = false
    )
  }

  /**
   * 关闭状态管理器
   */
  override fun shutdown() {
    AILog.i(TAG, "Shutting down ServiceStatusManagerImpl")

    // 设置停止状态
    updateStatus(
      statusCode = ServiceStatusCode.STOPPED,
      statusMessage = "服务已停止",
      isTaskManagerRunning = false
    )

    // 清理所有回调
    unregisterAllStatusCallbacks()

    // 取消协程作用域
    scopeDelegate.shutdown("ServiceStatusManagerImpl shutdown")

    AILog.i(TAG, "ServiceStatusManagerImpl shutdown completed")
  }
}
