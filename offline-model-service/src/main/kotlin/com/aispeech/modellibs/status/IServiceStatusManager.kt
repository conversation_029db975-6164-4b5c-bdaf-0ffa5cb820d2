package com.aispeech.modellibs.status

import com.aispeech.modellibs.IModelStatusCallback
import com.aispeech.modellibs.ModelServiceStatus
import com.aispeech.modellibs.task.ITaskManager

/**
 * 服务状态管理器接口
 * 负责管理服务状态和状态回调
 */
interface IServiceStatusManager {

  /**
   * 设置任务管理器引用
   * 用于订阅任务状态变化
   */
  fun setTaskManager(taskManager: ITaskManager)

  /**
   * 获取当前状态
   */
  fun getCurrentStatus(): ModelServiceStatus

  /**
   * 注册状态回调
   */
  fun registerStatusCallback(callback: IModelStatusCallback): Boolean

  /**
   * 取消注册状态回调
   */
  fun unregisterStatusCallback(callback: IModelStatusCallback): Boolean

  /**
   * 取消所有状态回调
   */
  fun unregisterAllStatusCallbacks()

  /**
   * 刷新服务状态
   */
  fun refreshServiceStatus()

  /**
   * 设置授权完成
   */
  fun setAuthorized()

  /**
   * 设置授权失败
   */
  fun setAuthorizationFailed(errorMessage: String)

  /**
   * 设置授权重试中
   */
  fun setAuthorizationRetrying(retryReason: String)

  /**
   * 设置初始化完成
   */
  fun setInitialized()

  /**
   * 设置服务组件已就绪
   * 当 Service 完成其内部组件加载时调用
   */
  fun setServiceComponentsReady()

  /**
   * 设置服务错误
   */
  fun setError(errorMessage: String)

  /**
   * 关闭状态管理器
   */
  fun shutdown()
}
