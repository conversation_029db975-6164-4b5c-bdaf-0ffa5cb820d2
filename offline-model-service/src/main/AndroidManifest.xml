<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  package="com.aispeech.modellibs">

  <!-- 前台服务权限 -->
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

  <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

  <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>

  <application
    android:name=".OfflineModelApplication"
    android:networkSecurityConfig="@xml/network_security_config"
    android:allowBackup="true"
    android:icon="@mipmap/ic_launcher"
    android:label="@string/app_name"
    android:roundIcon="@mipmap/ic_launcher_round"
    android:supportsRtl="true">

    <activity
      android:name=".UnfreezeActivity"
      android:exported="true"
      android:theme="@android:style/Theme.Translucent.NoTitleBar"
      android:enabled="true"/>
<!--    <activity-->
<!--      android:name=".HybridDebugActivity"-->
<!--      android:exported="true"-->
<!--      android:label="@string/title_activity_hybrid_debug"-->
<!--      android:theme="@style/Theme.Aimttablet">-->
<!--      <intent-filter>-->
<!--        <action android:name="android.intent.action.MAIN" />-->
<!--        <category android:name="android.intent.category.LAUNCHER" />-->
<!--      </intent-filter>-->
<!--    </activity>-->


    <activity
      android:name=".ModelServiceTestActivity"
      android:exported="true"
      android:label="@string/title_activity_hybrid_debug_offline"
      android:theme="@style/Theme.Aimttablet">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>

    <!-- 注册开机广播 -->
<!--    <receiver android:name=".BootReceiver"-->
<!--      android:enabled="true"-->
<!--      android:exported="true">-->
<!--      <intent-filter>-->
<!--        <action android:name="android.intent.action.BOOT_COMPLETED"/>-->
<!--        <action android:name="android.intent.action.LOCKED_BOOT_COMPLETED"/>-->
<!--        <action android:name="android.intent.action.MY_PACKAGE_REPLACED"/>-->
<!--      </intent-filter>-->
<!--    </receiver>-->

    <service
      android:name=".OfflineModelService"
      android:enabled="true"
      android:exported="true"
      android:foregroundServiceType="dataSync">
      <intent-filter>
        <action android:name="com.aispeech.modellibs.ACTION_MODEL_LIBS" />
      </intent-filter>
    </service>
  </application>

</manifest>