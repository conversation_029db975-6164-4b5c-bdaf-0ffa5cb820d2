/*
 * Copyright (C) 2023 AISPEECH. All rights reserved.
 *
 * This software is the confidential and proprietary information of AI Speech Co., Ltd..
 * Unauthorized copying, modification, publication, or use of this software, either
 * in part or in whole, is strictly prohibited without the prior written consent of <PERSON><PERSON><PERSON><PERSON>.
 *
 * For authorization inquiries, please contact AISPEECH at www.aispeech.com.
 */
import com.android.build.gradle.internal.api.ApkVariantOutputImpl
import java.util.Properties

plugins {
  id("aispeech.android.application")
  id("aispeech.android.application.compose")
  id("aispeech.spotless")
  id("com.google.devtools.ksp")
  id("org.jetbrains.kotlin.plugin.serialization") version "1.9.0"
}

val storeType = properties["keyStore"].toString()
val appSign = when(storeType){
  "1" -> "a1"
  "2" -> "a2"
  "3" -> "a3"
  else -> "app"
}
val keystoreProperties = Properties()
file("../app/keystore.properties").inputStream().use { inputStream ->
  keystoreProperties.load(inputStream)
}

android {
  namespace = "com.aispeech.modellibs"
  compileSdk = Configurations.compileSdk

  signingConfigs {
    println("=======> storeType = $storeType")
    create("aispeech") {
      keyAlias = keystoreProperties.getProperty("keyAlias")
      keyPassword = keystoreProperties.getProperty("keyPassword")
      storeFile = file(keystoreProperties.getProperty("storeFile"))
      storePassword = keystoreProperties.getProperty("storePassword")
    }
    create("system-11") {
      keyAlias = keystoreProperties.getProperty("keyAlias-system")
      keyPassword = keystoreProperties.getProperty("keyPassword-system")
      storeFile = file(keystoreProperties.getProperty("storeFile-system-11"))
      storePassword = keystoreProperties.getProperty("storePassword-system")
    }
    create("system-13") {
      keyAlias = keystoreProperties.getProperty("keyAlias-system")
      keyPassword = keystoreProperties.getProperty("keyPassword-system")
      storeFile = file(keystoreProperties.getProperty("storeFile-system-13"))
      storePassword = keystoreProperties.getProperty("storePassword-system")
    }
    create("system-15") {
      keyAlias = keystoreProperties.getProperty("keyAlias-system-15")
      keyPassword = keystoreProperties.getProperty("keyPassword-system")
      storeFile = file(keystoreProperties.getProperty("storeFile-system-15"))
      storePassword = keystoreProperties.getProperty("storePassword-system")
    }
  }

  // 编译类型
  buildTypes {
    getByName("release") {
      signingConfig = signingConfigs.getByName(
        when (storeType) {
          "1" -> "system-11"
          "2" -> "system-13"
          "3" -> "system-15"
          else -> "aispeech"
        }
      )
    }
    getByName("debug") {
      signingConfig = signingConfigs.getByName(
        when (storeType) {
          "1" -> "system-11"
          "2" -> "system-13"
          "3" -> "system-15"
          else -> "aispeech"
        }
      )
    }
  }

  // 输出类型
  android.applicationVariants.all {
    val buildType = this.buildType.name
    outputs.all {
      if (this is ApkVariantOutputImpl) {
        this.outputFileName =
          "aimt-modellibs_v${defaultConfig.versionName}_${buildType}_${properties["environment"].toString()}_${properties["country"].toString()}_${appSign}.apk"
      }
    }
  }

  buildFeatures {
    buildConfig = true
    aidl = true
    compose = true
  }

  defaultConfig {
    manifestPlaceholders += mapOf()
    applicationId = "com.aispeech.modellibs"
    minSdk = Configurations.minSdk
    targetSdk = Configurations.targetSdk
    versionCode = properties["buildNumber"].toString().toInt()
    versionName = properties["appVersion"] as String? + "." + properties["buildNumber"] as String?
    buildConfigField("String", "environment", "\"${properties["environment"].toString()}\"")
    buildConfigField("String", "country", "\"${properties["country"].toString()}\"")

    manifestPlaceholders.putAll(
      mapOf("SHARED_USER_ID" to when(properties["keyStore"].toString()){
        "1","2","3"->"android.uid.system"
        else -> ""
      })
    )
    vectorDrawables {
      useSupportLibrary = true
    }

    ndk {
      //noinspection ChromeOsAbiSupport
      abiFilters += listOf("arm64-v8a")
    }
  }

  packaging {
    resources.excludes += setOf("META-INF/DEPENDENCIES", "META-INF/LICENSE", "META-INF/ASL2.0")
  }
  compileOptions {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
  }
  kotlinOptions {
    jvmTarget = "1.8"
  }
}

dependencies {
  // libs
//  implementation(files("libs/lib-meeting-model-util-1.0.2-release.aar"))
  implementation(fileTree(mapOf("dir" to "libs", "include" to listOf("*.jar"))))

  // core modules
  implementation(project(":core-designsystem"))
  implementation(project(":core-navigation"))
  implementation(project(":core-model"))
  implementation(project(":core-common"))
  implementation(project(":core-network"))
  implementation(project(":offline-model-interface"))
  implementation(project(":offline-model-util"))
  implementation(project(":core-preferences"))

  // room
  implementation(libs.androidx.room.runtime)
  implementation(libs.androidx.room.ktx)
  implementation(libs.androidx.junit.ktx)

  ksp(libs.androidx.room.compiler)

  // material
  implementation(libs.androidx.appcompat)

  // compose
  implementation(libs.androidx.activity.compose)
  implementation(libs.androidx.compose.runtime)
  implementation(libs.androidx.compose.ui.tooling)
  implementation(libs.androidx.compose.ui.tooling.preview)
  implementation(libs.androidx.compose.constraintlayout)

  // jetpack
  implementation(libs.androidx.worker)
  implementation(libs.androidx.recyclerview)
  implementation(libs.androidx.ui.unit.android)
  implementation(libs.androidx.ui.text.android)
  implementation(libs.kotlinx.datetime)

  implementation(project(":lib-statusbar"))
  implementation(project(":lib-system"))

  implementation(libs.kotlinx.coroutines.core)
  implementation(libs.kotlinx.coroutines.android)

  implementation(libs.bugly)

  // Test
  testImplementation(libs.junit4)
  testImplementation("io.mockk:mockk:1.13.10")
  androidTestImplementation(libs.junit)
  androidTestImplementation("io.mockk:mockk-android:1.13.10")
  androidTestImplementation(libs.espresso.core)
}