# 异步任务状态查询接口使用指南

## 概述

为了提高任务状态查询的性能和用户体验，我们为 `IModelService` 添加了异步版本的 `getTaskState` 方法。新的异步接口避免了阻塞调用线程，特别适合频繁查询任务状态的场景。

## 接口设计

### AIDL 接口层面

#### 新增回调接口：ITaskStateCallback.aidl
```aidl
oneway interface ITaskStateCallback {
    void onTaskStateRetrieved(in TaskState taskState);
    void onTaskNotFound(String taskId);
    void onQueryError(String taskId, int errorCode, String errorMessage);
}
```

#### IModelService.aidl 新增方法
```aidl
void getTaskStateAsync(String taskId, ITaskStateCallback callback);
```

### 服务实现层面

- **OfflineModelService**: 添加了 `getTaskStateAsync` 方法，使用 `serviceScope.launch` 在后台线程执行
- **TaskDispatcher**: 实现异步查询逻辑，直接操作数据库并通过回调返回结果
- **错误处理**: 统一的异常处理和错误码返回机制

### Client 封装层面

ModelServiceClient 提供了两种使用方式：

1. **协程版本** (`suspend fun getTaskStateAsync`): 返回 TaskState?，适合使用协程的客户端
2. **传统回调版本** (`fun getTaskStateAsync`): 直接使用 ITaskStateCallback，适合不使用协程的客户端

## 使用方法

### 1. 协程版本（推荐）

```kotlin
class MyService {
    private val client = ModelServiceClient(context)
    
    suspend fun checkTaskStatus(taskId: String) {
        try {
            client.connect()
            
            val taskState = client.getTaskStateAsync(taskId)
            if (taskState != null) {
                when {
                    taskState.isCompleted() -> {
                        println("任务完成: ${taskState.result}")
                    }
                    taskState.isFailed() -> {
                        println("任务失败: ${taskState.errorMessage}")
                    }
                    taskState.isExecuting() -> {
                        println("任务执行中...")
                    }
                }
            } else {
                println("任务不存在或查询失败")
            }
        } catch (e: Exception) {
            println("查询异常: ${e.message}")
        }
    }
}
```

### 2. 传统回调版本

```kotlin
class MyService {
    private val client = ModelServiceClient(context)
    
    fun checkTaskStatus(taskId: String) {
        val callback = object : ITaskStateCallback.Stub() {
            override fun onTaskStateRetrieved(taskState: TaskState?) {
                taskState?.let {
                    println("任务状态: ${it.getReadableStatus()}")
                }
            }
            
            override fun onTaskNotFound(taskId: String?) {
                println("任务不存在: $taskId")
            }
            
            override fun onQueryError(taskId: String?, errorCode: Int, errorMessage: String?) {
                println("查询错误: $errorCode - $errorMessage")
            }
        }
        
        client.getTaskStateAsync(taskId, callback)
    }
}
```

### 3. 轮询任务状态

```kotlin
suspend fun waitForTaskCompletion(taskId: String): TaskState? {
    var retryCount = 0
    val maxRetries = 30
    
    while (retryCount < maxRetries) {
        val taskState = client.getTaskStateAsync(taskId)
        
        if (taskState?.isFinished() == true) {
            return taskState
        }
        
        delay(2000) // 等待2秒
        retryCount++
    }
    
    return null // 超时
}
```

## 性能优势

### 同步版本 vs 异步版本

| 特性 | 同步版本 | 异步版本 |
|------|----------|----------|
| 线程阻塞 | 会阻塞调用线程 | 不阻塞调用线程 |
| 数据库操作 | 使用 runBlocking | 使用协程 |
| 适用场景 | 偶尔查询 | 频繁查询、轮询 |
| 响应性 | 可能影响UI响应 | 不影响UI响应 |

### 使用建议

1. **频繁查询场景**: 使用异步版本避免阻塞
2. **轮询任务状态**: 使用异步版本提高性能
3. **一次性查询**: 两种版本都可以，同步版本更简单
4. **UI相关查询**: 推荐使用异步版本保持UI响应性

## 错误处理

### 错误类型

1. **服务未连接**: 通过 `onQueryError` 返回 `ERROR_CODE_SERVICE_UNAVAILABLE`
2. **任务不存在**: 通过 `onTaskNotFound` 回调
3. **数据库异常**: 通过 `onQueryError` 返回 `PROCESSING_FAILED`
4. **网络异常**: 在客户端层面处理，返回 null 或调用错误回调

### 超时处理

异步查询本身没有内置超时机制，建议在客户端实现：

```kotlin
suspend fun getTaskStateWithTimeout(taskId: String, timeoutMs: Long = 5000): TaskState? {
    return withTimeoutOrNull(timeoutMs) {
        client.getTaskStateAsync(taskId)
    }
}
```

## 向后兼容性

- 原有的同步 `getTaskState` 方法保持不变
- 现有代码无需修改即可继续使用
- 新功能作为可选的增强功能提供

## 测试

项目包含了完整的单元测试：
- `TaskStateAsyncTest.kt`: 测试 TaskDispatcher 的异步实现
- 测试覆盖成功、失败、任务不存在等场景

## 注意事项

1. **回调生命周期**: 确保在适当的时候取消回调，避免内存泄漏
2. **异常处理**: 始终处理可能的异常情况
3. **线程安全**: 回调可能在不同线程执行，注意线程安全
4. **资源管理**: 及时断开服务连接，释放资源
