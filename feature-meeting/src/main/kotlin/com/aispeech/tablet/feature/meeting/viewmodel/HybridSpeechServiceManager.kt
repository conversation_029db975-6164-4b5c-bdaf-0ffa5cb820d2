package com.aispeech.tablet.feature.meeting.viewmodel

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.IBinder
import android.os.RemoteException
import android.util.Log
import java.util.concurrent.atomic.AtomicBoolean
import com.aispeech.tablet.feature.meeting.utils.PackageUnstoppedHelper
import com.aispeech.hybridspeech.AudioRecordingConfig
import com.aispeech.hybridspeech.IHybridSpeechConfigProvider
import com.aispeech.hybridspeech.IHybridSpeechService
import com.aispeech.hybridspeech.IPauseRecordingCallback
import com.aispeech.hybridspeech.IRecordProgressCallback
import com.aispeech.hybridspeech.IResumeRecordingCallback
import com.aispeech.hybridspeech.IStartRecordingCallback
import com.aispeech.hybridspeech.IStopRecordingCallback
import com.aispeech.hybridspeech.ITranscriptionCallback
import com.aispeech.hybridspeech.Mp3EncodingConfig
import com.aispeech.hybridspeech.OfflineEngineConfig
import com.aispeech.hybridspeech.OfflineTranslationConfig
import com.aispeech.hybridspeech.OpusEncodingConfig

import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.RecordingProgressConfig
import com.aispeech.hybridspeech.RecordingResultInfo
import com.aispeech.tablet.core.common.Tablet
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeout
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.cancel
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * 混合语音识别服务管理器
 *
 * 提供统一的录音接口，支持在线和离线两种模式：
 * - 在线模式（默认）：支持ASR续传功能，网络中断时可以从断点继续语音识别
 * - 离线模式：使用本地模型进行语音识别
 */
class HybridSpeechServiceManager {

  companion object {
    private const val TAG = "HybridSpeechServiceManager"
    private const val SERVICE_PACKAGE = "com.aispeech.hybridspeech"
    private const val SERVICE_CLASS = "com.aispeech.hybridspeech.HybridSpeechService"
    private const val DEFAULT_PROGRESS_INTERVAL = 500

    // 重试相关常量
    private const val BINDING_TIMEOUT = 5000L // 5秒超时
    private const val MAX_RETRY_COUNT = 3 // 最大重试次数
  }

  sealed class RecordingProgressState {
    data object Started : RecordingProgressState()
    data class Progress(val durationMs: Long) : RecordingProgressState()
    data class Stopped(val totalDurationMs: Long) : RecordingProgressState()
    data class Error(val message: String) : RecordingProgressState()
  }

  private var hybridSpeechService: IHybridSpeechService? = null
  private var transcriptionCallback: ITranscriptionCallback? = null
  private var configProvider: IHybridSpeechConfigProvider? = null

  private val _connectionState = MutableStateFlow(false)
  val connectionState: StateFlow<Boolean> = _connectionState.asStateFlow()

  // 协程作用域用于管理异步操作
  private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

  // 重试相关变量
  private val bindingFlag = AtomicBoolean(false)
  private var bindingRetryCount = MAX_RETRY_COUNT
  private var bindingTimeoutJob: Job? = null

  // 普通函数：检查服务连接状态
  private fun requireServiceConnected(): IHybridSpeechService {
    return hybridSpeechService ?: throw IllegalStateException("Service not connected")
  }

  // 统一远程调用异常处理
  private fun <T> executeRemoteCall(operation: String, action: () -> T): T {
    return try {
      action()
    } catch (e: RemoteException) {
      Log.e(TAG, "Remote call failed for $operation", e)
      throw e
    }
  }

  private val serviceConnection = object : ServiceConnection {
    override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
      Log.d(TAG, "Service connected: $name")
      // 取消超时任务
      bindingTimeoutJob?.cancel()
      bindingTimeoutJob = null

      bindingFlag.set(false)
      bindingRetryCount = MAX_RETRY_COUNT // 重置重试计数

      hybridSpeechService = IHybridSpeechService.Stub.asInterface(service)
      _connectionState.value = true
      registerTranscriptionCallback()
    }

    override fun onServiceDisconnected(name: ComponentName?) {
      Log.w(TAG, "Service disconnected: $name")
      hybridSpeechService = null
      _connectionState.value = false
      bindingFlag.set(false)
      // 取消任何正在进行的超时任务
      bindingTimeoutJob?.cancel()
      bindingTimeoutJob = null
    }
  }

  private fun registerTranscriptionCallback() {
    transcriptionCallback?.let { callback ->
      runCatching {
        hybridSpeechService?.registerCallback(callback)
        Log.d(TAG, "Transcription callback re-registered")
      }.onFailure { e ->
        Log.e(TAG, "Failed to re-register transcription callback", e)
      }
    }

    // 同时重新注册配置提供者
    configProvider?.let { provider ->
      runCatching {
        hybridSpeechService?.registerConfigProvider(provider)
        Log.d(TAG, "Config provider re-registered")
      }.onFailure { e ->
        Log.e(TAG, "Failed to re-register config provider", e)
      }
    }
  }

  /**
   * 尝试绑定服务（带重试机制）
   */
  private fun tryBindService(): Boolean {
    if (bindingFlag.getAndSet(true)) {
      Log.d(TAG, "Service binding already in progress")
      return false
    }

    // 取消之前的超时任务
    bindingTimeoutJob?.cancel()

    // 在绑定服务前，先检查目标应用是否处于 stopped 状态，如果是则尝试激活
    try {
      val context = Tablet.application
      if (PackageUnstoppedHelper.isPackageStopped(context, SERVICE_PACKAGE)) {
        Log.i(TAG, "Target service package $SERVICE_PACKAGE is stopped, attempting to activate...")
        val activated = PackageUnstoppedHelper.ensurePackageActive(context, SERVICE_PACKAGE)
        if (!activated) {
          Log.w(TAG, "Failed to activate stopped package $SERVICE_PACKAGE, but will still try to bind")
        } else {
          Log.i(TAG, "Successfully activated package $SERVICE_PACKAGE")
        }
      }
    } catch (e: Exception) {
      Log.w(TAG, "Error checking/activating package state: ${e.message}")
    }

    // 启动超时协程
    bindingTimeoutJob = serviceScope.launch {
      delay(BINDING_TIMEOUT)
      // 超时处理
      Log.w(TAG, "Service binding timeout, retrying... (remaining: $bindingRetryCount)")
      bindingFlag.set(false)
      if (bindingRetryCount > 0) {
        tryBindService()
      } else {
        Log.e(TAG, "Service binding failed after all retries")
        _connectionState.value = false
      }
    }

    return runCatching {
      val intent = Intent().apply {
        component = ComponentName(SERVICE_PACKAGE, SERVICE_CLASS)
      }
      val bound = Tablet.application.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
      if (!bound) {
        Log.e(TAG, "bindService returned false")
        bindingFlag.set(false)
        bindingTimeoutJob?.cancel()
        bindingTimeoutJob = null
      }
      bindingRetryCount--
      bound
    }.fold(
      onSuccess = { it },
      onFailure = { e ->
        Log.e(TAG, "Error binding service", e)
        bindingFlag.set(false)
        bindingTimeoutJob?.cancel()
        bindingTimeoutJob = null
        false
      }
    )
  }

  fun connect(): Boolean {
    if (_connectionState.value) {
      Log.d(TAG, "Service already connected")
      return true
    }

    bindingRetryCount = MAX_RETRY_COUNT
    return tryBindService()
  }

  /**
   * 连接服务并等待连接完成
   * @param timeoutMs 超时时间，默认5秒
   * @return 是否连接成功
   */
  suspend fun connectAndWait(timeoutMs: Long = 5000): Boolean {
    if (_connectionState.value) {
      Log.d(TAG, "Service already connected")
      return true
    }

    val connected = connect()
    if (!connected) {
      Log.e(TAG, "Failed to initiate service binding")
      return false
    }

    // 等待连接完成
    return try {
      withTimeout(timeoutMs) {
        connectionState.first { it }
      }
      Log.d(TAG, "Service connected successfully")
      true
    } catch (e: TimeoutCancellationException) {
      Log.e(TAG, "Service connection timeout after ${timeoutMs}ms")
      false
    } catch (e: Exception) {
      Log.e(TAG, "Error waiting for service connection", e)
      false
    }
  }

  fun disconnect() {
    if (!_connectionState.value) return

    // 清理超时任务和重试状态
    bindingTimeoutJob?.cancel()
    bindingTimeoutJob = null
    bindingFlag.set(false)
    bindingRetryCount = MAX_RETRY_COUNT

    // 清理配置提供者
    unregisterConfigProvider()

    runCatching {
      Tablet.application.unbindService(serviceConnection)
    }.onFailure { e ->
      when (e) {
        is IllegalArgumentException -> Log.w(TAG, "Service not registered", e)
        else -> Log.e(TAG, "Error disconnecting service", e)
      }
    }

    hybridSpeechService = null
    _connectionState.value = false
  }

  /**
   * 清理资源
   */
  fun cleanup() {
    disconnect()
    serviceScope.cancel()
  }

  fun createProgressFlow(intervalMs: Int = DEFAULT_PROGRESS_INTERVAL): Flow<RecordingProgressState> =
    callbackFlow {
      val service = requireServiceConnected()

      val callback = object : IRecordProgressCallback.Stub() {
        override fun onRecordingStarted() {
          trySend(RecordingProgressState.Started)
        }
        override fun onRecordingProgress(durationMs: Long) {
          trySend(RecordingProgressState.Progress(durationMs))
        }
        override fun onRecordingStopped(totalDurationMs: Long) {
          trySend(RecordingProgressState.Stopped(totalDurationMs))
          close()
        }
        override fun onError(error: String?) {
          close(RuntimeException(error ?: "Unknown progress error"))
        }
      }

      executeRemoteCall("registerProgressCallback") {
        service.registerProgressCallback(callback, intervalMs)
        Log.d(TAG, "Progress callback registered")
      }

      awaitClose {
        if (_connectionState.value) {
          runCatching {
            hybridSpeechService?.unregisterProgressCallback(callback)
            Log.d(TAG, "Progress callback unregistered")
          }.onFailure {
            Log.w(TAG, "Failed to unregister progress callback", it)
          }
        }
      }
    }

  /**
   * 开始录音 - 业务参数接口
   * @param recordId 录音ID
   * @param userId 用户ID
   * @param pcmFilePath PCM文件路径
   * @param mp3FilePath MP3文件路径
   * @param language 语言，默认为"cn"
   * @param audioType 音频类型，默认为"ogg_opus"
   * @param translate 翻译目标语言，可空
   * @param useOnlineMode 是否使用在线模式，默认为true（在线模式支持ASR续传）
   * @param enableTranslation 是否启用翻译（离线模式）
   * @param offlineModelPath 离线模型路径（离线模式）
   */
  suspend fun startRecordingWithBusinessParams(
    recordId: Long,
    userId: String,
    pcmFilePath: String,
    mp3FilePath: String,
    language: String = "cn",
    audioType: String = "ogg_opus",
    translate: String? = null,
    useOnlineMode: Boolean = true,
    enableTranslation: Boolean = false,
    offlineModelPath: String? = null
  ) {
    // 确保服务已连接，如果未连接则先连接
    if (!_connectionState.value) {
      Log.d(TAG, "Service not connected, connecting now...")
      val connected = connectAndWait()
      if (!connected) {
        throw IllegalStateException("Failed to connect to service")
      }
    }

    val service = requireServiceConnected()

    return suspendCancellableCoroutine { continuation ->
      if (useOnlineMode) {
        // 在线模式：创建支持ASR续传的配置
        val config = RecordingConfig.createOnlineRecordingConfigWithResumeSupport(
          recordId = recordId,
          userId = userId,
          pcmFilePath = pcmFilePath,
          mp3FilePath = mp3FilePath,
          serverUrl = "", // 将由配置提供者填充
          apiKey = "", // 将由配置提供者填充
          language = language,
          audioType = audioType,
          translate = translate
        )

        Log.d(TAG, "开始在线录音: recordId=$recordId, audioType=$audioType")
        Log.d(TAG, "在线ASR配置: recordId=${config.onlineAsrConfig?.recordId}, userId=${config.onlineAsrConfig?.userId}")

        executeRemoteCall("startRecordingWithProvider") {
          val callback = object : IStartRecordingCallback.Stub() {
            override fun onStartRecordingSuccess() {
              Log.d(TAG, "在线录音启动成功")
              if (continuation.isActive) {
                continuation.resume(Unit)
              }
            }

            override fun onStartRecordingError(errorCode: Int, errorMessage: String?) {
              Log.e(TAG, "在线录音启动失败: errorCode=$errorCode, message=$errorMessage")
              if (continuation.isActive) {
                val error = RuntimeException("Online recording failed: errorCode=$errorCode, message=$errorMessage")
                continuation.resumeWithException(error)
              }
            }
          }

          service.startRecordingWithProvider(config, callback)
        }
      } else {

        val mappedLanguage = getOfflineLanguageDisplayName(language)
        val mappedTranslate = translate?.let { getOfflineLanguageDisplayName(it) }

        // 离线模式：创建离线配置
        val offlineConfig = createOfflineConfig(
          pcmFilePath = pcmFilePath,
          mp3FilePath = mp3FilePath,
          language = mappedLanguage,
          translate = mappedTranslate,
          enableTranslation = enableTranslation,
          offlineModelPath = offlineModelPath
        )

        Log.d(TAG, "开始离线录音: recordId=$recordId")

        executeRemoteCall("startRecordingWithConfig") {
          service.startRecordingWithConfigAsync(offlineConfig, object : IStartRecordingCallback.Stub() {
            override fun onStartRecordingSuccess() {
              Log.d(TAG, "离线录音启动成功")
              if (continuation.isActive) {
                continuation.resume(Unit)
              }
            }

            override fun onStartRecordingError(errorCode: Int, errorMessage: String?) {
              Log.e(TAG, "离线录音启动失败: errorCode=$errorCode, message=$errorMessage")
              if (continuation.isActive) {
                val error = RuntimeException("Offline recording failed: errorCode=$errorCode, message=$errorMessage")
                continuation.resumeWithException(error)
              }
            }
          })
        }
      }
    }
  }


  suspend fun stopRecording(): RecordingResultInfo? {
    val service = requireServiceConnected()

    return suspendCancellableCoroutine { continuation ->
      executeRemoteCall("stopRecording") {
        service.stopRecordingWithResultAsync(object : IStopRecordingCallback.Stub() {
          override fun onStopRecordingSuccess(result: RecordingResultInfo?) {
            Log.d(TAG, "录音停止成功")
            if (continuation.isActive) {
              continuation.resume(result)
            }
          }

          override fun onStopRecordingError(errorCode: Int, errorMessage: String?) {
            Log.e(TAG, "录音停止失败: errorCode=$errorCode, message=$errorMessage")
            if (continuation.isActive) {
              val error = RuntimeException("Stop recording failed: errorCode=$errorCode, message=$errorMessage")
              continuation.resumeWithException(error)
            }
          }
        })
      }

      continuation.invokeOnCancellation {
        Log.d(TAG, "Stop recording operation was cancelled")
      }
    }
  }

  /**
   * 暂停录音
   */
  suspend fun pauseRecording() {
    val service = requireServiceConnected()

    return suspendCancellableCoroutine { continuation ->
      executeRemoteCall("pauseRecording") {
        service.pauseRecordingAsync(object : IPauseRecordingCallback.Stub() {
          override fun onPauseRecordingSuccess(result: com.aispeech.hybridspeech.PauseRecordingResult) {
            val modeText = if (result.isOnlineMode) "在线" else "离线"
            val finalTextInfo = if (result.isOnlineMode) ", 最终文本: ${result.finalTextReceived}" else ""
            Log.d(TAG, "录音暂停成功 - 模式: $modeText, 时长: ${result.recordingDurationMs}ms$finalTextInfo")
            if (continuation.isActive) {
              continuation.resume(Unit)
            }
          }

          override fun onPauseRecordingError(errorCode: Int, errorMessage: String?) {
            Log.e(TAG, "录音暂停失败: errorCode=$errorCode, message=$errorMessage")
            if (continuation.isActive) {
              val error = RuntimeException("Pause recording failed: errorCode=$errorCode, message=$errorMessage")
              continuation.resumeWithException(error)
            }
          }
        })
      }

      continuation.invokeOnCancellation {
        Log.d(TAG, "Stop recording operation was cancelled")
      }
    }
  }

  /**
   * 继续录音
   */
  suspend fun resumeRecording() {
    val service = requireServiceConnected()

    return suspendCancellableCoroutine { continuation ->
      executeRemoteCall("resumeRecording") {
        service.resumeRecordingAsync(object : IResumeRecordingCallback.Stub() {
          override fun onResumeRecordingSuccess() {
            Log.d(TAG, "录音恢复成功")
            if (continuation.isActive) {
              continuation.resume(Unit)
            }
          }

          override fun onResumeRecordingError(errorCode: Int, errorMessage: String?) {
            Log.e(TAG, "录音恢复失败: errorCode=$errorCode, message=$errorMessage")
            if (continuation.isActive) {
              val error = RuntimeException("Resume recording failed: errorCode=$errorCode, message=$errorMessage")
              continuation.resumeWithException(error)
            }
          }
        })
      }

      continuation.invokeOnCancellation {
        Log.d(TAG, "Resume recording operation was cancelled")
      }
    }
  }

  fun registerTranscriptionCallback(callback: ITranscriptionCallback) {
    transcriptionCallback = callback
    if (_connectionState.value) {
      runCatching {
        hybridSpeechService?.registerCallback(callback)
      }.onFailure { e ->
        Log.e(TAG, "Failed to register transcription callback", e)
      }
    }
  }

  fun unregisterTranscriptionCallback() {
    transcriptionCallback?.let { callback ->
      if (_connectionState.value) {
        runCatching {
          hybridSpeechService?.unregisterCallback(callback)
        }.onFailure { e ->
          Log.e(TAG, "Failed to unregister transcription callback", e)
        }
      }
    }
    transcriptionCallback = null
  }

  /**
   * 获取当前的录音时长
   */
  fun getCurrentDuration(): Long? {
    return if (_connectionState.value) {
      runCatching {
        hybridSpeechService?.recordingDuration
      }.getOrElse { e ->
        Log.e(TAG, "Error getting current status", e)
        null
      }
    } else {
      null
    }
  }

  /**
   * 获取当前的状态
   */
  fun getCurrentStatus(): Int? {
    return if (_connectionState.value) {
      runCatching {
        hybridSpeechService?.currentStatus
      }.getOrElse { e ->
        Log.e(TAG, "Error getting current status", e)
        null
      }
    } else {
      null
    }
  }

  /**
   * 注册配置提供者
   * @param provider 配置提供者接口实现
   */
  fun registerConfigProvider(provider: IHybridSpeechConfigProvider) {
    configProvider = provider
    if (_connectionState.value) {
      runCatching {
        hybridSpeechService?.registerConfigProvider(provider)
        Log.d(TAG, "Config provider registered successfully")
      }.onFailure { e ->
        Log.e(TAG, "Failed to register config provider", e)
      }
    }
  }

  /**
   * 取消注册配置提供者
   */
  fun unregisterConfigProvider() {
    if (_connectionState.value) {
      runCatching {
        hybridSpeechService?.unregisterConfigProvider()
        Log.d(TAG, "Config provider unregistered successfully")
      }.onFailure { e ->
        Log.e(TAG, "Failed to unregister config provider", e)
      }
    }
    configProvider = null
  }

  /**
   * 创建离线录音配置
   */
  private fun createOfflineConfig(
    pcmFilePath: String,
    mp3FilePath: String,
    language: String,
    translate: String? = null,
    enableTranslation: Boolean,
    offlineModelPath: String?
  ): RecordingConfig {
    return RecordingConfig(
      useOnlineMode = false,
      language = language, // 直接使用已映射的语言
      translate = translate, // 直接使用已映射的翻译语言
      pcmFilePath = pcmFilePath,
      mp3FilePath = mp3FilePath,
      audioConfig = AudioRecordingConfig.createDefault(),
      mp3Config = Mp3EncodingConfig.createDefault(),
      opusConfig = OpusEncodingConfig.createDefault(),
      progressConfig = RecordingProgressConfig.createDefault(),
      onlineAsrConfig = null,
      offlineEngineConfig = if (offlineModelPath != null) {
        OfflineEngineConfig(
          modelPath = offlineModelPath,
          enableVAD = true,
          vadThreshold = 0.5f
        )
      } else null,
      offlineTranslationConfig = if (enableTranslation) {
        OfflineTranslationConfig.createEnabled(
          taskName = "trans_dialect",
          fromLanguage = language,
          toLanguage = translate ?: "English",
          domain = "comm",
          resourcePath = offlineModelPath ?: ""
        )
      } else null
    )
  }


  /**
   * 根据语言代码获取离线模式下的语言显示名称
   */
  private fun getOfflineLanguageDisplayName(languageCode: String): String {
    return when (languageCode) {
      "cn" -> "Chinese"
      "en" -> "English"
      "ce" -> "Chinese-English"
      "ja" -> "Japanese"
      "ko" -> "Korean"
      "fr" -> "French"
      "de" -> "German"
      "es" -> "Spanish"
      "it" -> "Italian"
      "pt" -> "Portuguese"
      "ru" -> "Russian"
      else -> "Unknown"
    }
  }

}
