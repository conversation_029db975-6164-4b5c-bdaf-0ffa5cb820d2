/*
 * Copyright (C) 2023 AISPEECH. All rights reserved.
 *
 * This software is the confidential and proprietary information of AI Speech Co., Ltd..
 * Unauthorized copying, modification, publication, or use of this software, either
 * in part or in whole, is strictly prohibited without the prior written consent of <PERSON><PERSON><PERSON><PERSON>.
 *
 * For authorization inquiries, please contact AISPEECH at www.aispeech.com.
 */

plugins {
  id("com.android.library")
  id("org.jetbrains.kotlin.android")
  id("org.jetbrains.kotlin.plugin.parcelize")
  id("org.jetbrains.kotlin.plugin.serialization") version "1.9.25"
}

android {
  namespace = "com.aispeech.modellibs"
  compileSdk = Configurations.compileSdk

  defaultConfig {
    minSdk = Configurations.minSdk
    targetSdk = Configurations.targetSdk
  }

  buildFeatures {
    aidl = true
  }

  compileOptions {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
  }
  kotlinOptions {
    jvmTarget = "1.8"
  }
}

dependencies {
  api("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3")
  api(libs.kotlinx.collections.immutable)

  implementation(project(":lib-ailog"))
  implementation("androidx.compose.runtime:runtime:1.7.5")


  testImplementation("junit:junit:4.13.2")
  testImplementation("org.jetbrains.kotlin:kotlin-test-junit:1.9.25")
  testImplementation("org.mockito:mockito-core:4.6.1")
  testImplementation("org.mockito.kotlin:mockito-kotlin:4.1.0")
  testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:1.7.3")
  testImplementation("androidx.test:core:1.5.0")
  testImplementation("org.robolectric:robolectric:4.10.3")

  androidTestImplementation("androidx.test.ext:junit:1.1.5")
  androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
}