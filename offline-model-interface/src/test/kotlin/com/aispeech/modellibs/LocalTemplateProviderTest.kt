package com.aispeech.modellibs

import android.content.Context
import com.aispeech.modellibs.template.LocalTemplateProvider
import com.aispeech.modellibs.template.ScenarioType
import kotlinx.coroutines.async
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import org.mockito.kotlin.whenever
import org.robolectric.RobolectricTestRunner
import org.robolectric.RuntimeEnvironment
import org.robolectric.annotation.Config
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue

@RunWith(RobolectricTestRunner::class)
@Config(sdk = [28])
class LocalTemplateProviderTest {

  private lateinit var context: Context
  private lateinit var templateProvider: LocalTemplateProvider

  @Mock
  private lateinit var mockContext: Context

  @Before
  fun setUp() {
    MockitoAnnotations.openMocks(this)
    context = RuntimeEnvironment.getApplication()
    templateProvider = LocalTemplateProvider(context)
  }

  /**
   * 测试模板加载及缓存机制。
   * 由于Robolectric环境可能找不到资源，测试验证方法能正常处理成功（返回字符串）或失败（返回null）的情况。
   */
  @Test
  fun `test getTemplate success and cache`() = runTest {
    val scenarioType = ScenarioType.OTHER

    // 首次调用
    val firstResult = templateProvider.getTemplate(scenarioType)

    // 验证：结果要么是null（资源未找到），要么是一个非空字符串。方法不应崩溃。
    assertTrue(
      firstResult == null || firstResult.isNotEmpty(),
      "Result should be null (if resource not found) or a non-empty string."
    )

    // 再次调用，应命中缓存
    val secondResult = templateProvider.getTemplate(scenarioType)

    // 验证：第二次调用的结果必须与第一次相同，以证明缓存有效。
    assertEquals(firstResult, secondResult, "Subsequent calls should return the same result (from cache).")
  }

  /**
   * 测试当模板资源无法找到时的错误处理。
   * 模拟一个无效的上下文，预期返回null。
   */
  @Test
  fun `test getTemplate when resource not found`() = runTest {
    // 模拟一个找不到资源的上下文
    whenever(mockContext.packageName).thenReturn("com.nonexistent.package")
    whenever(mockContext.applicationContext).thenReturn(mockContext)
    val mockTemplateProvider = LocalTemplateProvider(mockContext)

    val result = mockTemplateProvider.getTemplate(ScenarioType.CONSULTATION_INTERVIEW)

    assertNull(result, "Should return null when resource is not found.")
  }

  /**
   * 测试并发访问的线程安全性。
   * 多个协程同时请求同一个模板，应返回一致的结果。
   */
  @Test
  fun `test concurrent access`() = runTest {
    val scenarioType = ScenarioType.OTHER

    // 并发请求模板
    val results = (1..10).map {
      async { templateProvider.getTemplate(scenarioType) }
    }.map { it.await() }

    // 验证所有结果都与第一个结果相同，确保缓存一致性
    val firstResult = results.first()
    results.forEach {
      assertEquals(firstResult, it, "Concurrent access should return consistent results.")
    }
  }
}
