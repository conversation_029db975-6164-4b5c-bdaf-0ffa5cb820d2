package com.aispeech.modellibs

import android.os.Parcel
import android.os.Parcelable
import kotlinx.serialization.Serializable

/**
 * 模型服务状态数据类
 *
 * 包含服务的基础状态信息，支持 AIDL 传输和序列化。
 *
 * @property statusCode 主要状态代码，使用 ServiceStatusCode 中定义的常量
 * @property statusMessage 状态描述信息
 * @property timestamp 状态更新时间戳（毫秒）
 * @property isAuthorized 应用是否已授权
 * @property isInitialized 应用是否已初始化
 * @property isServiceSupported 当前设备是否支持此服务
 * @property activeTaskCount 当前活跃任务数量
 * @property isTaskManagerRunning 任务管理器是否运行中
 * @property lastError 最后一次错误信息
 */
@Serializable
data class ModelServiceStatus(
  val statusCode: Int,
  val statusMessage: String,
  val timestamp: Long = System.currentTimeMillis(),
  val isAuthorized: Boolean = false,
  val isInitialized: Boolean = false,
  val isServiceSupported: Boolean = false,
  val activeTaskCount: Int = 0,
  val isTaskManagerRunning: Boolean = false,
  val lastError: String? = null,
  val isServiceComponentsReady: Boolean = false
) : Parcelable {

  constructor(parcel: Parcel) : this(
    statusCode = parcel.readInt(),
    statusMessage = parcel.readString() ?: "",
    timestamp = parcel.readLong(),
    isAuthorized = parcel.readByte() != 0.toByte(),
    isInitialized = parcel.readByte() != 0.toByte(),
    isServiceSupported = parcel.readByte() != 0.toByte(),
    activeTaskCount = parcel.readInt(),
    isTaskManagerRunning = parcel.readByte() != 0.toByte(),
    lastError = parcel.readString(),
    isServiceComponentsReady = parcel.readByte() != 0.toByte(),
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeInt(statusCode)
    parcel.writeString(statusMessage)
    parcel.writeLong(timestamp)
    parcel.writeByte(if (isAuthorized) 1 else 0)
    parcel.writeByte(if (isInitialized) 1 else 0)
    parcel.writeByte(if (isServiceSupported) 1 else 0)
    parcel.writeInt(activeTaskCount)
    parcel.writeByte(if (isTaskManagerRunning) 1 else 0)
    parcel.writeString(lastError)
    parcel.writeByte(if (isServiceComponentsReady) 1 else 0)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<ModelServiceStatus> {
    override fun createFromParcel(parcel: Parcel): ModelServiceStatus {
      return ModelServiceStatus(parcel)
    }

    override fun newArray(size: Int): Array<ModelServiceStatus?> {
      return arrayOfNulls(size)
    }
  }

  /**
   * 检查服务是否处于健康状态
   */
  fun isHealthy(): Boolean {
    return statusCode == ServiceStatusCode.RUNNING ||
      statusCode == ServiceStatusCode.IDLE &&
      isInitialized &&
      isAuthorized &&
      isServiceSupported &&
      lastError == null
  }

  /**
   * 检查服务是否正在运行
   */
  fun isRunning(): Boolean {
    return statusCode == ServiceStatusCode.RUNNING
  }

  /**
   * 检查服务是否处于错误状态
   */
  fun hasError(): Boolean {
    return statusCode == ServiceStatusCode.ERROR || lastError != null
  }

  /**
   * 检查服务是否完全就绪（可以接受任务）
   */
  fun isReady(): Boolean {
    return isHealthy() &&
      isInitialized &&
      isAuthorized &&
      isServiceSupported &&
      isTaskManagerRunning
  }

  /**
   * 获取状态的可读描述
   */
  fun getReadableStatus(): String {
    return when (statusCode) {
      ServiceStatusCode.INITIALIZING -> "初始化中"
      ServiceStatusCode.IDLE -> "空闲"
      ServiceStatusCode.RUNNING -> "运行中"
      ServiceStatusCode.BUSY -> "繁忙"
      ServiceStatusCode.ERROR -> "错误"
      ServiceStatusCode.STOPPING -> "停止中"
      ServiceStatusCode.STOPPED -> "已停止"
      else -> "未知状态($statusCode)"
    }
  }
}

/**
 * 服务状态代码常量
 */
object ServiceStatusCode {
  const val INITIALIZING = 0  // 初始化中
  const val IDLE = 1          // 空闲，可以接受新任务
  const val RUNNING = 2       // 运行中，正在处理任务
  const val BUSY = 3          // 繁忙，任务队列已满
  const val ERROR = 4         // 错误状态
  const val STOPPING = 5      // 正在停止
  const val STOPPED = 6       // 已停止
}














