package com.aispeech.modellibs.template

/**
 * 定义了所有支持的业务场景模板。
 * @param defaultTemplateId 内置的、默认的模板版本ID，可用于缓存key或版本控制。
 * @param resourceName 模板的通用资源名。对于本地模板，它对应res/raw下的文件名(不含扩展名)；
 *
 */
enum class ScenarioType(val defaultTemplateId: String, val resourceName: String) {
  BUSINESS_MEETING_MINUTES("business_meeting_minutes_v1", "business_meeting_minutes"),
  GOVERNMENT_MEETING_MINUTES("government_meeting_minutes_v1", "government_meeting_minutes"),
  CONSULTATION_INTERVIEW("consultation_interview_v1", "consultation_interview"),
  INTERVIEW_RECORD("interview_record_v1", "interview_record"),
  COURSE_TRAINING("course_training_v1", "course_training"),
  OTHER("other_v1", "other");
}