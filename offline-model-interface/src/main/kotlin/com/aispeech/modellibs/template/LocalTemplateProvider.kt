package com.aispeech.modellibs.template

import android.content.Context
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap

/**
 * 只从本地 res/raw 加载模板的初始实现。
 */
class LocalTemplateProvider(private val context: Context) : ITemplateProvider {

  companion object {
    private const val TAG = "LocalTemplateProvider"
  }

  // 内存缓存
  private val cache = ConcurrentHashMap<String, String>()

  override suspend fun getTemplate(type: ScenarioType): String? {
    val templateId = type.defaultTemplateId

    // 优先从缓存获取
    cache[templateId]?.let {
      Log.d(TAG, "Template '${type.resourceName}' found in cache.")
      return it
    }

    // 从 res/raw 异步加载
    return withContext(Dispatchers.IO) {
      val resId = context.resources.getIdentifier(type.resourceName, "raw", context.packageName)

      if (resId == 0) {
        Log.e(TAG, "No resource found in res/raw for name: '${type.resourceName}'")
        return@withContext null
      }

      try {
        val content = context.resources.openRawResource(resId)
          .bufferedReader().use { it.readText() }

        cache[templateId] = content
        Log.i(TAG, "Template '${type.resourceName}' loaded from res/raw and cached.")
        content
      } catch (e: Exception) {
        Log.e(TAG, "Error loading template from res/raw: $templateId", e)
        null
      }
    }
  }
}
