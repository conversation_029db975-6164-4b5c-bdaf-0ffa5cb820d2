package com.aispeech.modellibs.template

import android.content.Context

/**
 * 模板管理器的单例入口。
 * 负责创建和持有 ITemplateProvider 的实例。
 */
object TemplateManager {

    private lateinit var provider: ITemplateProvider

    /**
     * 在 Application.onCreate 中调用进行初始化。
     */
    fun initialize(context: Context) {
        if (!::provider.isInitialized) {
            provider = LocalTemplateProvider(context.applicationContext)
        }
    }

    /**
     * 获取模板提供者实例。
     */
    fun getProvider(): ITemplateProvider {
        if (!::provider.isInitialized) {
            error("TemplateManager must be initialized first in Application.onCreate().")
        }
        return provider
    }
}
