package com.aispeech.modellibs.example

import android.content.Context
import android.util.Log
import com.aispeech.modellibs.ITaskStateCallback
import com.aispeech.modellibs.ModelServiceClient
import com.aispeech.modellibs.TaskState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * 异步任务状态查询使用示例
 */
class TaskStateAsyncExample(private val context: Context) {
    
    companion object {
        private const val TAG = "TaskStateAsyncExample"
    }
    
    private val client = ModelServiceClient(context)
    private val scope = CoroutineScope(Dispatchers.Main)
    
    /**
     * 示例1：使用协程版本的异步查询
     */
    fun exampleCoroutineVersion(taskId: String) {
        scope.launch {
            try {
                // 连接服务
                client.connect()
                
                // 使用协程版本的异步查询
                val taskState = client.getTaskStateAsync(taskId)
                
                if (taskState != null) {
                    Log.d(TAG, "Task state retrieved: ${taskState.getReadableStatus()}")
                    when {
                        taskState.isCompleted() -> {
                            Log.d(TAG, "Task completed successfully: ${taskState.result}")
                        }
                        taskState.isFailed() -> {
                            Log.e(TAG, "Task failed: ${taskState.errorMessage}")
                        }
                        taskState.isExecuting() -> {
                            Log.d(TAG, "Task is still executing...")
                        }
                        taskState.isQueued() -> {
                            Log.d(TAG, "Task is queued for execution")
                        }
                    }
                } else {
                    Log.w(TAG, "Task not found or query failed: $taskId")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error in coroutine version example", e)
            }
        }
    }
    
    /**
     * 示例2：使用传统回调版本的异步查询
     */
    fun exampleCallbackVersion(taskId: String) {
        scope.launch {
            try {
                // 连接服务
                client.connect()
                
                // 创建回调接口
                val callback = object : ITaskStateCallback.Stub() {
                    override fun onTaskStateRetrieved(taskState: TaskState?) {
                        taskState?.let {
                            Log.d(TAG, "Callback: Task state retrieved: ${it.getReadableStatus()}")
                            
                            when {
                                it.isCompleted() -> {
                                    Log.d(TAG, "Callback: Task completed: ${it.result}")
                                }
                                it.isFailed() -> {
                                    Log.e(TAG, "Callback: Task failed: ${it.errorMessage}")
                                }
                                it.isExecuting() -> {
                                    Log.d(TAG, "Callback: Task is executing...")
                                }
                                it.isQueued() -> {
                                    Log.d(TAG, "Callback: Task is queued")
                                }
                            }
                        }
                    }
                    
                    override fun onTaskNotFound(taskId: String?) {
                        Log.w(TAG, "Callback: Task not found: $taskId")
                    }
                    
                    override fun onQueryError(taskId: String?, errorCode: Int, errorMessage: String?) {
                        Log.e(TAG, "Callback: Query error for $taskId: $errorCode - $errorMessage")
                    }
                }
                
                // 使用传统回调版本的异步查询
                client.getTaskStateAsync(taskId, callback)
                
            } catch (e: Exception) {
                Log.e(TAG, "Error in callback version example", e)
            }
        }
    }
    
    /**
     * 示例3：轮询任务状态直到完成
     */
    fun examplePollingUntilComplete(taskId: String) {
        scope.launch {
            try {
                client.connect()
                
                var isComplete = false
                var retryCount = 0
                val maxRetries = 30 // 最多轮询30次
                
                while (!isComplete && retryCount < maxRetries) {
                    val taskState = client.getTaskStateAsync(taskId)
                    
                    if (taskState != null) {
                        Log.d(TAG, "Polling attempt $retryCount: ${taskState.getReadableStatus()}")
                        
                        when {
                            taskState.isFinished() -> {
                                isComplete = true
                                if (taskState.isCompleted()) {
                                    Log.d(TAG, "Task completed after $retryCount attempts: ${taskState.result}")
                                } else {
                                    Log.e(TAG, "Task failed after $retryCount attempts: ${taskState.errorMessage}")
                                }
                            }
                            else -> {
                                // 等待2秒后重试
                                kotlinx.coroutines.delay(2000)
                                retryCount++
                            }
                        }
                    } else {
                        Log.w(TAG, "Task not found during polling: $taskId")
                        break
                    }
                }
                
                if (!isComplete) {
                    Log.w(TAG, "Task polling timeout after $maxRetries attempts")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "Error in polling example", e)
            }
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        client.disconnect()
    }
}
