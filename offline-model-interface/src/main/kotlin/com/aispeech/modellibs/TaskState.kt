package com.aispeech.modellibs

import android.os.Parcel
import android.os.Parcelable
import kotlinx.serialization.Serializable

/**
 * 任务状态数据类
 *
 * 包含任务的核心状态信息，支持 AIDL 传输和序列化。
 *
 * @property taskId 任务ID
 * @property taskType 任务类型，使用 TaskType 中定义的常量
 * @property status 任务状态，使用 TaskStatus 中定义的常量
 * @property result 任务执行结果，成功时包含结果数据，失败或未完成时为 null
 * @property errorMessage 错误信息，任务失败时设置
 */
@Serializable
data class TaskState(
  val taskId: String,
  val taskType: Int,
  val status: Int,
  val result: String? = null,
  val errorMessage: String? = null
) : Parcelable {

  constructor(parcel: Parcel) : this(
    taskId = parcel.readString() ?: "",
    taskType = parcel.readInt(),
    status = parcel.readInt(),
    result = parcel.readString(),
    errorMessage = parcel.readString()
  )

  override fun writeToParcel(parcel: Parcel, flags: Int) {
    parcel.writeString(taskId)
    parcel.writeInt(taskType)
    parcel.writeInt(status)
    parcel.writeString(result)
    parcel.writeString(errorMessage)
  }

  override fun describeContents(): Int = 0

  companion object CREATOR : Parcelable.Creator<TaskState> {
    override fun createFromParcel(parcel: Parcel): TaskState {
      return TaskState(parcel)
    }

    override fun newArray(size: Int): Array<TaskState?> {
      return arrayOfNulls(size)
    }
  }

  /**
   * 检查任务是否已完成（成功或失败）
   */
  fun isCompleted(): Boolean {
    return status == TaskStatus.COMPLETED
  }

  /**
   * 检查任务是否已结束（包括成功、失败、取消）
   */
  fun isFinished(): Boolean {
    return status == TaskStatus.COMPLETED || status == TaskStatus.FAILED || status == TaskStatus.CANCELLED
  }

  /**
   * 检查任务是否正在执行
   */
  fun isExecuting(): Boolean {
    return status == TaskStatus.EXECUTING
  }

  /**
   * 检查任务是否在队列中等待
   */
  fun isQueued(): Boolean {
    return status == TaskStatus.QUEUED
  }

  /**
   * 检查任务是否被取消
   */
  fun isCancelled(): Boolean {
    return status == TaskStatus.CANCELLED
  }

  /**
   * 检查任务是否失败
   */
  fun isFailed(): Boolean {
    return status == TaskStatus.FAILED
  }

  /**
   * 获取可读的任务类型描述
   */
  fun getReadableTaskType(): String {
    return fromDatabaseValueToTaskTypeByte(taskType)?.toApiKey() ?: ""
  }

  /**
   * 获取可读的状态描述
   */
  fun getReadableStatus(): String {
    return when (status) {
      TaskStatus.QUEUED -> "队列中"
      TaskStatus.EXECUTING -> "执行中"
      TaskStatus.COMPLETED -> "已完成"
      TaskStatus.FAILED -> "失败"
      TaskStatus.CANCELLED -> "已取消"
      else -> "未知状态($status)"
    }
  }
}


/**
 * 任务状态常量
 */
object TaskStatus {
  const val QUEUED = 0      // 队列中等待
  const val EXECUTING = 1   // 正在执行
  const val COMPLETED = 2   // 执行完成
  const val FAILED = 3      // 执行失败
  const val CANCELLED = 4   // 已取消
}


