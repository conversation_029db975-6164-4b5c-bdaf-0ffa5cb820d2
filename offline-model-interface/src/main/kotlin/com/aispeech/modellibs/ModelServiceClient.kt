package com.aispeech.modellibs

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.IBinder
import android.os.MemoryFile
import android.os.ParcelFileDescriptor
import android.os.RemoteException
import com.aispeech.aibase.AILog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeout
import java.io.FileDescriptor
import java.lang.reflect.Method
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.resume

/**
 * 离线模型服务异步客户端
 */
class ModelServiceClient(
  private val context: Context,
  private val servicePackage: String = DEFAULT_SERVICE_PACKAGE,
  private val serviceClass: String = DEFAULT_SERVICE_CLASS
) {

  companion object {
    private const val TAG = "ModelServiceClient"
    private const val DEFAULT_SERVICE_PACKAGE = "com.aispeech.modellibs"
    private const val DEFAULT_SERVICE_CLASS = "com.aispeech.modellibs.OfflineModelService"
//    private const val DEFAULT_SERVICE_ACTION = "com.aispeech.modellibs.ACTION_MODEL_LIBS"

    private const val UNFREEZE_ACTIVITY_NAME = ".UnfreezeActivity"
    private const val MAX_RETRY_COUNT = 3
    private const val BINDING_TIMEOUT = 5000L

    // 小于此阈值使用普通字符串传输，大于等于此阈值使用 ParcelFileDescriptor
    private const val STRING_TRANSMISSION_THRESHOLD = 1024 * 8 // 8KB
  }

  /**
   * 任务结果状态
   */
  sealed class TaskResult<T> {
    data class Success<T>(val data: T) : TaskResult<T>()
    data class Error<T>(val errorCode: Int, val errorMessage: String) : TaskResult<T>()
  }

  // 服务相关
  private var modelService: IModelService? = null

  // 连接状态
  private val _connectionState = MutableStateFlow(false)
  val connectionState: StateFlow<Boolean> = _connectionState.asStateFlow()

  // 协程作用域
  private var clientJob = SupervisorJob()
  private val clientScope = CoroutineScope(clientJob + Dispatchers.IO)

  // 重试相关变量
  private val bindingFlag = AtomicBoolean(false)
  private var bindingRetryCount = MAX_RETRY_COUNT
  private var bindingTimeoutJob: Job? = null

  /**
   * 检查服务连接状态
   */
  private fun requireServiceConnected(): IModelService {
    return modelService ?: error("Service not connected")
  }

  /**
   * 统一远程调用异常处理
   */
  private fun <T> executeRemoteCall(operation: String, action: () -> T): T {
    return try {
      action()
    } catch (e: RemoteException) {
      AILog.e(TAG, "Remote call failed for $operation", e)
      throw e
    }
  }

  /**
   * 服务连接回调
   */
  private val serviceConnection = object : ServiceConnection {
    override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
      AILog.d(TAG, "Service connected: $name")

      // 取消超时任务
      bindingTimeoutJob?.cancel()
      bindingTimeoutJob = null

      bindingFlag.set(false)
      bindingRetryCount = MAX_RETRY_COUNT // 重置重试计数

      modelService = IModelService.Stub.asInterface(service)
      _connectionState.value = true
    }

    override fun onServiceDisconnected(name: ComponentName?) {
      AILog.w(TAG, "Service disconnected: $name")
      modelService = null
      _connectionState.value = false
      bindingFlag.set(false)

      // 取消任何正在进行的超时任务
      bindingTimeoutJob?.cancel()
      bindingTimeoutJob = null
    }
  }

  /**
   * 检查应用是否处于 stopped 状态
   */
  private fun isPackageStopped(packageName: String): Boolean {
    return try {
      val packageManager = context.packageManager
      val applicationInfo =
        packageManager.getApplicationInfo(packageName, PackageManager.GET_META_DATA)

      val isStopped = (applicationInfo.flags and ApplicationInfo.FLAG_STOPPED) != 0
      AILog.d(TAG, "Package $packageName stopped state: $isStopped")
      isStopped
    } catch (e: PackageManager.NameNotFoundException) {
      AILog.w(TAG, "Package $packageName not found")
      false
    } catch (e: Exception) {
      AILog.e(TAG, "Error checking package stopped state", e)
      false
    }
  }

  /**
   * 尝试激活应用包
   */
  private suspend fun ensurePackageActive(packageName: String): Boolean {
    if (!isPackageStopped(packageName)) {
      AILog.i(TAG, "Package $packageName is not stopped, no activation needed")
      return true
    }

    return try {
      AILog.i(TAG, "Attempting to activate package: $packageName")
      val unfreezeIntent = Intent().apply {
        val fullActivityName = "$packageName$UNFREEZE_ACTIVITY_NAME"
        component = ComponentName(packageName, fullActivityName)
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      }

      context.startActivity(unfreezeIntent)
      AILog.i(TAG, "Sent intent to start UnfreezeActivity for package: $packageName")

      // 短暂等待后重新检查
      repeat(3) { attempt ->
        delay(200)
        if (!isPackageStopped(packageName)) {
          AILog.i(TAG, "Package $packageName activated after ${(attempt + 1) * 200}ms")
          return true
        }
      }

      AILog.i(TAG, "Package $packageName still stopped after polling.")
      false
    } catch (e: Exception) {
      AILog.i(TAG, "Failed to activate package $packageName with exception: ${e.message}")
      false
    }
  }

  /**
   * 尝试绑定服务（带重试机制）
   */
  private suspend fun tryBindService(): Boolean {
    if (bindingFlag.getAndSet(true)) {
      AILog.d(TAG, "Service binding already in progress")
      return false
    }

    if (bindingRetryCount <= 0) {
      AILog.e(TAG, "Service binding retry count exhausted")
      bindingFlag.set(false)
      return false
    }

    bindingRetryCount--
    AILog.d(TAG, "Attempting to bind service, retries left: $bindingRetryCount")

    return try {
      // 确保目标应用处于活跃状态
      if (!ensurePackageActive(servicePackage)) {
        AILog.w(TAG, "Failed to activate target package: $servicePackage")
      }

      val intent = Intent().apply {
        component = ComponentName(servicePackage, serviceClass)
      }

      val bindResult = context.bindService(
        intent,
        serviceConnection,
        Context.BIND_AUTO_CREATE or Context.BIND_IMPORTANT
      )

      if (bindResult) {
        AILog.d(TAG, "Service binding initiated successfully")

        // 设置超时任务
        bindingTimeoutJob = clientScope.launch {
          delay(BINDING_TIMEOUT)
          if (!_connectionState.value && bindingFlag.get()) {
            AILog.w(TAG, "Service binding timeout, attempting retry")
            context.unbindService(serviceConnection)
            bindingFlag.set(false)

            // 如果还有重试次数，继续尝试
            if (bindingRetryCount > 0) {
              delay(1000) // 等待1秒后重试
              tryBindService()
            } else {
              AILog.e(TAG, "Service binding failed after all retries")
            }
          }
        }
      } else {
        AILog.e(TAG, "Failed to initiate service binding")
        bindingFlag.set(false)
      }

      bindResult
    } catch (e: Exception) {
      AILog.e(TAG, "Exception during service binding", e)
      bindingFlag.set(false)
      false
    }
  }

  /**
   * 连接服务
   * @return 是否成功启动连接过程
   */
  suspend fun connect(): Boolean {
    if (_connectionState.value) {
      AILog.d(TAG, "Service already connected")
      return true
    }

    bindingRetryCount = MAX_RETRY_COUNT
    return tryBindService()
  }

  /**
   * 连接服务并等待连接完成
   * @param timeoutMs 超时时间，默认5秒
   * @return 是否连接成功
   */
  suspend fun connectAndWait(timeoutMs: Long = 5000): Boolean {
    if (_connectionState.value) {
      AILog.d(TAG, "Service already connected")
      return true
    }

    if (!connect()) {
      return false
    }

    return try {
      withTimeout(timeoutMs) {
        connectionState.first { it }
      }
    } catch (e: TimeoutCancellationException) {
      AILog.e(TAG, "Connection timeout after ${timeoutMs}ms")
      false
    }
  }

  /**
   * 连接服务并等待连接完成以及服务准备好（默认先使用这个）
   * @param timeoutMs 超时时间，默认5秒
   * @return 是否连接成功
   */
  suspend fun connectAndGetReady(timeoutMs: Long = 15000): Boolean {
    return try {
      withTimeout(timeoutMs) {
        if (!connectAndWait(timeoutMs)) {
          AILog.i(TAG, "Physical connection failed within the given timeout.")
          return@withTimeout false
        }
        AILog.i(TAG, "Physical service connection established. Now waiting for service to be ready...")

        val currentStatus = getCurrentStatus()
        if (currentStatus?.isServiceComponentsReady == true) {
          AILog.i(TAG, "Service was already ready upon connection.")
          return@withTimeout true
        }

        createStatusFlow("wait_for_ready").first { status ->
          if (status.statusCode == ServiceStatusCode.ERROR) {
            error("Service entered an error state while waiting to get ready: ${status.lastError}")
          }
          status.isServiceComponentsReady
        }

        AILog.i(TAG, "Service is now fully ready.")
        true
      }
    } catch (e: TimeoutCancellationException) {
      AILog.i(TAG, "connectAndGetReady timed out after ${timeoutMs}ms.")
      false
    } catch (e: Exception) {
      AILog.i(TAG, "connectAndGetReady failed with an exception ${e.message}.")
      false
    }
  }

  /**
   * 检查设备是否支持 APUSys 硬件
   * 比下面的更简洁，不需要使用下面的方式了
   */
  val isHardwareSupported: Boolean by lazy {
    CapabilityUtils.isApuSysSupported()
  }


  /**
   * 检查设备是否支持 APUSys 硬件
   * @return 是否支持 APUSys 硬件，如果服务未连接则返回 false
   */
  fun isApuSysSupported(): Boolean {
    return try {
      val service = requireServiceConnected()
      executeRemoteCall("isApuSysSupported") {
        service.isApuSysSupported()
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Error checking APUSys support", e)
      false
    }
  }

  // ============ 服务状态管理功能 ============

  // 状态回调管理
  private val statusCallbacks = mutableMapOf<String, IModelStatusCallback>()

  /**
   * 直接获取当前服务状态（同步方法）
   *
   * @return 当前服务状态，如果服务未连接则返回 null
   */
  suspend fun getCurrentStatus(): ModelServiceStatus? {
    return try {
      val service = requireServiceConnected()
      executeRemoteCall("getCurrentStatus") {
        service.getCurrentStatus()
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Error getting current status", e)
      null
    }
  }

  /**
   * 创建服务状态变化流（异步流式方法）
   *
   * @param callbackId 回调标识符，用于区分不同的监听器
   * @return 状态变化流
   */
  fun createStatusFlow(callbackId: String = "default"): Flow<ModelServiceStatus> = callbackFlow {
    val service = try {
      requireServiceConnected()
    } catch (e: Exception) {
      close(e)
      return@callbackFlow
    }

    val callback = object : IModelStatusCallback.Stub() {
      override fun onStatusChanged(status: ModelServiceStatus?) {
        status?.let {
          trySend(it)
          AILog.d(TAG, "Status changed: ${it.getReadableStatus()}")
        }
      }

      override fun onRegistrationSuccess(initialStatus: ModelServiceStatus?) {
        initialStatus?.let {
          trySend(it)
          AILog.d(TAG, "Registration successful, initial status: ${it.getReadableStatus()}")
        }
      }

      override fun onRegistrationFailed(errorCode: Int, errorMessage: String?) {
        close(RuntimeException("Registration failed: $errorCode - $errorMessage"))
      }

      override fun onConnectionLost(reason: String?) {
        close(RuntimeException("Connection lost: $reason"))
      }

      override fun onServiceError(
        errorCode: Int,
        errorMessage: String?,
        currentStatus: ModelServiceStatus?
      ) {
        AILog.e(TAG, "Service error: $errorCode - $errorMessage")
        currentStatus?.let { trySend(it) }
      }
    }

    try {
      // 注册回调
      val success = executeRemoteCall("registerStatusCallback") {
        service.registerStatusCallback(callback)
      }

      if (!success) {
        close(RuntimeException("Failed to register status callback"))
        return@callbackFlow
      }

      // 保存回调引用
      statusCallbacks[callbackId] = callback
      AILog.d(TAG, "Status callback registered with ID: $callbackId")

      awaitClose {
        try {
          executeRemoteCall("unregisterStatusCallback") {
            service.unregisterStatusCallback(callback)
          }
          statusCallbacks.remove(callbackId)
          AILog.d(TAG, "Status callback unregistered: $callbackId")
        } catch (e: Exception) {
          AILog.e(TAG, "Error unregistering callback", e)
        }
      }
    } catch (e: Exception) {
      close(e)
    }
  }

  /**
   * 断开服务连接
   */
  fun disconnect() {
    // 先清理所有状态回调
    if (modelService != null) {
      try {
        executeRemoteCall("unregisterAllStatusCallbacks") {
          modelService?.unregisterAllStatusCallbacks()
        }
        statusCallbacks.clear()
        AILog.d(TAG, "All status callbacks unregistered")
      } catch (e: Exception) {
        AILog.e(TAG, "Error unregistering status callbacks", e)
      }
    }

    if (_connectionState.value) {
      try {
        context.unbindService(serviceConnection)
        AILog.d(TAG, "Service unbound successfully")
      } catch (e: Exception) {
        AILog.e(TAG, "Error unbinding service", e)
      }
    }

    modelService = null
    _connectionState.value = false
    bindingFlag.set(false)

    // 取消超时任务
    bindingTimeoutJob?.cancel()
    bindingTimeoutJob = null
  }

  // ============ 高级业务接口 ============

  /**
   * 异步提取文本摘要
   *
   * @param inputText 输入文本
   * @return 任务执行结果
   */
  suspend fun extractAbstractAsync(inputText: String): TaskResult<String> {
    return executeAbstractTaskWorkflow(inputText)
  }

  /**
   * 异步检测待办事项
   *
   * @param inputText 输入文本
   * @return 任务执行结果
   */
  suspend fun detectActionItemAsync(inputText: String): TaskResult<String> {
    return executeActionItemTaskWorkflow(inputText)
  }

  /**
   * 异步检测待办事项
   *
   * @param inputText 输入文本
   * @return 任务执行结果
   */
  suspend fun extractQaAsync(inputText: String): TaskResult<String> {
    return executeExtractQaFlow(inputText)
  }

  /**
   * 异步检测说话人总结
   *
   * @param inputText 输入文本
   * @return 任务执行结果
   */
  suspend fun extractRoleSummarizeFlow(inputText: String): TaskResult<String> {
    return executeRoleSummarizeFlow(inputText)
  }


  /**
   * 异步检测待办事项
   *
   * @param inputText 输入文本
   * @return 任务执行结果
   */
  suspend fun extractSceneRecognitionAsync(inputText: String): TaskResult<String> {
    return executeSceneRecognitionFlow(inputText)
  }




  // ============ 任务提交功能 (Submission) ============

  /**
   * 提交文本摘要任务
   *
   * @param inputText 输入文本
   * @return 任务ID，如果提交失败返回 null
   */
  suspend fun submitAbstractTask(inputText: String): String? {
    return submitTaskInternal(TaskType.EXTRACT_ABSTRACT, inputText)
  }

  /**
   * 提交待办事项检测任务
   *
   * @param inputText 输入文本
   * @return 任务ID，如果提交失败返回 null
   */
  suspend fun submitActionItemTask(inputText: String): String? {
    return submitTaskInternal(TaskType.DETECT_ACTION_ITEM, inputText)
  }

  /**
   * 提交提取qa的任务
   *
   * @param inputText 输入文本
   * @return 任务ID，如果提交失败返回 null
   */
  suspend fun submitExtractQATask(inputText: String): String? {
    return submitTaskInternal(TaskType.EXTRACT_QA, inputText)
  }

  /**
   * 提交说话人总结的任务
   *
   * @param inputText 输入文本
   * @return 任务ID，如果提交失败返回 null
   */
  suspend fun submitSummarizeByRole(inputText: String): String? {
    return submitTaskInternal(TaskType.SUMMARIZE_BY_ROLE, inputText)
  }

  /**
   * 提交会议类型识别的任务
   *
   * @param inputText 输入文本
   * @return 任务ID，如果提交失败返回 null
   */
  suspend fun submitRecognizeScene(inputText: String): String? {
    return submitTaskInternal(TaskType.RECOGNIZE_SCENE, inputText)
  }

  /**
   * 内部任务提交实现
   *
   * @param taskType 任务的枚举类型
   * @param inputText 输入文本
   * @return 任务ID，如果提交失败返回 null
   */
  private suspend fun submitTaskInternal(
    taskType: TaskTypeByte,
    inputText: String,
  ): String? {
    val service = requireServiceConnected()

    val request = TaskRequest()
    request.taskType = taskType

    val dataSize = inputText.toByteArray(Charsets.UTF_8).size
    val useStringTransmission = dataSize < STRING_TRANSMISSION_THRESHOLD
    AILog.i(TAG, "${taskType.toApiKey()}: dataSize=$dataSize, useStringTransmission=$useStringTransmission")

    val modelInput = ModelInput()
    if (useStringTransmission) {
      modelInput.inputText = inputText
    } else {
      val pfd = createParcelFileDescriptor(inputText)
      if (pfd == null) {
        AILog.e(TAG, "Failed to create ParcelFileDescriptor for ${taskType.toApiKey()}")
        return null
      }
      modelInput.inputPfd = pfd
      modelInput.dataSize = dataSize
    }
    request.input = modelInput

    val taskId = submitTask(service, request)
    return taskId.ifEmpty { null }
  }

  // ============ 流程编排功能 (Orchestration) ============

  /**
   * 执行完整的文本摘要任务流程
   *
   * @param inputText 输入文本
   * @return 任务执行结果
   */
  suspend fun executeAbstractTaskWorkflow(inputText: String): TaskResult<String> {
    return executeTaskWorkflowInternal(TaskType.EXTRACT_ABSTRACT, inputText)
  }

  /**
   * 执行完整的待办事项检测任务流程
   *
   * @param inputText 输入文本
   * @return 任务执行结果
   */
  suspend fun executeActionItemTaskWorkflow(inputText: String): TaskResult<String> {
    return executeTaskWorkflowInternal(TaskType.DETECT_ACTION_ITEM, inputText)
  }

  /**
   * 执行完整的场景检测的任务流程
   *
   * @param inputText 输入文本
   * @return 任务执行结束返回
   */
  suspend fun executeSceneRecognitionFlow(inputText: String): TaskResult<String> {
    return executeTaskWorkflowInternal(TaskType.RECOGNIZE_SCENE, inputText)
  }

  /**
   * 执行完整的QA任务流程
   *
   * @param inputText 输入文本
   * @return 任务执行结束返回
   */
  suspend fun executeExtractQaFlow(inputText: String): TaskResult<String> {
    return executeTaskWorkflowInternal(TaskType.EXTRACT_QA, inputText)
  }

  /**
   * 执行完整的QA任务流程
   *
   * @param inputText 输入文本
   * @return 任务执行结束返回
   */
  suspend fun executeRoleSummarizeFlow(inputText: String): TaskResult<String> {
    return executeTaskWorkflowInternal(TaskType.SUMMARIZE_BY_ROLE, inputText)
  }


  /**
   * 执行合并任务的流程
   * 新的接口合并任务包含，摘要，待办，会议类型检
   *
   * @param inputText 输入文本
   * @return 任务执行结束返回
   */
  suspend fun executeMergeTaskFlow(inputText: String): TaskResult<String> {
    return executeTaskWorkflowInternal(TaskType.MERGE_TASK, inputText)
  }


  /**
   * 内部流程编排实现 - 完整的异步任务执行流程
   */
  private suspend fun executeTaskWorkflowInternal(
    taskType: TaskTypeByte,
    inputText: String,
  ): TaskResult<String> {
    if (!isHardwareSupported) {
      AILog.w(TAG, "APUSys hardware not supported, ${taskType.toApiKey()} may not work properly")
      return TaskResult.Error(-2, "APUSys hardware not supported")
    }

    // 提交任务获取 taskId
    val taskId = submitTaskInternal(taskType, inputText)
      ?: return TaskResult.Error(-1, "Failed to submit ${taskType.toApiKey()} task")

    AILog.d(TAG, "${taskType.toApiKey()} task submitted with ID: $taskId")

    // 轮询任务状态直到完成
    return waitForTaskCompletion(taskId)
  }

  /**
   * 具体的任务提交实现
   *
   * @return 任务ID，如果提交失败返回空字符串
   */
  private suspend fun submitTask(
    service: IModelService,
    request: TaskRequest,
  ): String {
    return suspendCancellableCoroutine { continuation ->
      val submitCallback = object : ITaskSubmitCallback.Stub() {
        override fun onSubmitted(taskId: String?) {
          AILog.d(TAG, "${request.taskType.toApiKey()} task submitted: $taskId")
          if (continuation.isActive) {
            continuation.resume(taskId ?: "")
          }
        }

        override fun onError(errorCode: Int, message: String?) {
          AILog.e(TAG, "${request.taskType.toApiKey()} task submit failed: errorCode=$errorCode, message=$message")
          if (continuation.isActive) {
            continuation.resume("")
          }
        }
      }

      try {
        executeRemoteCall("submitTask") {
          service.submitTask(request, submitCallback)
          AILog.d(TAG, "${request.taskType.toApiKey()} request sent via generic interface")
        }
      } catch (e: Exception) {
        AILog.e(TAG, "Error submitting ${request.taskType.toApiKey()} task", e)
        if (continuation.isActive) {
          continuation.resume("")
        }
      }
    }
  }

  // ============ 状态查询功能 (Status Polling) ============

  /**
   * 查询任务状态
   *
   * @param taskId 任务ID
   * @return 任务状态，如果任务不存在返回 null
   */
  suspend fun getTaskState(taskId: String): TaskState? {
    return try {
      val service = requireServiceConnected()
      executeRemoteCall("getTaskState") {
        service.getTaskState(taskId)
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Error getting task state for $taskId", e)
      null
    }
  }

  // ============ 任务控制功能 (Control) ============

  /**
   * 取消任务
   *
   * 提供取消任务的能力
   *
   * @param taskId 任务ID
   * @return 是否成功取消
   */
  suspend fun cancelTask(taskId: String): Boolean {
    return try {
      val service = requireServiceConnected()
      executeRemoteCall("cancelTask") {
        service.cancelTask(taskId)
      }
    } catch (e: Exception) {
      AILog.e(TAG, "Error cancelling task $taskId", e)
      false
    }
  }

  // ============ 流程编排辅助功能 (Orchestration Helpers) ============

  /**
   * 等待任务完成
   *
   * @param taskId 任务ID
   * @param timeoutMs 超时时间，默认300秒
   * @param pollIntervalMs 轮询间隔，默认500毫秒
   * @return 任务执行结果
   */
  private suspend fun waitForTaskCompletion(
    taskId: String,
    timeoutMs: Long = 300_000,
    pollIntervalMs: Long = 500
  ): TaskResult<String> {
    val startTime = System.currentTimeMillis()

    AILog.d(TAG, "Waiting for task $taskId completion (timeout: ${timeoutMs}ms)")

    while (System.currentTimeMillis() - startTime < timeoutMs) {
      val taskState = getTaskState(taskId)

      if (taskState == null) {
        AILog.w(TAG, "Task $taskId not found or expired")
        return TaskResult.Error(-1, "Task not found or expired")
      }

      AILog.v(TAG, "Task $taskId status: ${taskState.getReadableStatus()}")

      when {
        taskState.isCompleted() -> {
          AILog.d(TAG, "Task $taskId completed successfully")
          return TaskResult.Success(taskState.result ?: "")
        }
        taskState.isFailed() -> {
          AILog.e(TAG, "Task $taskId failed: ${taskState.errorMessage}")
          return TaskResult.Error(-1, taskState.errorMessage ?: "Task failed")
        }
        taskState.isCancelled() -> {
          AILog.w(TAG, "Task $taskId was cancelled")
          return TaskResult.Error(-1, "Task was cancelled")
        }
        taskState.isExecuting() -> {
          // 任务正在执行中，继续等待
          delay(pollIntervalMs)
        }
        taskState.isQueued() -> {
          // 任务在队列中等待，继续等待
          delay(pollIntervalMs)
        }
        else -> {
          // 其他状态，继续等待
          delay(pollIntervalMs)
        }
      }
    }

    // 超时处理
    AILog.e(TAG, "Task $taskId timed out after ${timeoutMs}ms")
    return TaskResult.Error(-1, "Task execution timed out")
  }

  /**
   * 创建 ParcelFileDescriptor 从文本
   */
  private fun createParcelFileDescriptor(text: String): ParcelFileDescriptor? {
    return try {
      val data = text.toByteArray(Charsets.UTF_8)
      val memoryFile = MemoryFile("input_text", data.size)
      memoryFile.writeBytes(data, 0, 0, data.size)

      // 使用反射获取 FileDescriptor
      val getFileDescriptorMethod: Method =
        MemoryFile::class.java.getDeclaredMethod("getFileDescriptor")
      getFileDescriptorMethod.isAccessible = true
      val fd = getFileDescriptorMethod.invoke(memoryFile) as FileDescriptor

      ParcelFileDescriptor.dup(fd)
    } catch (e: Exception) {
      AILog.e(TAG, "Error creating ParcelFileDescriptor", e)
      null
    }
  }

  /**
   * 清理资源
   * 断开服务连接并清理所有资源
   */
  fun cleanup() {
    disconnect()
  }
}

