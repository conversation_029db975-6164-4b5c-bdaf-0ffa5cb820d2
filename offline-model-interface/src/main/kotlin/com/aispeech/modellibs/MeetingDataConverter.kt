import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import java.util.UUID

@Serializable
data class OutputParagraph(
  val timestamp: String,
  val speaker: String,
  val content: String
)

@Serializable
data class OutputData(
  @SerialName("product_id") val productId: String,
  @SerialName("api_key") val apiKey: String,
  @SerialName("dialog_id") val dialogId: String,
  val paragraph: List<OutputParagraph>,
  @SerialName("speaker_info") val speakerInfo: Map<String, String>,
  @SerialName("meeting_type") val meetingType: String
)

private val json = Json {
  ignoreUnknownKeys = true
  coerceInputValues = true
}

fun createMeetingJson(
  paragraphs: List<Triple<String, String, String>>,
  speakerInfo: Map<String, String>,
  meetingType: String,
  productId: String = "279619920",
  apiKey: String = "0298eed42fec4ec79921d0584d2bae3c",
  dialogId: String = UUID.randomUUID().toString()
): String {
  val outputData = OutputData(
    productId = productId,
    apiKey = apiKey,
    dialogId = dialogId,
    paragraph = paragraphs.map { (timestamp, speakerId, content) ->
      OutputParagraph(timestamp, speakerId, content)
    },
    speakerInfo = speakerInfo,
    meetingType = meetingType
  )

  return json.encodeToString(outputData)
}
