package com.aispeech.modellibs

import com.aispeech.aibase.AILog
import java.io.File

object CapabilityUtils {
  private const val TAG = "CapabilityUtils"
  private const val APU_DEVICE_PATH = "/dev/apusys"

  /**
   * 检查设备是否支持 APU。
   * @return true 如果支持，否则 false。
   */
  @JvmStatic // 方便Java调用
  fun isApuSysSupported(): Boolean {
    return try {
      val deviceFile = File(APU_DEVICE_PATH)
      val supported = deviceFile.exists() && deviceFile.canRead()
      AILog.i(TAG, "APU support check from CapabilityUtils: $supported")
      supported
    } catch (e: Exception) {
      AILog.e(TAG, "APU support check failed in CapabilityUtils", e)
      false
    }
  }
}