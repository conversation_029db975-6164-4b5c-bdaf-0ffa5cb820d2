package com.aispeech.modellibs

import com.aispeech.aibase.AILog
import java.io.File

object CapabilityUtils {
  private const val TAG = "CapabilityUtils"
  private const val APU_DEVICE_PATH = "/dev/apusys"

  /**
   * 缓存的 APU 支持检查结果
   * 使用 lazy 确保文件系统检查只执行一次，避免重复的 I/O 操作
   */
  private val isApuSysSupportedCached: Boolean by lazy {
    try {
      val deviceFile = File(APU_DEVICE_PATH)
      val supported = deviceFile.exists() && deviceFile.canRead()
      AILog.i(TAG, "APU support check from CapabilityUtils (cached): $supported")
      supported
    } catch (e: Exception) {
      AILog.e(TAG, "APU support check failed in CapabilityUtils", e)
      false
    }
  }

  /**
   * 检查设备是否支持 APU。
   * 结果会被缓存，避免重复的文件系统检查。
   * @return true 如果支持，否则 false。
   */
  @JvmStatic // 方便Java调用
  fun isApuSysSupported(): Boolean {
    return isApuSysSupportedCached
  }

  /**
   * 强制重新检查 APU 支持（仅用于测试或特殊场景）
   * 注意：这会绕过缓存机制，直接进行文件系统检查
   * @return true 如果支持，否则 false。
   */
  @JvmStatic
  fun forceCheckApuSysSupported(): Boolean {
    return try {
      val deviceFile = File(APU_DEVICE_PATH)
      val supported = deviceFile.exists() && deviceFile.canRead()
      AILog.i(TAG, "APU support force check from CapabilityUtils: $supported")
      supported
    } catch (e: Exception) {
      AILog.e(TAG, "APU support force check failed in CapabilityUtils", e)
      false
    }
  }
}