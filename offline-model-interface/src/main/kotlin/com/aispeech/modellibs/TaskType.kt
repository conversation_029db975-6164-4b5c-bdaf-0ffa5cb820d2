package com.aispeech.modellibs

typealias TaskTypeByte = Byte

/**
 * 将任务类型(Byte)转换为数据库存储值(Int)
 */
fun TaskTypeByte.toDatabaseValue(): Int {
  return this.toInt() + 1
}

/**
 * 将任务类型(Byte)转换为对应的 API Key (String)
 */
fun TaskTypeByte.toApiKey(): String {
  return when (this) {
    TaskType.EXTRACT_ABSTRACT -> "extractAbstract"
    TaskType.DETECT_ACTION_ITEM -> "dectionActionItem"
    TaskType.EXTRACT_QA -> "extractQA"
    TaskType.SUMMARIZE_BY_ROLE -> "summarizeByRole"
    TaskType.RECOGNIZE_SCENE -> "recognizeScene"
    else -> throw IllegalArgumentException("Unknown TaskType value: $this")
  }
}

// =================================================================
// == 反向查找：从 具体值 -> TaskTypeByte
// =================================================================

private val allTaskTypes = listOf(
  TaskType.EXTRACT_ABSTRACT,
  TaskType.DETECT_ACTION_ITEM,
  TaskType.EXTRACT_QA,
  TaskType.SUMMARIZE_BY_ROLE,
  TaskType.RECOGNIZE_SCENE
)


private val databaseValueToTaskTypeMap by lazy {
  allTaskTypes.associateBy { it.toDatabaseValue() }
}

private val apiKeyToTaskTypeMap by lazy {
  allTaskTypes.associateBy { it.toApiKey() }
}

private val validTaskTypeInts by lazy {
  allTaskTypes.map { it.toInt() }.toSet()
}

/**
 * 从数据库存储值(Int)转换回任务类型(Byte)。
 *
 * @param value 从数据库读取的整数。
 * @return 对应的 TaskTypeByte，如果未找到则返回 null。
 */
fun fromDatabaseValueToTaskTypeByte(value: Int): TaskTypeByte? {
  return databaseValueToTaskTypeMap[value]
}

/**
 * 从 API Key (String) 转换回任务类型(Byte)。
 *
 * @param apiKey 字符串标识。
 * @return 对应的 TaskTypeByte，如果未找到则返回 null。
 */
fun fromApiKeyToTaskTypeByte(apiKey: String): TaskTypeByte? {
  return apiKeyToTaskTypeMap[apiKey]
}