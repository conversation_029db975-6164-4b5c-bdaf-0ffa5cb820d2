package com.aispeech.modellibs;

/**
 * 任务提交回调接口
 *
 * 用于接收任务提交的结果，包括成功提交时的任务ID或提交失败时的错误信息。
 *
 */
oneway interface ITaskSubmitCallback {

    /**
     * 任务提交成功回调
     * 当任务成功提交到队列时调用，返回生成的任务ID
     *
     * @param taskId 生成的任务ID，客户端可以使用此ID查询任务状态或取消任务
     */
    void onSubmitted(String taskId);

    /**
     * 任务提交失败回调
     * 当任务提交失败时调用，返回错误代码和错误信息
     *
     * @param errorCode 错误代码，参考 ErrorCodes 中的定义
     * @param message 错误描述信息
     */
    void onError(int errorCode, String message);
}
