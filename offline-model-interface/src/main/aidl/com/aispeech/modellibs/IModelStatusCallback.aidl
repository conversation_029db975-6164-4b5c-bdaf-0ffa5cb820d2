package com.aispeech.modellibs;

import com.aispeech.modellibs.ModelServiceStatus;

/**
 * 模型服务状态变化回调接口
 *
 * 用于接收服务状态的实时更新，支持流式状态监听。
 * 使用 oneway 关键字确保调用是非阻塞的，不会挂起服务端线程。
 */
oneway interface IModelStatusCallback {

    /**
     * 服务状态变化回调
     * 当服务状态发生变化时，服务端会主动调用此方法通知客户端
     *
     * @param status 新的服务状态对象，包含完整的状态信息
     */
    void onStatusChanged(in ModelServiceStatus status);

    /**
     * 注册成功回调
     * 当客户端成功注册状态监听器时调用，确认注册状态
     *
     * @param initialStatus 注册时的初始服务状态
     */
    void onRegistrationSuccess(in ModelServiceStatus initialStatus);

    /**
     * 注册失败回调
     * 当客户端注册状态监听器失败时调用
     *
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     */
    void onRegistrationFailed(int errorCode, String errorMessage);

    /**
     * 服务连接断开回调
     * 当服务端检测到客户端连接断开或异常时调用
     *
     * @param reason 断开原因
     */
    void onConnectionLost(String reason);

    /**
     * 服务错误回调
     * 当服务运行过程中发生错误时调用
     *
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     * @param currentStatus 发生错误时的服务状态
     */
    void onServiceError(int errorCode, String errorMessage, in ModelServiceStatus currentStatus);
}
