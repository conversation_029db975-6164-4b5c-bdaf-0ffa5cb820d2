package com.aispeech.modellibs;

import android.os.ParcelFileDescriptor;
import com.aispeech.modellibs.IModelStatusCallback;
import com.aispeech.modellibs.ITaskSubmitCallback;
import com.aispeech.modellibs.ModelServiceStatus;
import com.aispeech.modellibs.TaskState;
import com.aispeech.modellibs.TaskRequest;

/**
 * 离线模型服务的 AIDL 接口。
 */
interface IModelService {
    /**
     * 异步请求提取文本摘要（使用 ParcelFileDescriptor 传输大数据）
     * @param inputPfd 一个指向共享内存区域的 ParcelFileDescriptor，其中包含 UTF-8 编码的输入文本。
     *                 服务在读取后会负责关闭此 PFD。
     * @param dataSize 写入共享内存的文本数据的确切字节大小。
     * @param submitCallback 任务提交回调，返回 taskId 或错误信息
     */
    void extractAbstract(in ParcelFileDescriptor inputPfd, int dataSize, ITaskSubmitCallback submitCallback);

    /**
     * 异步请求提取文本摘要（使用普通字符串传输小数据）
     * @param inputText 输入文本内容
     * @param submitCallback 任务提交回调，返回 taskId 或错误信息
     */
    void extractAbstractWithText(String inputText, ITaskSubmitCallback submitCallback);

    /**
     * 异步请求检测待办事项（使用 ParcelFileDescriptor 传输大数据）
     * @param inputPfd 一个指向共享内存区域的 ParcelFileDescriptor，其中包含 UTF-8 编码的输入文本。
     *                 服务在读取后会负责关闭此 PFD。
     * @param dataSize 写入共享内存的文本数据的确切字节大小。
     * @param submitCallback 任务提交回调，返回 taskId 或错误信息
     */
    void dectionActionItem(in ParcelFileDescriptor inputPfd, int dataSize, ITaskSubmitCallback submitCallback);

    /**
     * 异步请求检测待办事项（使用普通字符串传输小数据）
     * @param inputText 输入文本内容
     * @param submitCallback 任务提交回调，返回 taskId 或错误信息
     */
    void dectionActionItemWithText(String inputText, ITaskSubmitCallback submitCallback);


    /**
    * 提交一个通用的、结构化的模型处理任务。
    * @param request 包含 apiKey 和具体参数的请求对象
    * @param submitCallback 任务提交回调
    */
    void submitTask(in TaskRequest request, ITaskSubmitCallback submitCallback);

    /**
     * 检测当前设备是否支持 APUSys 硬件
     * @return true 如果设备支持 APUSys 硬件，false 否则
     */
    boolean isApuSysSupported();

    /**
     * 直接获取当前服务状态（同步方法）
     * @return 当前服务状态对象，包含状态码、消息、时间戳等信息
     */
    ModelServiceStatus getCurrentStatus();

    /**
     * 注册服务状态监听器（异步流式方法）
     * 客户端注册后，服务端会在状态变化时主动推送状态更新
     * @param callback 状态变化回调接口
     * @return 注册是否成功
     */
    boolean registerStatusCallback(IModelStatusCallback callback);

    /**
     * 取消注册服务状态监听器
     * @param callback 要取消的回调接口
     * @return 取消注册是否成功
     */
    boolean unregisterStatusCallback(IModelStatusCallback callback);

    /**
     * 取消所有状态监听器
     * 通常在服务关闭时调用
     */
    void unregisterAllStatusCallbacks();

    /**
     * 查询指定任务的状态
     * @param taskId 任务ID
     * @return 任务状态对象，如果任务不存在返回 null
     */
    TaskState getTaskState(String taskId);

    /**
     * 取消指定任务
     * @param taskId 任务ID
     * @return 取消是否成功
     */
    boolean cancelTask(String taskId);


    /**
     * 更新 Magnus Runtime 路径
     * @param newPath 新的路径
     * @return 更新是否成功
     */
    boolean updateMagnusRuntimePath(String newPath);
}