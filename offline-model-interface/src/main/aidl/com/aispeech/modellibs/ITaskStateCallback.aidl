package com.aispeech.modellibs;

import com.aispeech.modellibs.TaskState;

/**
 * 任务状态查询回调接口
 *
 * 用于接收异步任务状态查询的结果，包括成功时的任务状态或查询失败时的错误信息。
 * 使用 oneway 关键字确保调用是非阻塞的，不会挂起服务端线程。
 */
oneway interface ITaskStateCallback {

    /**
     * 任务状态查询成功回调
     * 当成功获取到任务状态时调用
     *
     * @param taskState 任务状态对象，包含完整的任务信息
     */
    void onTaskStateRetrieved(in TaskState taskState);

    /**
     * 任务不存在回调
     * 当指定的任务ID不存在时调用
     *
     * @param taskId 查询的任务ID
     */
    void onTaskNotFound(String taskId);

    /**
     * 任务状态查询失败回调
     * 当查询过程中发生错误时调用
     *
     * @param taskId 查询的任务ID
     * @param errorCode 错误代码
     * @param errorMessage 错误描述信息
     */
    void onQueryError(String taskId, int errorCode, String errorMessage);
}
