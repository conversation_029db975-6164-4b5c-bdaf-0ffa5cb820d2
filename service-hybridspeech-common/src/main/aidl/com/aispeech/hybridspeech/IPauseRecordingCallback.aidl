package com.aispeech.hybridspeech;

import com.aispeech.hybridspeech.PauseRecordingResult;

// 异步暂停录音回调接口
interface IPauseRecordingCallback {
    /**
     * 暂停录音成功回调
     * @param result 暂停结果信息，包含暂停的组件类型和详细信息
     */
    void onPauseRecordingSuccess(in PauseRecordingResult result);

    /**
     * 暂停录音失败回调
     * @param errorCode 错误代码
     * @param errorMessage 错误信息
     */
    void onPauseRecordingError(int errorCode, String errorMessage);
}
