package com.aispeech.hybridspeech

import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.ApplicationInfo
import android.content.pm.PackageManager
import android.os.IBinder
import android.os.RemoteException
import com.aispeech.aibase.AILog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withTimeout
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.resume
import kotlin.coroutines.resumeWithException

/**
 * 混合语音识别服务客户端
 */
class HybridSpeechClient(
  private val context: Context,
  private val servicePackage: String = DEFAULT_SERVICE_PACKAGE,
  private val serviceClass: String = DEFAULT_SERVICE_CLASS
) {

  companion object {
    private const val TAG = "HybridSpeechClient"
    private const val DEFAULT_SERVICE_PACKAGE = "com.aispeech.tablet.service.hybridspeech"
    private const val DEFAULT_SERVICE_CLASS = "com.aispeech.tablet.service.hybridspeech.HybridSpeechService"

    private const val UNFREEZE_ACTIVITY_NAME = ".UnfreezeActivity"
    private const val MAX_RETRY_COUNT = 3
    private const val BINDING_TIMEOUT = 5000L
    private const val DEFAULT_PROGRESS_INTERVAL = 1000
  }

  /**
   * 录音进度状态
   */
  sealed class RecordingProgressState {
    data object Started : RecordingProgressState()
    data class Progress(val durationMs: Long) : RecordingProgressState()
    data class Stopped(val totalDurationMs: Long) : RecordingProgressState()
    data class Error(val message: String) : RecordingProgressState()
  }

  /**
   * 任务结果封装类
   */
  sealed class TaskResult<T> {
    data class Success<T>(val data: T) : TaskResult<T>()
    data class Error<T>(val errorCode: Int, val errorMessage: String) : TaskResult<T>()
  }

  // 服务相关
  private var hybridSpeechService: IHybridSpeechService? = null
  private var transcriptionCallback: ITranscriptionCallback? = null
  private var configProvider: IHybridSpeechConfigProvider? = null

  // 连接状态
  private val _connectionState = MutableStateFlow(false)
  val connectionState: StateFlow<Boolean> = _connectionState.asStateFlow()

  // 协程作用域
  private val clientScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

  // 重试相关变量
  private val bindingFlag = AtomicBoolean(false)
  private var bindingRetryCount = MAX_RETRY_COUNT
  private var bindingTimeoutJob: Job? = null

  /**
   * 检查服务连接状态
   */
  private fun requireServiceConnected(): IHybridSpeechService {
    return hybridSpeechService ?: throw IllegalStateException("Service not connected")
  }

  /**
   * 统一远程调用异常处理
   */
  private fun <T> executeRemoteCall(operation: String, action: () -> T): T {
    return try {
      action()
    } catch (e: RemoteException) {
      AILog.e(TAG, "Remote call failed for $operation", e)
      throw e
    }
  }

  /**
   * 服务连接回调
   */
  private val serviceConnection = object : ServiceConnection {
    override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
      AILog.d(TAG, "Service connected: $name")

      // 取消超时任务
      bindingTimeoutJob?.cancel()
      bindingTimeoutJob = null

      bindingFlag.set(false)
      bindingRetryCount = MAX_RETRY_COUNT // 重置重试计数

      hybridSpeechService = IHybridSpeechService.Stub.asInterface(service)
      _connectionState.value = true

      // 重新注册回调
      reregisterCallbacks()
    }

    override fun onServiceDisconnected(name: ComponentName?) {
      AILog.w(TAG, "Service disconnected: $name")
      hybridSpeechService = null
      _connectionState.value = false
      bindingFlag.set(false)

      // 取消任何正在进行的超时任务
      bindingTimeoutJob?.cancel()
      bindingTimeoutJob = null
    }
  }

  /**
   * 重新注册回调
   */
  private fun reregisterCallbacks() {
    // 重新注册转写回调
    transcriptionCallback?.let { callback ->
      runCatching {
        hybridSpeechService?.registerCallback(callback)
        AILog.d(TAG, "Transcription callback re-registered")
      }.onFailure { e ->
        AILog.e(TAG, "Failed to re-register transcription callback", e)
      }
    }

    // 重新注册配置提供者
    configProvider?.let { provider ->
      runCatching {
        hybridSpeechService?.registerConfigProvider(provider)
        AILog.d(TAG, "Config provider re-registered")
      }.onFailure { e ->
        AILog.e(TAG, "Failed to re-register config provider", e)
      }
    }
  }

  /**
   * 检查应用是否处于 stopped 状态
   */
  private fun isPackageStopped(packageName: String): Boolean {
    return try {
      val packageManager = context.packageManager
      val applicationInfo = packageManager.getApplicationInfo(packageName, PackageManager.GET_META_DATA)

      // 检查 ApplicationInfo.FLAG_STOPPED 标志
      val isStopped = (applicationInfo.flags and ApplicationInfo.FLAG_STOPPED) != 0
      AILog.d(TAG, "Package $packageName stopped state: $isStopped")
      isStopped
    } catch (e: PackageManager.NameNotFoundException) {
      AILog.w(TAG, "Package $packageName not found")
      false
    } catch (e: Exception) {
      AILog.e(TAG, "Error checking package stopped state", e)
      false
    }
  }

  /**
   * 尝试激活应用包
   */
  private suspend fun ensurePackageActive(packageName: String): Boolean {
    if (!isPackageStopped(packageName)) {
      AILog.d(TAG, "Package $packageName is not stopped, no activation needed")
      return true
    }

    return try {
      AILog.d(TAG, "Attempting to activate package: $packageName")
      val unfreezeIntent = Intent().apply {
        val fullActivityName = "$packageName$UNFREEZE_ACTIVITY_NAME"
        component = ComponentName(packageName, fullActivityName)
        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
      }

      context.startActivity(unfreezeIntent)
      AILog.d(TAG, "Sent intent to start UnfreezeActivity for package: $packageName")

      // 短暂等待后重新检查
      repeat(3) { attempt ->
        delay(200)
        if (!isPackageStopped(packageName)) {
          AILog.d(TAG, "Package $packageName activated after ${(attempt + 1) * 200}ms")
          return true
        }
      }

      val stillStopped = isPackageStopped(packageName)
      AILog.d(TAG, "Package $packageName activation result: ${!stillStopped}")
      !stillStopped
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to activate package $packageName", e)
      false
    }
  }

  /**
   * 尝试绑定服务（带重试机制）
   */
  private suspend fun tryBindService(): Boolean {
    if (bindingFlag.getAndSet(true)) {
      AILog.d(TAG, "Service binding already in progress")
      return false
    }

    if (bindingRetryCount <= 0) {
      AILog.e(TAG, "Service binding retry count exhausted")
      bindingFlag.set(false)
      return false
    }

    bindingRetryCount--
    AILog.d(TAG, "Attempting to bind service, retries left: $bindingRetryCount")

    return try {
      // 确保目标应用处于活跃状态
      if (!ensurePackageActive(servicePackage)) {
        AILog.w(TAG, "Failed to activate target package: $servicePackage")
      }

      val intent = Intent().apply {
        component = ComponentName(servicePackage, serviceClass)
      }

      val bindResult = context.bindService(
        intent,
        serviceConnection,
        Context.BIND_AUTO_CREATE or Context.BIND_IMPORTANT
      )

      if (bindResult) {
        AILog.d(TAG, "Service binding initiated successfully")

        // 设置超时任务
        bindingTimeoutJob = clientScope.launch {
          delay(BINDING_TIMEOUT)
          if (!_connectionState.value && bindingFlag.get()) {
            AILog.w(TAG, "Service binding timeout, attempting retry")
            context.unbindService(serviceConnection)
            bindingFlag.set(false)

            // 如果还有重试次数，继续尝试
            if (bindingRetryCount > 0) {
              delay(1000) // 等待1秒后重试
              tryBindService()
            } else {
              AILog.e(TAG, "Service binding failed after all retries")
            }
          }
        }
      } else {
        AILog.e(TAG, "Failed to initiate service binding")
        bindingFlag.set(false)
      }

      bindResult
    } catch (e: Exception) {
      AILog.e(TAG, "Exception during service binding", e)
      bindingFlag.set(false)
      false
    }
  }

  /**
   * 连接服务
   * @return 是否成功启动连接过程
   */
  suspend fun connect(): Boolean {
    if (_connectionState.value) {
      AILog.d(TAG, "Service already connected")
      return true
    }

    bindingRetryCount = MAX_RETRY_COUNT
    return tryBindService()
  }

  /**
   * 连接服务并等待连接完成
   * @param timeoutMs 超时时间，默认5秒
   * @return 是否连接成功
   */
  suspend fun connectAndWait(timeoutMs: Long = 5000): Boolean {
    if (_connectionState.value) {
      AILog.d(TAG, "Service already connected")
      return true
    }

    if (!connect()) {
      return false
    }

    return try {
      withTimeout(timeoutMs) {
        connectionState.first { it }
      }
    } catch (e: TimeoutCancellationException) {
      AILog.e(TAG, "Connection timeout after ${timeoutMs}ms")
      false
    }
  }

  /**
   * 断开服务连接
   */
  fun disconnect() {
    if (_connectionState.value) {
      try {
        context.unbindService(serviceConnection)
        AILog.d(TAG, "Service unbound successfully")
      } catch (e: Exception) {
        AILog.e(TAG, "Error unbinding service", e)
      }
    }

    hybridSpeechService = null
    transcriptionCallback = null
    configProvider = null
    _connectionState.value = false
    bindingFlag.set(false)

    // 取消超时任务
    bindingTimeoutJob?.cancel()
    bindingTimeoutJob = null

    // 取消所有协程
    clientScope.cancel()
  }

  /**
   * 异步开始录音
   */
  suspend fun startRecordingAsync(config: RecordingConfig) {
    val service = requireServiceConnected()

    return suspendCancellableCoroutine { continuation ->
      val callback = object : IStartRecordingCallback.Stub() {
        override fun onStartRecordingSuccess() {
          AILog.d(TAG, "Recording started successfully")
          if (continuation.isActive) {
            continuation.resume(Unit)
          }
        }

        override fun onStartRecordingError(errorCode: Int, errorMessage: String?) {
          AILog.e(TAG, "Recording start failed: errorCode=$errorCode, message=$errorMessage")
          if (continuation.isActive) {
            val error = RuntimeException("Recording failed: errorCode=$errorCode, message=$errorMessage")
            continuation.resumeWithException(error)
          }
        }
      }

      executeRemoteCall("startRecordingWithConfigAsync") {
        service.startRecordingWithConfigAsync(config, callback)
        AILog.d(TAG, "Start recording request sent")
      }
    }
  }

  /**
   * 异步停止录音并获取结果
   */
  suspend fun stopRecordingWithResultAsync(): RecordingResultInfo = suspendCancellableCoroutine { continuation ->
    val service = requireServiceConnected()

    val callback = object : IStopRecordingCallback.Stub() {
      override fun onStopRecordingSuccess(result: RecordingResultInfo?) {
        AILog.d(TAG, "Recording stopped successfully")
        if (continuation.isActive) {
          continuation.resume(result ?: RecordingResultInfo(
            pcmFilePath = "",
            mp3FilePath = "",
            durationMs = 0L,
            fileSizeBytes = 0L,
            audioConfig = AudioRecordingConfig()
          ))
        }
      }

      override fun onStopRecordingError(errorCode: Int, errorMessage: String?) {
        AILog.e(TAG, "Recording stop failed: errorCode=$errorCode, message=$errorMessage")
        if (continuation.isActive) {
          val error = RuntimeException("Stop recording failed: errorCode=$errorCode, message=$errorMessage")
          continuation.resumeWithException(error)
        }
      }
    }

    executeRemoteCall("stopRecordingWithResultAsync") {
      service.stopRecordingWithResultAsync(callback)
      AILog.d(TAG, "Stop recording request sent")
    }
  }

  /**
   * 异步暂停录音
   */
  suspend fun pauseRecordingAsync() {
    val service = requireServiceConnected()

    return suspendCancellableCoroutine { continuation ->
      val callback = object : IPauseRecordingCallback.Stub() {
        override fun onPauseRecordingSuccess(result: PauseRecordingResult) {
          val modeText = if (result.isOnlineMode) "在线" else "离线"
          val finalTextInfo = if (result.isOnlineMode) ", 最终文本: ${result.finalTextReceived}" else ""
          AILog.d(TAG, "Recording paused successfully - 模式: $modeText, 时长: ${result.recordingDurationMs}ms$finalTextInfo")
          if (continuation.isActive) {
            continuation.resume(Unit)
          }
        }

        override fun onPauseRecordingError(errorCode: Int, errorMessage: String?) {
          AILog.e(TAG, "Recording pause failed: errorCode=$errorCode, message=$errorMessage")
          if (continuation.isActive) {
            val error = RuntimeException("Pause recording failed: errorCode=$errorCode, message=$errorMessage")
            continuation.resumeWithException(error)
          }
        }
      }

      executeRemoteCall("pauseRecordingAsync") {
        service.pauseRecordingAsync(callback)
        AILog.d(TAG, "Pause recording request sent")
      }
    }
  }

  /**
   * 异步恢复录音
   */
  suspend fun resumeRecordingAsync() {
    val service = requireServiceConnected()

    return suspendCancellableCoroutine { continuation ->
      val callback = object : IResumeRecordingCallback.Stub() {
        override fun onResumeRecordingSuccess() {
          AILog.d(TAG, "Recording resumed successfully")
          if (continuation.isActive) {
            continuation.resume(Unit)
          }
        }

        override fun onResumeRecordingError(errorCode: Int, errorMessage: String?) {
          AILog.e(TAG, "Recording resume failed: errorCode=$errorCode, message=$errorMessage")
          if (continuation.isActive) {
            val error = RuntimeException("Resume recording failed: errorCode=$errorCode, message=$errorMessage")
            continuation.resumeWithException(error)
          }
        }
      }

      executeRemoteCall("resumeRecordingAsync") {
        service.resumeRecordingAsync(callback)
        AILog.d(TAG, "Resume recording request sent")
      }
    }
  }

  /**
   * 使用配置提供者开始录音
   * @param config 录音配置
   */
  suspend fun startRecordingWithProvider(config: RecordingConfig): Unit = suspendCancellableCoroutine { continuation ->
    val service = requireServiceConnected()

    val callback = object : IStartRecordingCallback.Stub() {
      override fun onStartRecordingSuccess() {
        AILog.d(TAG, "Recording with provider started successfully")
        if (continuation.isActive) {
          continuation.resume(Unit)
        }
      }

      override fun onStartRecordingError(errorCode: Int, errorMessage: String?) {
        AILog.e(TAG, "Recording with provider start failed: errorCode=$errorCode, message=$errorMessage")
        if (continuation.isActive) {
          val error = RuntimeException("Recording with provider failed: errorCode=$errorCode, message=$errorMessage")
          continuation.resumeWithException(error)
        }
      }
    }

    executeRemoteCall("startRecordingWithProvider") {
      service.startRecordingWithProvider(config, callback)
      AILog.d(TAG, "Start recording with provider request sent")
    }
  }

  /**
   * 注册转写结果回调
   */
  fun registerTranscriptionCallback(callback: ITranscriptionCallback) {
    transcriptionCallback = callback
    if (_connectionState.value) {
      runCatching {
        hybridSpeechService?.registerCallback(callback)
        AILog.d(TAG, "Transcription callback registered successfully")
      }.onFailure { e ->
        AILog.e(TAG, "Failed to register transcription callback", e)
      }
    }
  }

  /**
   * 取消注册转写结果回调
   */
  fun unregisterTranscriptionCallback() {
    if (_connectionState.value && transcriptionCallback != null) {
      runCatching {
        hybridSpeechService?.unregisterCallback(transcriptionCallback!!)
        AILog.d(TAG, "Transcription callback unregistered successfully")
      }.onFailure { e ->
        AILog.e(TAG, "Failed to unregister transcription callback", e)
      }
    }
    transcriptionCallback = null
  }

  /**
   * 注册配置提供者
   */
  fun registerConfigProvider(provider: IHybridSpeechConfigProvider) {
    configProvider = provider
    if (_connectionState.value) {
      runCatching {
        hybridSpeechService?.registerConfigProvider(provider)
        AILog.d(TAG, "Config provider registered successfully")
      }.onFailure { e ->
        AILog.e(TAG, "Failed to register config provider", e)
      }
    }
  }

  /**
   * 取消注册配置提供者
   */
  fun unregisterConfigProvider() {
    if (_connectionState.value) {
      runCatching {
        hybridSpeechService?.unregisterConfigProvider()
        AILog.d(TAG, "Config provider unregistered successfully")
      }.onFailure { e ->
        AILog.e(TAG, "Failed to unregister config provider", e)
      }
    }
    configProvider = null
  }

  /**
   * 创建录音进度监控流
   * @param intervalMs 进度回调间隔（毫秒）
   * @return 进度状态流
   */
  fun createProgressFlow(intervalMs: Int = DEFAULT_PROGRESS_INTERVAL): Flow<RecordingProgressState> =
    callbackFlow {
      val service = requireServiceConnected()

      val callback = object : IRecordProgressCallback.Stub() {
        override fun onRecordingStarted() {
          trySend(RecordingProgressState.Started)
        }

        override fun onRecordingProgress(durationMs: Long) {
          trySend(RecordingProgressState.Progress(durationMs))
        }

        override fun onRecordingStopped(totalDurationMs: Long) {
          trySend(RecordingProgressState.Stopped(totalDurationMs))
          close()
        }

        override fun onError(error: String?) {
          close(RuntimeException(error ?: "Unknown progress error"))
        }
      }

      executeRemoteCall("registerProgressCallback") {
        service.registerProgressCallback(callback, intervalMs)
        AILog.d(TAG, "Progress callback registered")
      }

      awaitClose {
        if (_connectionState.value) {
          runCatching {
            hybridSpeechService?.unregisterProgressCallback(callback)
            AILog.d(TAG, "Progress callback unregistered")
          }.onFailure {
            AILog.w(TAG, "Failed to unregister progress callback with failure: ${it.message}")
          }
        }
      }
    }

  /**
   * 获取当前录音时长
   */
  fun getCurrentDuration(): Long? {
    return if (_connectionState.value) {
      runCatching {
        hybridSpeechService?.getRecordingDuration()
      }.getOrElse { e ->
        AILog.e(TAG, "Error getting current duration", e)
        null
      }
    } else {
      null
    }
  }

  /**
   * 获取当前状态
   */
  fun getCurrentStatus(): Int? {
    return if (_connectionState.value) {
      runCatching {
        hybridSpeechService?.getCurrentStatus()
      }.getOrElse { e ->
        AILog.e(TAG, "Error getting current status", e)
        null
      }
    } else {
      null
    }
  }

  /**
   * 清理资源
   * 断开服务连接并清理所有资源
   */
  fun cleanup() {
    disconnect()
  }

  // ==================== 录音配置管理 ====================

  /**
   * 录音配置数据类
   *
   * 集中管理录音的配置参数，避免重复定义
   */
  private data class RecordingTaskConfig(
    val recordId: Long,
    val userId: String,
    val pcmFilePath: String,
    val mp3FilePath: String,
    val language: String,
    val audioType: String,
    val translate: String?,
    val useOnlineMode: Boolean,
    val enableTranslation: Boolean,
    val offlineModelPath: String?
  ) {
    fun createRecordingConfig(): RecordingConfig {
      return if (useOnlineMode) {
        RecordingConfig.createOnlineRecordingConfigWithResumeSupport(
          recordId = recordId,
          userId = userId,
          pcmFilePath = pcmFilePath,
          mp3FilePath = mp3FilePath,
          serverUrl = "", // 由配置提供者填充
          apiKey = "",    // 由配置提供者填充
          language = language,
          audioType = audioType,
          translate = translate
        )
      } else {
        // 离线模式：映射语言名称并创建离线配置
        val mappedLanguage = getOfflineLanguageDisplayName(language)
        val mappedTranslate = translate?.let { getOfflineLanguageDisplayName(it) }

        createOfflineConfig(
          pcmFilePath = pcmFilePath,
          mp3FilePath = mp3FilePath,
          language = mappedLanguage,
          translate = mappedTranslate,
          enableTranslation = enableTranslation,
          offlineModelPath = offlineModelPath
        )
      }
    }

    private fun getOfflineLanguageDisplayName(languageCode: String): String {
      return when (languageCode) {
        "cn" -> "Chinese"
        "en" -> "English"
        "ce" -> "Chinese-English"
        "ja" -> "Japanese"
        "ko" -> "Korean"
        "fr" -> "French"
        "de" -> "German"
        "es" -> "Spanish"
        "it" -> "Italian"
        "pt" -> "Portuguese"
        "ru" -> "Russian"
        else -> "Unknown"
      }
    }

    private fun createOfflineConfig(
      pcmFilePath: String,
      mp3FilePath: String,
      language: String,
      translate: String? = null,
      enableTranslation: Boolean,
      offlineModelPath: String?,
      enableSmooth: Boolean = true
    ): RecordingConfig {
      return RecordingConfig(
        useOnlineMode = false,
        language = language,
        translate = translate,
        pcmFilePath = pcmFilePath,
        mp3FilePath = mp3FilePath,
        audioConfig = AudioRecordingConfig.createDefault(),
        mp3Config = Mp3EncodingConfig.createDefault(),
        opusConfig = OpusEncodingConfig.createDefault(),
        progressConfig = RecordingProgressConfig.createDefault(),
        onlineAsrConfig = null,
        offlineEngineConfig = if (offlineModelPath != null) {
          OfflineEngineConfig(
            modelPath = offlineModelPath,
            enableSmooth = enableSmooth,
          )
        } else null,
        offlineTranslationConfig = if (enableTranslation) {
          OfflineTranslationConfig.createEnabled(
            taskName = "trans_dialect",
            fromLanguage = language,
            toLanguage = translate ?: "English",
            domain = "aicar",
            resourcePath = offlineModelPath ?: ""
          )
        } else null
      )
    }
  }

  // ==================== 高级业务接口 ====================

  /**
   * 异步开始录音 - 业务参数接口
   *
   * 提供纯粹的业务接口，自动处理连接管理和配置创建
   *
   * @param recordId 录音ID
   * @param userId 用户ID
   * @param pcmFilePath PCM文件路径
   * @param mp3FilePath MP3文件路径
   * @param language 语言，默认为"cn"
   * @param audioType 音频类型，默认为"ogg_opus"
   * @param translate 翻译目标语言，可空
   * @param useOnlineMode 是否使用在线模式，默认为true（在线模式支持ASR续传）
   * @param enableTranslation 是否启用翻译（离线模式）
   * @param offlineModelPath 离线模型路径（离线模式）
   * @return 任务执行结果
   */
  suspend fun startRecordingWithBusinessParams(
    recordId: Long,
    userId: String,
    pcmFilePath: String,
    mp3FilePath: String,
    language: String = "cn",
    audioType: String = "ogg_opus",
    translate: String? = null,
    useOnlineMode: Boolean = true,
    enableTranslation: Boolean = false,
    offlineModelPath: String? = null
  ): TaskResult<Unit> {
    val taskConfig = RecordingTaskConfig(
      recordId = recordId,
      userId = userId,
      pcmFilePath = pcmFilePath,
      mp3FilePath = mp3FilePath,
      language = language,
      audioType = audioType,
      translate = translate,
      useOnlineMode = useOnlineMode,
      enableTranslation = enableTranslation,
      offlineModelPath = offlineModelPath
    )

    return executeRecordingWorkflow(taskConfig)
  }

  // ==================== 流程编排功能 ====================

  /**
   * 执行完整的录音任务流程
   *
   * 参考 ModelServiceClient 的设计模式，提供完整的流程编排
   *
   * @param taskConfig 录音任务配置
   * @return 任务执行结果
   */
  private suspend fun executeRecordingWorkflow(taskConfig: RecordingTaskConfig): TaskResult<Unit> {
    return try {
      // 1. 确保服务已连接
      if (!ensureServiceConnected()) {
        return TaskResult.Error(-1, "Failed to connect to service")
      }

      // 2. 创建录音配置
      val config = taskConfig.createRecordingConfig()

      // 3. 开始录音
      if (configProvider != null) {
        startRecordingWithProvider(config)
      } else {
        startRecordingAsync(config)
      }

      TaskResult.Success(Unit)
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to execute recording workflow", e)
      TaskResult.Error(-1, e.message ?: "Unknown error")
    }
  }

  /**
   * 确保服务已连接
   *
   * @return 是否连接成功
   */
  private suspend fun ensureServiceConnected(): Boolean {
    if (_connectionState.value) {
      return true
    }

    AILog.d(TAG, "Service not connected, attempting to connect...")
    return connectAndWait()
  }

  /**
   * 停止录音并返回结果
   */
  suspend fun stopRecordingWithResult(): TaskResult<RecordingResultInfo> {
    return try {
      val result = stopRecordingWithResultAsync()
      TaskResult.Success(result)
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to stop recording", e)
      TaskResult.Error(-1, e.message ?: "Unknown error")
    }
  }

  /**
   * 停止录音（兼容 HybridSpeechServiceManager 接口）
   */
  suspend fun stopRecording(): RecordingResultInfo? {
    return try {
      stopRecordingWithResultAsync()
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to stop recording", e)
      null
    }
  }

  /**
   * 暂停录音
   */
  suspend fun pauseRecording(): TaskResult<Unit> {
    return try {
      pauseRecordingAsync()
      TaskResult.Success(Unit)
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to pause recording", e)
      TaskResult.Error(-1, e.message ?: "Unknown error")
    }
  }

  /**
   * 恢复录音
   */
  suspend fun resumeRecording(): TaskResult<Unit> {
    return try {
      resumeRecordingAsync()
      TaskResult.Success(Unit)
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to resume recording", e)
      TaskResult.Error(-1, e.message ?: "Unknown error")
    }
  }



  // ==================== 辅助方法 ====================

  /**
   * 根据语言代码获取离线模式下的语言显示名称
   */
  private fun getOfflineLanguageDisplayName(languageCode: String): String {
    return when (languageCode) {
      "cn" -> "Chinese"
      "en" -> "English"
      "ce" -> "Chinese-English"
      "ja" -> "Japanese"
      "ko" -> "Korean"
      "fr" -> "French"
      "de" -> "German"
      "es" -> "Spanish"
      "it" -> "Italian"
      "pt" -> "Portuguese"
      "ru" -> "Russian"
      else -> "Unknown"
    }
  }

  /**
   * 创建离线录音配置
   */
  private fun createOfflineConfig(
    pcmFilePath: String,
    mp3FilePath: String,
    language: String,
    translate: String? = null,
    enableTranslation: Boolean,
    offlineModelPath: String?,
    enableSmooth: Boolean = true,
  ): RecordingConfig {
    return RecordingConfig(
      useOnlineMode = false,
      language = language, // 直接使用已映射的语言
      translate = translate, // 直接使用已映射的翻译语言
      pcmFilePath = pcmFilePath,
      mp3FilePath = mp3FilePath,
      audioConfig = AudioRecordingConfig.createDefault(),
      mp3Config = Mp3EncodingConfig.createDefault(),
      opusConfig = OpusEncodingConfig.createDefault(),
      progressConfig = RecordingProgressConfig.createDefault(),
      onlineAsrConfig = null,
      offlineEngineConfig = if (offlineModelPath != null) {
        OfflineEngineConfig(
          modelPath = offlineModelPath,
          enableSmooth = enableSmooth
        )
      } else null,
      offlineTranslationConfig = if (enableTranslation) {
        OfflineTranslationConfig.createEnabled(
          taskName = "trans_dialect",
          fromLanguage = language,
          toLanguage = translate ?: "English",
          domain = "aicar",
          resourcePath = offlineModelPath ?: ""
        )
      } else null
    )
  }
}
