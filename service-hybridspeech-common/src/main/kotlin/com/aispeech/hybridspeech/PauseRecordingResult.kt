package com.aispeech.hybridspeech

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Serializable

/**
 * 暂停录音结果信息
 * 简化设计，只包含必要的信息
 */
@Parcelize
@Serializable
data class PauseRecordingResult(
    /**
     * 处理模式：true为在线模式，false为离线模式
     */
    val isOnlineMode: Boolean,

    /**
     * 当前录音时长（毫秒）
     */
    val recordingDurationMs: Long,

    /**
     * 在线模式特有：是否已收到最终文本结果
     * true表示服务器已完成当前音频片段的处理
     * 离线模式时为null
     */
    val finalTextReceived: Boolean? = null
) : Parcelable
