package com.aispeech.hybridspeech.core

import com.aispeech.aibase.AILog
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * 协程扩展函数
 * 提供便捷的协程操作和异常处理
 */

/**
 * 在IO父作用域中启动协程
 */
fun launchIO(
  tag: String = "LaunchIO",
  block: suspend CoroutineScope.() -> Unit
): Job {
  return CoroutineScopeManager.getIOParentScope().launch {
    try {
      block()
    } catch (e: CancellationException) {
      AILog.d(tag, "IO coroutine cancelled: ${e.message}")
    } catch (e: Exception) {
      AILog.e(tag, "Error in IO coroutine", e)
    }
  }
}

/**
 * 在主线程父作用域中启动协程
 */
fun launchMain(
  tag: String = "LaunchMain",
  block: suspend CoroutineScope.() -> Unit
): Job {
  return CoroutineScopeManager.getMainParentScope().launch {
    try {
      block()
    } catch (e: Exception) {
      AILog.e(tag, "Error in Main coroutine", e)
    }
  }
}

/**
 * 在模块作用域中启动协程 - 已移除
 * 请使用模块自己的CoroutineScope接口：module.launch { block() }
 */

/**
 * 创建带有异常处理的协程作用域
 * @deprecated 使用模块自己的CoroutineScope接口替代
 */
@Deprecated(
  message = "Use module's own CoroutineScope interface instead",
  replaceWith = ReplaceWith("CoroutineScopeManager.createModuleScopeDelegate(name)")
)
fun createSafeScope(
  name: String,
  dispatcher: CoroutineDispatcher = Dispatchers.IO
): CoroutineScope {
  return CoroutineScopeManager.createModuleScopeDelegate(name, dispatcher = dispatcher)
}

/**
 * 安全地取消Job
 */
fun Job?.cancelSafely(reason: String = "Job cancelled") {
  try {
    this?.cancel(reason)
  } catch (e: Exception) {
    AILog.w("JobCancel", "Error cancelling job: ${e.message}")
  }
}

/**
 * 安全地取消作用域
 */
fun CoroutineScope?.cancelSafely(reason: String = "Scope cancelled") {
  try {
    this?.cancel(reason)
  } catch (e: Exception) {
    AILog.w("ScopeCancel", "Error cancelling scope: ${e.message}")
  }
}

/**
 * 延迟执行，带有异常处理
 */
fun delayedLaunch(
  delayMs: Long,
  scope: CoroutineScope = CoroutineScopeManager.getIOParentScope(),
  tag: String = "DelayedLaunch",
  block: suspend CoroutineScope.() -> Unit
): Job {
  return scope.launch {
    try {
      delay(delayMs)
      block()
    } catch (e: CancellationException) {
      AILog.d(tag, "IO coroutine cancelled: ${e.message}")
    } catch (e: Exception) {
      AILog.e(tag, "Error in delayed launch", e)
    }
  }
}
