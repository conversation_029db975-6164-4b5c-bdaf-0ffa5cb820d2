package com.aispeech.hybridspeech.core

import com.aispeech.aibase.AILog
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.TimeoutCancellationException
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicInteger

/**
 * 统一异常处理器
 * 集成 CoroutineScopeManager 的异常处理机制，提供标准化的错误响应和日志记录
 */
object UnifiedExceptionHandler {

    private const val TAG = "UnifiedExceptionHandler"

    // 异常统计
    private val exceptionStats = ConcurrentHashMap<String, AtomicInteger>()

    /**
     * 异常处理结果
     */
    data class ExceptionResult(
        val errorCode: Int,
        val errorMessage: String,
        val shouldRetry: Boolean = false,
        val retryDelayMs: Long = 0L,
        val isFatal: Boolean = false
    )

    /**
     * 创建协程异常处理器
     * @param moduleName 模块名称，用于日志标识
     * @param customHandler 自定义异常处理逻辑，可选
     */
    fun createCoroutineExceptionHandler(
        moduleName: String,
        customHandler: ((Throwable) -> Unit)? = null
    ): CoroutineExceptionHandler {
        return CoroutineExceptionHandler { _, exception ->
            val tag = "CoroutineException[$moduleName]"
            
            // 记录异常统计
            recordException(exception)
            
            // 执行自定义处理逻辑
            customHandler?.invoke(exception)
            
            // 根据异常类型进行不同的处理
            when (exception) {
                is CancellationException -> {
                    AILog.d(tag, "Coroutine cancelled: ${exception.message}")
                }
                is TimeoutCancellationException -> {
                    AILog.w(tag, "Coroutine timeout: ${exception.message}")
                }
                is IOException -> {
                    AILog.e(tag, "IO exception in coroutine", exception)
                }
                is SecurityException -> {
                    AILog.e(tag, "Security exception in coroutine", exception)
                }
                is IllegalStateException -> {
                    AILog.e(tag, "Illegal state in coroutine", exception)
                }
                else -> {
                    AILog.e(tag, "Uncaught exception in coroutine", exception)
                }
            }
        }
    }

    /**
     * 处理任务执行异常
     */
    fun handleTaskException(taskId: String, exception: Throwable): ExceptionResult {
        recordException(exception)
        
        return when (exception) {
            is TimeoutCancellationException -> {
                AILog.w(TAG, "Task $taskId timeout: ${exception.message}")
                ExceptionResult(
                    errorCode = 1001, // PROCESSING_TIMEOUT
                    errorMessage = "任务执行超时",
                    shouldRetry = true,
                    retryDelayMs = 1000L
                )
            }
            
            is CancellationException -> {
                AILog.i(TAG, "Task $taskId cancelled: ${exception.message}")
                ExceptionResult(
                    errorCode = 1007, // TASK_CANCELLED
                    errorMessage = "任务已取消"
                )
            }
            
            is IOException -> {
                AILog.e(TAG, "Task $taskId IO error", exception)
                ExceptionResult(
                    errorCode = 1002, // IO_ERROR
                    errorMessage = "IO操作失败: ${exception.message}",
                    shouldRetry = true,
                    retryDelayMs = 2000L
                )
            }
            
            is SecurityException -> {
                AILog.e(TAG, "Task $taskId security error", exception)
                ExceptionResult(
                    errorCode = 1003, // SECURITY_ERROR
                    errorMessage = "权限不足: ${exception.message}",
                    isFatal = true
                )
            }
            
            is IllegalStateException -> {
                AILog.e(TAG, "Task $taskId illegal state", exception)
                ExceptionResult(
                    errorCode = 1004, // ILLEGAL_STATE
                    errorMessage = "状态异常: ${exception.message}",
                    shouldRetry = true,
                    retryDelayMs = 1500L
                )
            }
            
            else -> {
                AILog.e(TAG, "Task $taskId unknown error", exception)
                ExceptionResult(
                    errorCode = 1000, // UNKNOWN_ERROR
                    errorMessage = "未知错误: ${exception.message ?: "无详细信息"}",
                    shouldRetry = false
                )
            }
        }
    }

    /**
     * 处理组件初始化异常
     */
    fun handleInitializationException(component: String, exception: Throwable): ExceptionResult {
        recordException(exception)
        
        return when (exception) {
            is SecurityException -> {
                AILog.e(TAG, "$component security error", exception)
                ExceptionResult(
                    errorCode = 1003, // SECURITY_ERROR
                    errorMessage = "权限不足: ${exception.message}",
                    isFatal = true
                )
            }
            
            is IllegalStateException -> {
                AILog.e(TAG, "$component illegal state", exception)
                ExceptionResult(
                    errorCode = 1004, // ILLEGAL_STATE
                    errorMessage = "服务状态异常: ${exception.message}",
                    shouldRetry = true,
                    retryDelayMs = 2000L
                )
            }
            
            else -> {
                AILog.e(TAG, "$component init error", exception)
                ExceptionResult(
                    errorCode = 1000, // UNKNOWN_ERROR
                    errorMessage = "初始化失败: ${exception.message ?: "未知错误"}",
                    shouldRetry = true,
                    retryDelayMs = 3000L
                )
            }
        }
    }

    /**
     * 记录异常统计
     */
    private fun recordException(exception: Throwable) {
        val exceptionType = exception::class.java.simpleName
        exceptionStats.computeIfAbsent(exceptionType) { AtomicInteger(0) }.incrementAndGet()
    }

    /**
     * 获取异常统计信息
     */
    fun getExceptionStats(): Map<String, Int> {
        return exceptionStats.mapValues { it.value.get() }
    }

    /**
     * 清除异常统计
     */
    fun clearExceptionStats() {
        exceptionStats.clear()
        AILog.d(TAG, "Exception statistics cleared")
    }

    /**
     * 获取格式化的异常统计报告
     */
    fun getFormattedExceptionReport(): String {
        val stats = getExceptionStats()
        if (stats.isEmpty()) {
            return "No exceptions recorded"
        }
        
        val totalExceptions = stats.values.sum()
        val report = StringBuilder()
        report.append("Exception Statistics (Total: $totalExceptions):\n")
        
        stats.entries.sortedByDescending { it.value }.forEach { (type, count) ->
            val percentage = (count * 100.0 / totalExceptions).let { "%.1f".format(it) }
            report.append("  $type: $count ($percentage%)\n")
        }
        
        return report.toString()
    }
}
