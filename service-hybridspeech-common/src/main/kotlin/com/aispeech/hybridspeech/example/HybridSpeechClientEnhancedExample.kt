package com.aispeech.hybridspeech.example

import android.content.Context
import android.util.Log
import com.aispeech.hybridspeech.*
import kotlinx.coroutines.*

/**
 * HybridSpeechClientEnhanced 使用示例
 * 
 * 展示如何使用增强版客户端进行录音和转写操作
 */
class HybridSpeechClientEnhancedExample(private val context: Context) {
    
    companion object {
        private const val TAG = "HybridSpeechClientEnhancedExample"
    }
    
    private val client = HybridSpeechClientEnhanced(context)
    private val exampleScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    /**
     * 基本使用示例
     */
    fun basicUsageExample() {
        exampleScope.launch {
            try {
                // 1. 连接服务
                Log.d(TAG, "正在连接服务...")
                val connected = client.connectAndWait()
                if (!connected) {
                    Log.e(TAG, "服务连接失败")
                    return@launch
                }
                Log.d(TAG, "服务连接成功")
                
                // 2. 注册回调
                setupCallbacks()
                
                // 3. 开始录音（在线模式）
                Log.d(TAG, "开始在线录音...")
                val startResult = client.startRecordingWithBusinessParams(
                    recordId = System.currentTimeMillis(),
                    userId = "user123",
                    pcmFilePath = "/path/to/recording.pcm",
                    mp3FilePath = "/path/to/recording.mp3",
                    language = "cn",
                    useOnlineMode = true
                )
                
                when (startResult) {
                    is HybridSpeechClientEnhanced.TaskResult.Success -> {
                        Log.d(TAG, "录音开始成功")
                        
                        // 4. 模拟录音一段时间
                        delay(10000) // 录音10秒
                        
                        // 5. 停止录音
                        Log.d(TAG, "停止录音...")
                        val stopResult = client.stopRecordingWithResult()
                        when (stopResult) {
                            is HybridSpeechClientEnhanced.TaskResult.Success -> {
                                Log.d(TAG, "录音停止成功，结果: ${stopResult.data}")
                            }
                            is HybridSpeechClientEnhanced.TaskResult.Error -> {
                                Log.e(TAG, "录音停止失败: ${stopResult.errorMessage}")
                            }
                        }
                    }
                    is HybridSpeechClientEnhanced.TaskResult.Error -> {
                        Log.e(TAG, "录音开始失败: ${startResult.errorMessage}")
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "录音过程中发生异常", e)
            } finally {
                // 6. 清理资源
                client.cleanup()
                Log.d(TAG, "资源清理完成")
            }
        }
    }
    
    /**
     * 离线模式录音示例
     */
    fun offlineRecordingExample() {
        exampleScope.launch {
            try {
                // 连接服务
                if (!client.connectAndWait()) {
                    Log.e(TAG, "服务连接失败")
                    return@launch
                }
                
                // 注册回调
                setupCallbacks()
                
                // 开始离线录音
                Log.d(TAG, "开始离线录音...")
                val result = client.startRecordingWithBusinessParams(
                    recordId = System.currentTimeMillis(),
                    userId = "user123",
                    pcmFilePath = "/path/to/offline_recording.pcm",
                    mp3FilePath = "/path/to/offline_recording.mp3",
                    language = "cn",
                    translate = "en", // 翻译为英文
                    useOnlineMode = false, // 离线模式
                    enableTranslation = true,
                    offlineModelPath = "/path/to/offline/model"
                )
                
                when (result) {
                    is HybridSpeechClientEnhanced.TaskResult.Success -> {
                        Log.d(TAG, "离线录音开始成功")
                        
                        // 监控录音进度
                        monitorRecordingProgress()
                        
                        // 模拟录音
                        delay(15000) // 录音15秒
                        
                        // 停止录音
                        val stopResult = client.stopRecordingWithResult()
                        Log.d(TAG, "离线录音结果: $stopResult")
                    }
                    is HybridSpeechClientEnhanced.TaskResult.Error -> {
                        Log.e(TAG, "离线录音开始失败: ${result.errorMessage}")
                    }
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "离线录音过程中发生异常", e)
            } finally {
                client.cleanup()
            }
        }
    }
    
    /**
     * 暂停和恢复录音示例
     */
    fun pauseResumeExample() {
        exampleScope.launch {
            try {
                if (!client.connectAndWait()) return@launch
                
                setupCallbacks()
                
                // 开始录音
                val startResult = client.startRecordingWithBusinessParams(
                    recordId = System.currentTimeMillis(),
                    userId = "user123",
                    pcmFilePath = "/path/to/pause_resume.pcm",
                    mp3FilePath = "/path/to/pause_resume.mp3"
                )
                
                if (startResult is HybridSpeechClientEnhanced.TaskResult.Success) {
                    Log.d(TAG, "录音开始，5秒后暂停...")
                    delay(5000)
                    
                    // 暂停录音
                    val pauseResult = client.pauseRecording()
                    Log.d(TAG, "暂停结果: $pauseResult")
                    
                    delay(3000) // 暂停3秒
                    
                    // 恢复录音
                    val resumeResult = client.resumeRecording()
                    Log.d(TAG, "恢复结果: $resumeResult")
                    
                    delay(5000) // 再录音5秒
                    
                    // 停止录音
                    val stopResult = client.stopRecordingWithResult()
                    Log.d(TAG, "最终结果: $stopResult")
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "暂停恢复示例中发生异常", e)
            } finally {
                client.cleanup()
            }
        }
    }
    
    /**
     * 设置回调
     */
    private fun setupCallbacks() {
        // 注册转写回调
        val transcriptionCallback = object : ITranscriptionCallback.Stub() {
            override fun onTranscriptionResult(result: TranscriptionResult?) {
                Log.d(TAG, "转写结果: $result")
            }
            
            override fun onTranscriptionError(errorCode: Int, errorMessage: String?) {
                Log.e(TAG, "转写错误: $errorCode - $errorMessage")
            }
        }
        client.registerTranscriptionCallback(transcriptionCallback)
        
        // 注册配置提供者（如果需要）
        val configProvider = object : IHybridSpeechConfigProvider.Stub() {
            override fun requestNetworkConfig(
                request: NetworkConfigRequest?,
                callback: INetworkConfigCallback?
            ) {
                // 提供网络配置
                Log.d(TAG, "请求网络配置: $request")
                // 这里应该实现实际的配置逻辑
            }
        }
        client.registerConfigProvider(configProvider)
    }
    
    /**
     * 监控录音进度
     */
    private fun monitorRecordingProgress() {
        exampleScope.launch {
            client.createProgressFlow().collect { progressState ->
                when (progressState) {
                    is HybridSpeechClient.RecordingProgressState.Started -> {
                        Log.d(TAG, "录音已开始")
                    }
                    is HybridSpeechClient.RecordingProgressState.Progress -> {
                        Log.d(TAG, "录音进度: ${progressState.durationMs}ms")
                    }
                    is HybridSpeechClient.RecordingProgressState.Stopped -> {
                        Log.d(TAG, "录音已停止，总时长: ${progressState.totalDurationMs}ms")
                    }
                    is HybridSpeechClient.RecordingProgressState.Error -> {
                        Log.e(TAG, "录音进度错误: ${progressState.message}")
                    }
                }
            }
        }
    }
    
    /**
     * 清理资源
     */
    fun cleanup() {
        exampleScope.cancel()
        client.cleanup()
    }
}
