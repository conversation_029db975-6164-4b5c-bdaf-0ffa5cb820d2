# HybridSpeechServiceManager 到 HybridSpeechClientEnhanced 迁移指南

## 概述

本指南说明如何从 `HybridSpeechServiceManager` 迁移到新的 `HybridSpeechClientEnhanced`。新的客户端基于 ModelServiceClient 的设计模式，提供了更好的错误处理、统一的返回类型和更清晰的 API 设计。

## 主要变化

### 1. 类名变更
- **旧**: `HybridSpeechServiceManager`
- **新**: `HybridSpeechClientEnhanced`

### 2. 返回类型统一
- **旧**: 直接抛出异常或返回 null
- **新**: 使用 `TaskResult<T>` 封装结果

```kotlin
// 旧的方式
try {
    manager.startRecordingWithBusinessParams(...)
    // 成功
} catch (e: Exception) {
    // 处理错误
}

// 新的方式
when (val result = client.startRecordingWithBusinessParams(...)) {
    is TaskResult.Success -> {
        // 成功处理
    }
    is TaskResult.Error -> {
        // 错误处理: result.errorCode, result.errorMessage
    }
}
```

### 3. 架构改进
- **委托模式**: 新客户端委托给现有的 `HybridSpeechClient`，避免重复实现
- **统一错误处理**: 所有业务方法都返回 `TaskResult<T>`
- **保持兼容性**: 保留所有原有功能

## 迁移步骤

### 步骤 1: 更新依赖和导入

```kotlin
// 旧的导入
import com.aispeech.tablet.feature.meeting.viewmodel.HybridSpeechServiceManager

// 新的导入
import com.aispeech.hybridspeech.HybridSpeechClientEnhanced
```

### 步骤 2: 更新实例化代码

```kotlin
// 旧的方式
private val serviceManager = HybridSpeechServiceManager()

// 新的方式
private val speechClient = HybridSpeechClientEnhanced(context)
```

### 步骤 3: 更新方法调用

#### 连接服务
```kotlin
// 旧的方式
val connected = serviceManager.connectAndWait()

// 新的方式 (保持不变)
val connected = speechClient.connectAndWait()
```

#### 开始录音
```kotlin
// 旧的方式
try {
    serviceManager.startRecordingWithBusinessParams(
        recordId = recordId,
        userId = userId,
        pcmFilePath = pcmPath,
        mp3FilePath = mp3Path,
        language = "cn",
        useOnlineMode = true
    )
    // 处理成功
} catch (e: Exception) {
    // 处理错误
}

// 新的方式
when (val result = speechClient.startRecordingWithBusinessParams(
    recordId = recordId,
    userId = userId,
    pcmFilePath = pcmPath,
    mp3FilePath = mp3Path,
    language = "cn",
    useOnlineMode = true
)) {
    is HybridSpeechClientEnhanced.TaskResult.Success -> {
        // 处理成功
    }
    is HybridSpeechClientEnhanced.TaskResult.Error -> {
        // 处理错误: result.errorCode, result.errorMessage
    }
}
```

#### 停止录音
```kotlin
// 旧的方式
try {
    val result = serviceManager.stopRecording()
    // 处理结果
} catch (e: Exception) {
    // 处理错误
}

// 新的方式
when (val result = speechClient.stopRecordingWithResult()) {
    is HybridSpeechClientEnhanced.TaskResult.Success -> {
        val recordingInfo = result.data
        // 处理结果
    }
    is HybridSpeechClientEnhanced.TaskResult.Error -> {
        // 处理错误
    }
}
```

#### 暂停/恢复录音
```kotlin
// 旧的方式
try {
    serviceManager.pauseRecording()
    // 成功
} catch (e: Exception) {
    // 错误
}

// 新的方式
when (val result = speechClient.pauseRecording()) {
    is HybridSpeechClientEnhanced.TaskResult.Success -> {
        // 成功
    }
    is HybridSpeechClientEnhanced.TaskResult.Error -> {
        // 错误处理
    }
}
```

### 步骤 4: 更新回调注册

```kotlin
// 回调注册方式保持不变
speechClient.registerTranscriptionCallback(transcriptionCallback)
speechClient.registerConfigProvider(configProvider)

// 进度监控保持不变
speechClient.createProgressFlow().collect { progressState ->
    // 处理进度状态
}
```

### 步骤 5: 更新资源清理

```kotlin
// 旧的方式
serviceManager.cleanup()

// 新的方式 (保持不变)
speechClient.cleanup()
```

## 完整迁移示例

### 迁移前 (HybridSpeechServiceManager)

```kotlin
class OldRecordingManager(private val context: Context) {
    private val serviceManager = HybridSpeechServiceManager()
    
    suspend fun startRecording() {
        try {
            if (!serviceManager.connectAndWait()) {
                throw IllegalStateException("Failed to connect")
            }
            
            serviceManager.registerTranscriptionCallback(callback)
            serviceManager.startRecordingWithBusinessParams(
                recordId = 123L,
                userId = "user123",
                pcmFilePath = "/path/to/pcm",
                mp3FilePath = "/path/to/mp3"
            )
        } catch (e: Exception) {
            handleError(e)
        }
    }
    
    suspend fun stopRecording() {
        try {
            val result = serviceManager.stopRecording()
            handleResult(result)
        } catch (e: Exception) {
            handleError(e)
        }
    }
}
```

### 迁移后 (HybridSpeechClientEnhanced)

```kotlin
class NewRecordingManager(private val context: Context) {
    private val speechClient = HybridSpeechClientEnhanced(context)
    
    suspend fun startRecording() {
        if (!speechClient.connectAndWait()) {
            handleError(-1, "Failed to connect")
            return
        }
        
        speechClient.registerTranscriptionCallback(callback)
        
        when (val result = speechClient.startRecordingWithBusinessParams(
            recordId = 123L,
            userId = "user123",
            pcmFilePath = "/path/to/pcm",
            mp3FilePath = "/path/to/mp3"
        )) {
            is HybridSpeechClientEnhanced.TaskResult.Success -> {
                // 录音开始成功
            }
            is HybridSpeechClientEnhanced.TaskResult.Error -> {
                handleError(result.errorCode, result.errorMessage)
            }
        }
    }
    
    suspend fun stopRecording() {
        when (val result = speechClient.stopRecordingWithResult()) {
            is HybridSpeechClientEnhanced.TaskResult.Success -> {
                handleResult(result.data)
            }
            is HybridSpeechClientEnhanced.TaskResult.Error -> {
                handleError(result.errorCode, result.errorMessage)
            }
        }
    }
}
```

## 优势

1. **统一错误处理**: 所有方法都返回 `TaskResult<T>`，避免异常处理的复杂性
2. **更好的可测试性**: 结果类型明确，便于单元测试
3. **委托模式**: 复用现有的 `HybridSpeechClient`，减少代码重复
4. **向后兼容**: 保留所有原有功能和参数
5. **清晰的 API**: 参考 ModelServiceClient 的设计模式

## 注意事项

1. **错误处理**: 新的 API 不会抛出异常，需要检查 `TaskResult` 的类型
2. **导入路径**: 确保更新正确的导入路径
3. **测试**: 迁移后需要充分测试所有功能
4. **渐进迁移**: 可以逐步迁移，新旧代码可以共存

## 测试建议

1. 创建单元测试验证所有业务方法的返回类型
2. 测试错误场景，确保错误信息正确传递
3. 测试服务连接和断开逻辑
4. 验证回调注册和注销功能
5. 测试录音的完整流程（开始、暂停、恢复、停止）
