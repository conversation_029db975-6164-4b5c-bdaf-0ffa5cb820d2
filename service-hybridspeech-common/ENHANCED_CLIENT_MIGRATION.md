# HybridSpeechServiceManager 到增强版 HybridSpeechClient 迁移指南

## 概述

现在 `HybridSpeechClient` 已经完全集成了 `HybridSpeechServiceManager` 的所有业务逻辑，参考了 `ModelServiceClient` 的优秀设计模式，提供了纯粹的业务接口。

## 主要变化

### 1. 统一的客户端类
- **旧**: 使用 `HybridSpeechServiceManager`
- **新**: 使用增强版 `HybridSpeechClient`

### 2. 架构改进
- **纯粹的业务接口**: 隐藏所有连接管理细节
- **自动连接管理**: 无需手动连接服务
- **统一错误处理**: 使用 `TaskResult<T>` 封装
- **流程编排**: 参考 `ModelServiceClient` 的设计模式

### 3. 保持 API 兼容性
- 所有原有的方法签名都保持不变
- 新增了 `TaskResult<T>` 封装的版本，提供更好的错误处理
- 支持渐进式迁移

## 迁移步骤

### 步骤 1: 更新导入

```kotlin
// 旧的导入
import com.aispeech.tablet.feature.meeting.viewmodel.HybridSpeechServiceManager

// 新的导入
import com.aispeech.hybridspeech.HybridSpeechClient
```

### 步骤 2: 更新实例化代码

```kotlin
// 旧的方式
private val serviceManager = HybridSpeechServiceManager()

// 新的方式
private val speechClient = HybridSpeechClient(
    context = context,
    servicePackage = "com.aispeech.hybridspeech",
    serviceClass = "com.aispeech.hybridspeech.HybridSpeechService"
)
```

### 步骤 3: 方法调用保持不变

#### 连接服务
```kotlin
// 完全相同的 API
val connected = speechClient.connectAndWait()
```

#### 注册回调
```kotlin
// 完全相同的 API
speechClient.registerTranscriptionCallback(callback)
speechClient.registerConfigProvider(provider)
```

#### 开始录音
```kotlin
// 新的纯粹业务接口（推荐）- 自动处理连接管理
when (val result = speechClient.startRecordingWithBusinessParams(
    recordId = recordId,
    userId = userId,
    pcmFilePath = pcmPath,
    mp3FilePath = mp3Path,
    language = "cn",
    useOnlineMode = true
)) {
    is HybridSpeechClient.TaskResult.Success -> {
        // 录音开始成功，无需手动连接服务
    }
    is HybridSpeechClient.TaskResult.Error -> {
        // 统一的错误处理: result.errorCode, result.errorMessage
    }
}

// 兼容方式 1: 使用原有的异常处理方式（完全兼容）
try {
    // 需要先手动连接（兼容旧代码）
    if (!speechClient.connectAndWait()) {
        throw IllegalStateException("Failed to connect")
    }

    speechClient.startRecordingWithBusinessParams(
        recordId = recordId,
        userId = userId,
        pcmFilePath = pcmPath,
        mp3FilePath = mp3Path,
        language = "cn",
        useOnlineMode = true
    )
    // 处理成功
} catch (e: Exception) {
    // 处理错误
}
```

#### 停止录音
```kotlin
// 方式 1: 原有方式（完全兼容）
val result = speechClient.stopRecording() // 返回 RecordingResultInfo?

// 方式 2: 新的 TaskResult 方式（推荐）
when (val result = speechClient.stopRecordingWithResult()) {
    is HybridSpeechClient.TaskResult.Success -> {
        val recordingResult = result.data // RecordingResultInfo
        // 处理成功
    }
    is HybridSpeechClient.TaskResult.Error -> {
        // 处理错误
    }
}
```

#### 暂停/恢复录音
```kotlin
// 方式 1: 原有方式（完全兼容）
try {
    speechClient.pauseRecording()
    speechClient.resumeRecording()
} catch (e: Exception) {
    // 处理错误
}

// 方式 2: 新的 TaskResult 方式（推荐）
when (val result = speechClient.pauseRecording()) {
    is HybridSpeechClient.TaskResult.Success -> { /* 成功 */ }
    is HybridSpeechClient.TaskResult.Error -> { /* 错误 */ }
}
```

## 完整迁移示例

### 迁移前 (HybridSpeechServiceManager)

```kotlin
class OldRecordingManager(private val context: Context) {
    private val serviceManager = HybridSpeechServiceManager()
    
    suspend fun startRecording() {
        try {
            if (!serviceManager.connectAndWait()) {
                throw IllegalStateException("Failed to connect")
            }
            
            serviceManager.registerTranscriptionCallback(callback)
            serviceManager.startRecordingWithBusinessParams(
                recordId = 123L,
                userId = "user123",
                pcmFilePath = "/path/to/pcm",
                mp3FilePath = "/path/to/mp3"
            )
        } catch (e: Exception) {
            handleError(e)
        }
    }
    
    suspend fun stopRecording() {
        try {
            val result = serviceManager.stopRecording()
            handleResult(result)
        } catch (e: Exception) {
            handleError(e)
        }
    }
}
```

### 迁移后 (增强版 HybridSpeechClient)

```kotlin
class NewRecordingManager(private val context: Context) {
    private val speechClient = HybridSpeechClient(
        context = context,
        servicePackage = "com.aispeech.hybridspeech",
        serviceClass = "com.aispeech.hybridspeech.HybridSpeechService"
    )
    
    suspend fun startRecording() {
        if (!speechClient.connectAndWait()) {
            handleError(-1, "Failed to connect")
            return
        }
        
        speechClient.registerTranscriptionCallback(callback)
        
        // 使用新的 TaskResult 方式
        when (val result = speechClient.startRecordingWithBusinessParams(
            recordId = 123L,
            userId = "user123",
            pcmFilePath = "/path/to/pcm",
            mp3FilePath = "/path/to/mp3"
        )) {
            is HybridSpeechClient.TaskResult.Success -> {
                // 录音开始成功
            }
            is HybridSpeechClient.TaskResult.Error -> {
                handleError(result.errorCode, result.errorMessage)
            }
        }
    }
    
    suspend fun stopRecording() {
        when (val result = speechClient.stopRecordingWithResult()) {
            is HybridSpeechClient.TaskResult.Success -> {
                handleResult(result.data)
            }
            is HybridSpeechClient.TaskResult.Error -> {
                handleError(result.errorCode, result.errorMessage)
            }
        }
    }
}
```

## 优势

1. **统一的客户端**: 不再需要维护两个不同的客户端类
2. **更好的错误处理**: TaskResult 提供了统一的错误处理机制
3. **向后兼容**: 保持了所有原有的 API 接口
4. **集中管理**: 所有服务连接和业务逻辑都在一个类中
5. **简化依赖**: 减少了模块间的依赖关系

## 注意事项

1. 确保更新所有使用 `HybridSpeechServiceManager` 的地方
2. 推荐使用新的 `TaskResult` 方式进行错误处理
3. 原有的异常处理方式仍然有效，可以渐进式迁移
4. 服务包名和类名需要根据实际部署情况调整
